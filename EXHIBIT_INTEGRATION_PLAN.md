# Exhibit Integration Plan for AI Document Editor

## Overview

This document outlines the technical approach for integrating exhibits into the AI-driven document editor. Legal documents often reference exhibits, and our system needs to provide an intuitive way to insert, manage, and reference these exhibits within document sections.

## Current Exhibit Structure

Based on the existing models:

- `Exhibit` model is associated with a case file, draft, and document
- Exhibits have labels, descriptions, and sort orders
- Each exhibit is linked to a `Document` which contains the actual file

## Integration Requirements

1. Allow users to view available exhibits for the current case
2. Provide a way to insert exhibit references into document sections
3. Enable AI to suggest appropriate exhibit references
4. Maintain proper exhibit numbering and formatting
5. Update exhibit references when exhibits are added, removed, or reordered

## Phase 1: Exhibit Management Interface

### 1.1 Exhibit Sidebar Component

```php
// app/Livewire/Drafts/ExhibitSidebar.php
class ExhibitSidebar extends Component
{
    public $caseFileId;
    public $draftId;
    public $exhibits = [];
    public $showAddExhibitModal = false;
    
    protected $listeners = [
        'exhibitAdded' => 'refreshExhibits',
        'exhibitRemoved' => 'refreshExhibits',
        'exhibitUpdated' => 'refreshExhibits'
    ];
    
    public function mount($caseFileId, $draftId)
    {
        $this->caseFileId = $caseFileId;
        $this->draftId = $draftId;
        $this->refreshExhibits();
    }
    
    public function refreshExhibits()
    {
        $this->exhibits = Exhibit::where('case_file_id', $this->caseFileId)
            ->orderBy('sort_order')
            ->get();
    }
    
    public function insertExhibitReference($exhibitId)
    {
        $exhibit = Exhibit::find($exhibitId);
        if (!$exhibit) {
            return;
        }
        
        // Format the exhibit reference
        $reference = $this->formatExhibitReference($exhibit);
        
        // Emit event to insert the reference at cursor position
        $this->emit('insertTextAtCursor', $reference);
    }
    
    protected function formatExhibitReference($exhibit)
    {
        return "Exhibit {$exhibit->label}: {$exhibit->description}";
    }
    
    public function render()
    {
        return view('livewire.drafts.exhibit-sidebar');
    }
}
```

### 1.2 Exhibit Sidebar View

```html
<!-- resources/views/livewire/drafts/exhibit-sidebar.blade.php -->
<div class="bg-base-200 p-4 rounded-lg">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium">Exhibits</h3>
        <button 
            wire:click="$set('showAddExhibitModal', true)" 
            class="btn btn-sm btn-primary"
        >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add
        </button>
    </div>
    
    @if($exhibits->isEmpty())
        <div class="text-center py-4 text-base-content/70">
            <p>No exhibits added yet</p>
        </div>
    @else
        <div class="space-y-2 max-h-64 overflow-y-auto">
            @foreach($exhibits as $exhibit)
                <div class="bg-base-100 p-3 rounded-lg">
                    <div class="flex justify-between items-start">
                        <div>
                            <span class="font-medium">Exhibit {{ $exhibit->label }}</span>
                            <p class="text-sm text-base-content/70">{{ $exhibit->description }}</p>
                        </div>
                        <button 
                            wire:click="insertExhibitReference({{ $exhibit->id }})"
                            class="btn btn-xs btn-ghost"
                            title="Insert reference"
                        >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                            </svg>
                        </button>
                    </div>
                </div>
            @endforeach
        </div>
    @endif
    
    <!-- Add Exhibit Modal -->
    <x-modal wire:model="showAddExhibitModal">
        <x-slot name="title">Add New Exhibit</x-slot>
        <x-slot name="content">
            @livewire('exhibits.create-exhibit-form', ['caseFileId' => $caseFileId, 'draftId' => $draftId])
        </x-slot>
    </x-modal>
</div>
```

## Phase 2: Exhibit Reference Handling

### 2.1 Exhibit Reference Format

Define a consistent format for exhibit references:

```php
// app/Services/ExhibitService.php
class ExhibitService
{
    /**
     * Format an exhibit reference for insertion into a document
     */
    public function formatExhibitReference(Exhibit $exhibit, $format = 'standard')
    {
        switch ($format) {
            case 'standard':
                return "Exhibit {$exhibit->label}";
            
            case 'detailed':
                return "Exhibit {$exhibit->label}: {$exhibit->description}";
            
            case 'legal':
                return "Exhibit \"{$exhibit->label}\" attached hereto and incorporated herein by reference";
            
            case 'parenthetical':
                return "(See Exhibit {$exhibit->label})";
            
            default:
                return "Exhibit {$exhibit->label}";
        }
    }
    
    /**
     * Parse exhibit references from text
     */
    public function parseExhibitReferences($text)
    {
        $references = [];
        $pattern = '/Exhibit\s+([A-Z0-9]+)/i';
        
        if (preg_match_all($pattern, $text, $matches, PREG_OFFSET_CAPTURE)) {
            foreach ($matches[1] as $index => $match) {
                $references[] = [
                    'label' => $match[0],
                    'position' => $matches[0][$index][1],
                    'full_match' => $matches[0][$index][0]
                ];
            }
        }
        
        return $references;
    }
}
```

### 2.2 Integration with Document Editor

Update the AiDocumentEditor component to handle exhibit references:

```php
// app/Livewire/Drafts/AiDocumentEditor.php
class AiDocumentEditor extends Component
{
    // Existing properties...
    public $showExhibitSidebar = false;
    
    protected $listeners = [
        'insertTextAtCursor' => 'handleInsertTextAtCursor'
    ];
    
    // Existing methods...
    
    /**
     * Handle inserting text at cursor position
     */
    public function handleInsertTextAtCursor($text)
    {
        // This will be handled by JavaScript in the view
        // The event is emitted to the browser and caught by Alpine.js
    }
    
    /**
     * Get exhibits for the current case file
     */
    public function getExhibits()
    {
        return Exhibit::where('case_file_id', $this->caseFile->id)
            ->orderBy('sort_order')
            ->get();
    }
    
    /**
     * Toggle exhibit sidebar
     */
    public function toggleExhibitSidebar()
    {
        $this->showExhibitSidebar = !$this->showExhibitSidebar;
    }
    
    /**
     * Update document context with exhibits
     */
    protected function getDocumentContext()
    {
        $context = parent::getDocumentContext();
        
        // Add exhibits to context
        $context['exhibits'] = $this->getExhibits()->map(function($exhibit) {
            return [
                'id' => $exhibit->id,
                'label' => $exhibit->label,
                'description' => $exhibit->description
            ];
        })->toArray();
        
        return $context;
    }
}
```

### 2.3 JavaScript for Cursor Position Handling

```javascript
// resources/js/exhibit-integration.js
document.addEventListener('DOMContentLoaded', function() {
    // Listen for the insertTextAtCursor event from Livewire
    window.addEventListener('insertTextAtCursor', event => {
        const text = event.detail.text;
        const textarea = document.querySelector('#active-section-content');
        
        if (!textarea) return;
        
        // Get cursor position
        const startPos = textarea.selectionStart;
        const endPos = textarea.selectionEnd;
        
        // Insert text at cursor position
        const textBefore = textarea.value.substring(0, startPos);
        const textAfter = textarea.value.substring(endPos);
        
        // Update textarea value
        textarea.value = textBefore + text + textAfter;
        
        // Update Livewire model
        const livewireComponent = Livewire.find(
            textarea.closest('[wire\\:id]').getAttribute('wire:id')
        );
        
        if (livewireComponent) {
            livewireComponent.set('activeSectionContent', textarea.value);
        }
        
        // Set cursor position after inserted text
        textarea.focus();
        textarea.selectionStart = startPos + text.length;
        textarea.selectionEnd = startPos + text.length;
    });
});
```

## Phase 3: AI Integration for Exhibits

### 3.1 Exhibit-Aware Prompts

Update the DocumentPromptService to include exhibit awareness:

```php
// app/Services/DocumentPromptService.php
class DocumentPromptService
{
    // Existing methods...
    
    /**
     * Get a prompt for suggesting relevant exhibits
     */
    public function getExhibitSuggestionPrompt($sectionContent, $exhibits)
    {
        $prompt = "Based on the following section content, suggest which exhibits might be relevant to reference:\n\n";
        $prompt .= $sectionContent . "\n\n";
        
        $prompt .= "Available exhibits:\n";
        foreach ($exhibits as $exhibit) {
            $prompt .= "- Exhibit {$exhibit['label']}: {$exhibit['description']}\n";
        }
        
        $prompt .= "\nFor each relevant exhibit, explain why it should be referenced and suggest where in the text it could be mentioned.";
        
        return $prompt;
    }
    
    /**
     * Enhance section prompts with exhibit awareness
     */
    public function getSectionPrompt($sectionType, $action, $variables = [])
    {
        $prompt = parent::getSectionPrompt($sectionType, $action, $variables);
        
        // Add exhibit guidance if exhibits are available
        if (isset($variables['exhibits']) && !empty($variables['exhibits'])) {
            $prompt .= "\n\nThe following exhibits are available for reference:\n";
            
            foreach ($variables['exhibits'] as $exhibit) {
                $prompt .= "- Exhibit {$exhibit['label']}: {$exhibit['description']}\n";
            }
            
            $prompt .= "\nReference relevant exhibits where appropriate in your response.";
        }
        
        return $prompt;
    }
}
```

### 3.2 AI Exhibit Suggestions

Add a method to suggest relevant exhibits for the current section:

```php
// app/Livewire/Drafts/AiDocumentEditor.php
public function suggestRelevantExhibits()
{
    if (!$this->activeSectionId) {
        return;
    }
    
    // Find the active section
    $activeSection = null;
    foreach ($this->sections as $section) {
        if ($section['id'] === $this->activeSectionId) {
            $activeSection = $section;
            break;
        }
    }
    
    if (!$activeSection || empty($activeSection['content'])) {
        return;
    }
    
    // Get available exhibits
    $exhibits = $this->getExhibits()->toArray();
    
    if (empty($exhibits)) {
        $this->addSystemMessage("No exhibits are available for this case file.");
        return;
    }
    
    // Create a prompt for exhibit suggestions
    $prompt = $this->documentPromptService->getExhibitSuggestionPrompt(
        $activeSection['content'],
        $exhibits
    );
    
    // Add the prompt to the chat
    $this->chatMessages[] = [
        'role' => 'user',
        'content' => "Please suggest relevant exhibits for the current section.",
        'timestamp' => now()->format('g:i A')
    ];
    
    $this->isWaitingForResponse = true;
    
    // Send to AI service with the full prompt in context
    $context = $this->getDocumentContext();
    $context['_internal_prompt'] = $prompt; // This will be used by the service but not shown to the user
    
    $response = $this->documentAiService->generateContent(
        "Please suggest relevant exhibits for the current section.",
        $context
    );
    
    // Add the AI response to the chat
    $this->chatMessages[] = [
        'role' => 'assistant',
        'content' => $response,
        'timestamp' => now()->format('g:i A')
    ];
    
    $this->isWaitingForResponse = false;
}
```

## Phase 4: Exhibit Management UI

### 4.1 Create Exhibit Form

```php
// app/Livewire/Exhibits/CreateExhibitForm.php
class CreateExhibitForm extends Component
{
    public $caseFileId;
    public $draftId;
    public $label;
    public $description;
    public $documentId;
    public $dated;
    
    public $availableDocuments = [];
    
    protected $rules = [
        'label' => 'required|string|max:10',
        'description' => 'required|string|max:255',
        'documentId' => 'nullable|exists:documents,id',
        'dated' => 'nullable|date'
    ];
    
    public function mount($caseFileId, $draftId = null)
    {
        $this->caseFileId = $caseFileId;
        $this->draftId = $draftId;
        
        // Get next available exhibit label (A, B, C, etc.)
        $this->label = $this->getNextExhibitLabel();
        
        // Load available documents
        $this->loadAvailableDocuments();
    }
    
    protected function getNextExhibitLabel()
    {
        $lastExhibit = Exhibit::where('case_file_id', $this->caseFileId)
            ->orderBy('sort_order', 'desc')
            ->first();
            
        if (!$lastExhibit) {
            return 'A';
        }
        
        // If label is a letter, get next letter
        if (preg_match('/^[A-Z]$/', $lastExhibit->label)) {
            return chr(ord($lastExhibit->label) + 1);
        }
        
        // If label is a number, increment
        if (is_numeric($lastExhibit->label)) {
            return (int)$lastExhibit->label + 1;
        }
        
        // Default to next letter
        return 'A';
    }
    
    protected function loadAvailableDocuments()
    {
        $this->availableDocuments = Document::where('case_file_id', $this->caseFileId)
            ->whereDoesntHave('exhibit')
            ->get();
    }
    
    public function save()
    {
        $this->validate();
        
        // Get next sort order
        $maxSortOrder = Exhibit::where('case_file_id', $this->caseFileId)
            ->max('sort_order') ?? 0;
            
        // Create the exhibit
        $exhibit = Exhibit::create([
            'case_file_id' => $this->caseFileId,
            'draft_id' => $this->draftId,
            'document_id' => $this->documentId,
            'label' => $this->label,
            'description' => $this->description,
            'sort_order' => $maxSortOrder + 1,
            'dated' => $this->dated
        ]);
        
        // Reset form
        $this->reset(['label', 'description', 'documentId', 'dated']);
        $this->label = $this->getNextExhibitLabel();
        $this->loadAvailableDocuments();
        
        // Emit event to refresh exhibits
        $this->emit('exhibitAdded');
        $this->emit('closeModal');
    }
    
    public function render()
    {
        return view('livewire.exhibits.create-exhibit-form');
    }
}
```

### 4.2 Exhibit List Component

```php
// app/Livewire/Exhibits/ExhibitList.php
class ExhibitList extends Component
{
    public $caseFileId;
    public $exhibits = [];
    
    protected $listeners = [
        'exhibitAdded' => 'refreshExhibits',
        'exhibitRemoved' => 'refreshExhibits',
        'exhibitUpdated' => 'refreshExhibits',
        'exhibitReordered' => 'handleReorder'
    ];
    
    public function mount($caseFileId)
    {
        $this->caseFileId = $caseFileId;
        $this->refreshExhibits();
    }
    
    public function refreshExhibits()
    {
        $this->exhibits = Exhibit::where('case_file_id', $this->caseFileId)
            ->orderBy('sort_order')
            ->get();
    }
    
    public function removeExhibit($exhibitId)
    {
        $exhibit = Exhibit::find($exhibitId);
        if ($exhibit && $exhibit->case_file_id == $this->caseFileId) {
            $exhibit->delete();
            $this->refreshExhibits();
            $this->emit('exhibitRemoved');
        }
    }
    
    public function handleReorder($orderedIds)
    {
        foreach ($orderedIds as $index => $id) {
            Exhibit::where('id', $id)->update(['sort_order' => $index + 1]);
        }
        
        $this->refreshExhibits();
        $this->emit('exhibitReordered');
    }
    
    public function render()
    {
        return view('livewire.exhibits.exhibit-list');
    }
}
```

## Phase 5: Document Update on Exhibit Changes

### 5.1 Exhibit Reference Tracking

Create a service to track and update exhibit references:

```php
// app/Services/ExhibitReferenceService.php
class ExhibitReferenceService
{
    /**
     * Update exhibit references in a document when exhibits change
     */
    public function updateReferencesInDraft(Draft $draft)
    {
        // Get all sections
        $sections = $draft->sections_structure ?? [];
        if (empty($sections)) {
            return;
        }
        
        // Get current exhibits
        $exhibits = Exhibit::where('case_file_id', $draft->case_file_id)
            ->orderBy('sort_order')
            ->get()
            ->keyBy('label');
            
        $updated = false;
        
        // Process each section
        foreach ($sections as &$section) {
            if (empty($section['content'])) {
                continue;
            }
            
            // Find exhibit references
            $references = $this->findExhibitReferences($section['content']);
            
            if (empty($references)) {
                continue;
            }
            
            // Check each reference
            foreach ($references as $reference) {
                $label = $reference['label'];
                
                // If exhibit no longer exists or label changed
                if (!isset($exhibits[$label])) {
                    // Mark as potentially broken reference
                    $newContent = str_replace(
                        $reference['full_match'],
                        "[MISSING EXHIBIT: {$reference['full_match']}]",
                        $section['content']
                    );
                    
                    $section['content'] = $newContent;
                    $updated = true;
                }
            }
        }
        
        // Save updated sections if changes were made
        if ($updated) {
            $draft->update([
                'sections_structure' => $sections
            ]);
            
            return true;
        }
        
        return false;
    }
    
    /**
     * Find exhibit references in text
     */
    protected function findExhibitReferences($text)
    {
        $references = [];
        $pattern = '/Exhibit\s+([A-Z0-9]+)/i';
        
        if (preg_match_all($pattern, $text, $matches, PREG_OFFSET_CAPTURE)) {
            foreach ($matches[1] as $index => $match) {
                $references[] = [
                    'label' => $match[0],
                    'position' => $matches[0][$index][1],
                    'full_match' => $matches[0][$index][0]
                ];
            }
        }
        
        return $references;
    }
}
```

### 5.2 Exhibit Change Listeners

Create a listener to update documents when exhibits change:

```php
// app/Listeners/UpdateExhibitReferences.php
class UpdateExhibitReferences
{
    protected $exhibitReferenceService;
    
    public function __construct(ExhibitReferenceService $exhibitReferenceService)
    {
        $this->exhibitReferenceService = $exhibitReferenceService;
    }
    
    public function handle($event)
    {
        // Get the exhibit from the event
        $exhibit = $event->exhibit;
        
        // Get all drafts for this case file
        $drafts = Draft::where('case_file_id', $exhibit->case_file_id)->get();
        
        foreach ($drafts as $draft) {
            $this->exhibitReferenceService->updateReferencesInDraft($draft);
        }
    }
}
```

## Implementation Timeline

1. **Phase 1 (Week 1)**: Implement the exhibit sidebar and basic reference insertion
2. **Phase 2 (Week 2)**: Develop exhibit reference handling and formatting
3. **Phase 3 (Week 3)**: Integrate AI suggestions for exhibits
4. **Phase 4 (Week 4)**: Build the exhibit management UI
5. **Phase 5 (Week 5)**: Implement reference tracking and updates

## Technical Requirements

- Existing Exhibit and Document models
- Livewire for interactive components
- Alpine.js for frontend interactivity
- AI integration through AssistantChatService

## Considerations and Risks

1. **Reference Integrity**: Exhibit references may break if exhibits are renamed or removed
2. **Performance**: Parsing and updating references in large documents may be resource-intensive
3. **User Experience**: Need to balance automation with user control over exhibit references
4. **AI Accuracy**: AI suggestions for exhibits may not always be relevant or accurate

## Next Steps

1. Implement the exhibit sidebar component
2. Create the JavaScript for cursor position handling
3. Develop the exhibit reference service
4. Integrate with the AI document editor
5. Build the exhibit management UI
