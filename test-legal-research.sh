#!/bin/bash

# Make the script executable
chmod +x test-legal-research.sh

# Default values
API_URL="https://research.justicequest.pro/api/legal-research"
JURISDICTION=""
CASE_ID=""

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --url=*)
      API_URL="${1#*=}"
      shift
      ;;
    --jurisdiction=*)
      JURISDICTION="${1#*=}"
      shift
      ;;
    --case-id=*)
      CASE_ID="${1#*=}"
      shift
      ;;
    *)
      QUESTION="$1"
      shift
      ;;
  esac
done

# Build the command
CMD="php artisan test:legal-research"

if [ ! -z "$QUESTION" ]; then
  # Escape quotes in the question
  QUESTION=$(echo "$QUESTION" | sed 's/"/\\"/g')
  CMD="$CMD \"$QUESTION\""
fi

if [ ! -z "$JURISDICTION" ]; then
  CMD="$CMD --jurisdiction=\"$JURISDICTION\""
fi

if [ ! -z "$CASE_ID" ]; then
  CMD="$CMD --case-id=$CASE_ID"
fi

if [ ! -z "$API_URL" ]; then
  CMD="$CMD --api-url=\"$API_URL\""
fi

# Execute the command
eval $CMD
