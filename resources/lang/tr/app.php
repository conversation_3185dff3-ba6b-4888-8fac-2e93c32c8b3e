<?php

return [
    'new_case' => 'Yeni Durum',
    'create_new_case' => 'Yeni Durum Oluştur',
    'start_new_case' => 'Yeni Bir Do<PERSON>a Başlat',
    'search_cases' => 'Vaka ara...',
    'assistant_ready' => 'Asistan Hazır',
    'assistant_pending' => 'Asistan Beklemede',
    'delete_case_file' => 'Dosyayı Silmek İstiyor Musunuz?',
    'delete_confirmation' => 'Bu dosyayı silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
    'yes_delete_it' => 'Evet, sil!',
    'cancel' => 'İptal',
    'view_case' => 'Vaka Görüntüle',
    'delete' => 'Sil',
    'collaborators' => 'İşbirlikçileri',
    'collaboration_enabled' => 'İşbirliği Etkin',
    'view_all' => 'Tümünü Görüntüle',
    'view' => '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
    'enter_case_number_if_available' => 'Varsa vaka numarasını girin',
    'create_and_continue' => 'Oluştur ve Devam Et',
    'creating' => 'Oluşturuluyor...',
    'draft_documents' => 'Taslak Belgeler',
    'go_to_draft_dashboard' => 'Taslak Gösterge Paneline Git',
    'document_editor' => 'Belge Düzenleyici',
    'auto_save' => 'Otomatik Kaydet',
    'save' => 'Kaydet',
    'start_typing' => 'Buraya belgenizi yazmaya başlayın...',
    'back_to_case' => 'Vaka\'ya Dön',
    'new_draft' => 'Yeni Taslak',
    'no_drafts_yet' => 'Henüz taslak yok',
    'get_started_draft' => 'Yeni bir taslak belge oluşturarak başlayın.',
    'create_draft' => 'Taslak Oluştur',
    'type' => 'Tür',
    'description' => 'Açıklama',
    'status' => 'Durum',
    'created' => 'Oluşturuldu',
    'no_description' => 'Açıklama yok',
    'delete_draft_confirmation' => 'Bu taslağı silmek istediğinizden emin misiniz?',
    'caption_information' => 'Başlık Bilgisi',
    'court_information' => 'Mahkeme Bilgisi',
    'court_name' => 'Mahkeme Adı',
    'division' => 'Bölüm',
    'case_number' => 'Dosya Numarası',
    'judge_name' => 'Hakim Adı',
    'plaintiffs' => 'Davacılar',
    'add_plaintiff' => 'Davacı Ekle',
    'name' => 'İsim',
    'role' => 'Rol',
    'plaintiff' => 'Davacı',
    'petitioner' => 'Başvuran',
    'defendants' => 'Davalılar',
    'add_defendant' => 'Davalı Ekle',
    'defendant' => 'Sanık',
    'respondent' => 'Davalı',
    'document_title' => 'Belge Başlığı',
    'exhibits' => 'Ekler',
    'add_exhibit' => 'Eki Ekle',
    'no_exhibits_yet' => 'Henüz eklenmiş ek yok',
    'exhibit' => 'Eki',
    'standard' => 'Standart',
    'detailed' => 'Detaylı',
    'legal' => 'Hukuki',
    'parenthetical' => 'Parantez içi',
    'add_new_exhibit' => 'Yeni Sergi Ekle',
    'editor' => 'Editör',
    'last_saved_at' => 'Son kaydedilen saat',
    'back_to_drafts' => 'Taslaklara Dön',
    'ai_is_thinking' => 'Yapay Zeka düşünüyor',
    'ai_assistant' => 'Yapay Zeka Asistanı',
    'ai_welcome_message' => 'Merhaba! Hukuki belge taslağı hazırlamanızda size yardımcı olacak yapay zeka asistanınızım. Size şunlarda yardımcı olabilirim:',
    'generate_content_sections' => 'Belirli bölümler için içerik oluştur',
    'create_complete_draft' => 'Tam bir belge taslağı oluştur',
    'answer_legal_questions' => 'Yasal taslaklar hakkında soruları yanıtla',
    'provide_suggestions' => 'Belgenizi geliştirmek için önerilerde bulunun',
    'what_work_today' => 'Bugün ne üzerinde çalışmak istersiniz?',
    'you' => 'Sen',
    'thinking' => 'Düşünüyor',
    'generate_full_document' => 'Tam Belgeyi Oluştur',
    'generate_current_section' => 'Mevcut Bölümü Oluştur',
    'hide_exhibits' => 'Ekleri Gizle',
    'show_exhibits' => 'Ekleri Göster',
    'test_insert_text' => 'Metin Ekleme Testi',
    'ask_help_document' => 'Belgenizde yardım istemek için bana sorun...',
    'thinking_ellipsis' => 'Düşünüyor...',
    'enter_shift_newline' => 'Göndermek için Enter, yeni satır için Shift+Enter\'a basın',
    'save_section' => 'Bölümü Kaydet',
    'debug' => 'Hata Ayıklama:',
    'content_length' => 'İçerik uzunluğu:',
    'bytes' => 'bayt',
    'save_caption' => 'Başlığı Kaydet',
    'ai_suggestions' => 'Yapay Zeka Önerileri',
    'apply_to_section' => 'Bölüme Uygula',
    'insert' => 'Ekle',
    'dismiss' => 'Kapat',
    'select_section_edit' => 'Düzenlemek için bir bölüm seçin',
    'click_section_start' => 'Düzenlemeye başlamak için yukarıdaki bölüm adına tıklayın',
    'create_new_draft' => 'Yeni Taslak Oluştur',
    'use_template' => 'Şablon Kullan',
    'select_template_quick' => 'Hızlıca ön yapılandırılmış belge oluşturmak için bir şablon seçin.',
    'use_template_btn' => 'Şablon Kullan',
    'no_templates' => 'Mevcut şablon yok. Önce bir şablon oluşturun.',
    'create_template' => 'Şablon Oluştur',
    'create_from_scratch' => 'Sıfırdan Oluştur',
    'create_blank_document' => 'Şablon kullanmadan yeni boş belge oluştur',
    'document_type' => 'Belge Türü',
    'select_document_type' => 'Belge türünü seç',
    'complaint' => 'Şikayet',
    'motion' => 'İhtarname',
    'affidavit' => 'İspatname',
    'letter' => 'Mektup',
    'contract' => 'Sözleşme',
    'brief_description' => 'Bu belgenin kısa açıklaması',
    'draft' => 'Taslak',
    'draft_details' => 'Taslak Detayları',
    'back_to_draft' => 'Taslağa Dön',
    'edit_draft' => 'Taslağı Düzenle',
    'update_draft' => 'Taslağı Güncelle',
    'delete_draft' => 'Taslağı Sil',
    'open_editor' => 'Editörü Aç',
    'open_in_editor' => 'Editörde Aç',
    'no_description_provided' => 'Açıklama sağlanmadı',
    'last_updated' => 'Son Güncelleme',
    'published' => 'Yayınlandı',
    'document_metadata' => 'Belge Meta Verisi',
    'sections' => 'Bölümler',
    'structure' => 'Yapı',
    'metadata' => 'Meta Veri',
    'order' => 'Sıra',
    'section' => 'Bölüm',
    'required' => 'Gerekli',
    'optional' => 'İsteğe Bağlı',
    'content_preview' => 'İçerik Önizlemesi',
    'no_content' => 'İçerik yok',
    'no_sections_defined' => 'Tanımlı bölüm yok',
    'no_structure_defined' => 'Yapılandırma yok',
    'no_metadata_defined' => 'Meta veri yok',
    'actions' => 'İşlemler',
    'edit_properties' => 'Özellikleri Düzenle',
    'generate_document' => 'Belge Oluştur',
    'delete_draft_confirm' => 'Bu taslağı silmek istediğinizden emin misiniz?',
    'document_content' => 'Belge İçeriği',
    'basic_editor' => 'Temel Düzenleyici',
    'ai_powered_editor' => 'Yapay Zeka Destekli Düzenleyici',
    'download' => 'İndir',
    'review' => 'İnceleme'
];
