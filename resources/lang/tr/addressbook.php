<?php

return [
    'title' => '<PERSON><PERSON>fteri',
    'hide_form' => 'Formu Gizle',
    'new_contact' => 'Ye<PERSON>',
    'manual_entry' => '<PERSON>',
    'voice_input' => '<PERSON><PERSON>',
    'name' => 'İsim',
    'relationship' => 'İlişki',
    'address_line1' => 'Adres Satırı 1',
    'address_line2' => 'Adres Satırı 2',
    'city' => 'Şehir',
    'state' => 'İl',
    'zip_code' => 'Posta Kodu',
    'email' => 'E-posta',
    'phone' => 'Telefon',
    'search_placeholder' => 'İsim veya adresle iletişimleri ara...',
    'select_relationship' => 'İlişki Seç',
    'relationships' => [
        'select' => 'İlişki Seç',
        'attorney' => 'Avukat',
        'opposing_council' => 'Karşı Tarafın Avukatı',
        'next_friend' => '<PERSON><PERSON> Sonraki Arkadaş',
        'court' => 'Ma<PERSON><PERSON><PERSON>',
        'opponent' => 'Rakip',
        'neutral' => 'Tara<PERSON>ız',
        'self' => 'Kişi',
        'client' => 'Müvekkil',
        'opposing_party' => '<PERSON>rşı Taraf',
        'witness' => 'Tanık',
        'expert_witness' => 'Uzman Tanık',
        'judge' => 'Hakim',
        'opposing_counsel' => 'Karşı Avukat',
        'court_staff' => 'Mahkeme Personeli',
        'investigator' => 'Soruşturmacı',
        'mediator' => 'Arabulucu',
        'family_member' => 'Aile Üyesi',
        'guardian' => 'Vasi',
        'insurance_agent' => 'Sigorta Temsilcisi',
        'medical_provider' => 'Sağlık Sağlayıcı',
        'other' => 'Diğer'
    ],
    'name_relationship' => 'İsim & İlişki',
    'address' => 'Adres',
    'contact_information' => 'İletişim Bilgileri',
    'added' => 'Eklendi',
    'actions' => 'İşlemler',
    'edit_contact' => 'İletişimi Düzenle',
    'save_contact' => 'İletişimi Kaydet',
    'cancel' => 'İptal',
    'no_contacts' => 'Hiç iletişim bulunamadı',
    'add_contact_prompt' => 'Birini dizininize eklemek için \'Yeni İletişim\'e tıklayın',
    'confirm_deletion' => 'Silme Onayı',
    'delete_confirmation_message' => 'Bu iletişimi silmek istediğinizden emin misiniz? Bu işlem geri alınamaz.',
    'delete_contact' => 'İletişimi Sil',
    'voice_input_title' => 'Ses Girişi',
    'speaking_instructions_title' => 'Konuşma Talimatları',
    'speaking_instructions' => 'Doğal bir şekilde konuşun ve istediğiniz kadar isim ve adres listeleyin. Hatalar için endişelenmeyin, hepsini sonra size yardımcı olacağız.',
    'process_contacts' => 'İletişimleri İşle',
    'processing' => 'İşleniyor...',
    'contacts_processed' => ':count iletişim başarıyla işlendi',
    'voice_error' => 'İşlenecek metin yok',
    'validation' => [
        'name_required' => 'İsim gerekli',
        'address_required' => 'Adres gerekli',
        'city_required' => 'Şehir gerekli',
        'state_required' => 'İl gerekli',
        'zip_required' => 'Posta kodu gerekli',
        'email_invalid' => 'Lütfen geçerli bir e-posta adresi girin'
    ],
    'select_state' => 'İl Seç',
    'edit' => 'Düzenle',
    'delete' => 'Sil',
    'created_at' => 'Oluşturuldu',
    'contact_details' => 'İletişim Bilgileri',
    'voice_processing' => 'Ses girişleri işleniyor',
    'voice_processing_complete' => 'İşlem tamamlandı',
    'voice_processing_error' => 'Ses girişinde hata oluştu',
    'search_results' => 'Arama Sonuçları',
    'no_results' => 'Sonuç bulunamadı',
    'loading' => 'Yükleniyor...',
    'edit_mode' => 'Düzenleme Modu',
    'create_mode' => 'Oluşturma Modu',
    'select' => 'Seç',
    'viewing_case_parties' => 'Dava taraflarını görüntülüyorsunuz: :case',
    'errors' => [
        'edit_permission' => 'Bu kişiyle düzenleme izniniz yok',
        'delete_permission' => 'Bu kişiyi silme izniniz yok',
        'select_permission' => 'Bu kişiyi seçme izniniz yok'
    ]
];
