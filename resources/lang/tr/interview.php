<?php

return [
    'intro' => [
        'title' => 'Hukuki Görüşmeye Hoşgeldiniz',
        'purpose' => 'Bu g<PERSON>, hukuki durumunuzu anlamamıza ve uygun rehberlik sağlamamıza yardımcı olacaktır.',
        'process' => 'İşlem birkaç adımdan oluşur:',
        'step1' => 'Davayla ilgili temel bilgiler',
        'step2' => 'Taraflar hakkında detaylar',
        'step3' => 'Durumunuzun genel değerlendirmesi',
        'step4' => 'Davayla ilgili özel sorular',
        'step5' => 'İstediğiniz sonuç',
        'time_estimate' => 'Tamamlanması yaklaşık 10-15 dakika sürecektir.',
        'start_button' => 'Görüşmeye Başla'
    ],
    'case_identification' => [
        'title' => 'Dava Tanımlaması',
        'has_active_case' => 'Halen aktif bir davanız var mı?',
        'yes' => 'Evet',
        'no' => 'Hayır',
        'user_role' => 'Bu davadaki rolünüz nedir?',
        'plaintiff' => 'Davacı / Dilekçe Sahibi (Davayı ben başlattım)',
        'defendant' => 'Davalı / Cevap Veren (Dava bana karşı açıldı)',
        'case_number' => 'Dava numarası (biliniyorsa)',
        'alternative_questions' => 'Lütfen uygun olanları seçiniz:',
        'considering_legal_action' => 'Yasal işlem yapmayı düşünüyorum',
        'threatened_with_legal_action' => 'Hukuki işlemle tehdit edildim',
        'information_gathering' => 'Gelecekteki olası işlem için bilgi topluyorum'
    ],
    'parties' => [
        'title' => 'Taraflar',
        'description' => 'Lütfen davanızda yer alan tüm tarafları tanımlayın. Bu, kendinizi, karşı tarafları, avukatları ve diğer ilgili kişiler veya kuruluşları içerir.',
        'selected' => 'Seçilen Taraflar',
        'none_selected' => 'Henüz taraf seçilmedi. Lütfen aşağıdaki adres defterinizden tarafları seçin.'
    ],
    'navigation' => [
        'previous' => 'Geri',
        'next' => 'İleri',
        'finish' => 'Bitir',
        'submit' => 'Gönder',
        'back' => 'Geri',
        'save' => 'İlerlemenizi Kaydet'
    ],
    'validation' => [
        'select_case_status' => 'Lütfen aktif bir durumunuz olup olmadığını seçin.',
        'select_role' => 'Lütfen davadaki rolünüzü seçin.',
        'select_at_least_one' => 'Lütfen en az bir seçenek seçin.',
        'case_number_too_long' => 'Dava numarası 255 karakteri geçmemelidir.'
    ],
    'progress' => [
        'step' => 'Adım',
        'of' => 'kadar'
    ],
    'steps' => [
        'intro' => 'Giriş',
        'case_identification' => 'Dava Tanımlama',
        'parties' => 'Taraflar',
        'exhibits' => 'Delilleri Yükle',
        'overview' => 'Dava Özeti',
        'ai_questions' => 'Özel Detaylar',
        'outcome' => 'İstenen Sonuç',
        'summary' => 'Dava Özeti'
    ],
    'summary' => [
        'title' => 'Görüşme Tamamlandı',
        'subtitle' => 'Dava Analizi Tamamlandı',
        'description' => 'Görüşmeyi tamamladığınız için teşekkür ederiz. Sağladığınız bilgilere dayanarak, davanızın kapsamlı bir analizini oluşturduk.',
        'analysis_title' => 'Dava Özeti ve Analizi',
        'generating' => 'Vaka özeti hâlâ oluşturuluyor. Lütfen birkaç dakika sonra tekrar kontrol edin.',
        'next_steps_title' => 'Sonraki Adımlar',
        'next_steps' => [
            'review' => 'Yukarıdaki vaka özeti ve analizini inceleyin',
            'upload' => 'Vakanızla ilgili olabilecek ek belgeleri yükleyin',
            'consult' => 'Önerilen sonraki adımlar hakkında hukuk ekibinizle görüşün',
            'consider' => 'Belirlenen hukuki konuları göz önünde bulundurun ve olası stratejileri araştırın'
        ],
        'back' => 'Geri',
        'return' => 'Vaka Kontrol Paneline Dön'
    ],
    'relationships' => [
        'select' => 'İlişki Seç',
        'attorney' => 'Avukat',
        'opposing_council' => 'Karşı Taraf Mahkemesi',
        'next_friend' => 'Sonraki Arkadaş',
        'court' => 'Mahkeme',
        'opponent' => 'Rakip',
        'neutral' => 'Tarafsız',
        'self' => 'Kişi',
        'client' => 'Müvekkil',
        'opposing_party' => 'Karşı Taraf',
        'witness' => 'Tanık',
        'expert_witness' => 'Uzman Tanık',
        'judge' => 'Hakim',
        'opposing_counsel' => 'Karşı Avukat',
        'court_staff' => 'Mahkeme Personeli',
        'investigator' => 'Soruşturmacı',
        'mediator' => 'Arabulucu',
        'family_member' => 'Aile Üyesi',
        'guardian' => 'Vasi',
        'insurance_agent' => 'Sigorta Temsilcisi',
        'medical_provider' => 'Sağlık Hizmetleri Sağlayıcısı',
        'other' => 'Diğer'
    ],
    'exhibits' => [
        'title' => 'Belgeleri ve Kanıtları Yükle',
        'instructions_title' => 'Önemli: Hikayenizi anlatmadan önce kanıtlarınızı yükleyin',
        'upload_prompt' => 'Davanızla ilgili herhangi bir belge veya medya yükleyin (sözleşmeler, e-postalar, fotoğraflar, fişler, ses kayıtları, videolar vb.). Bir sonraki adımda durumunuzu anlatırken bu materyallere referans verebileceksiniz.',
        'no_exhibits' => 'Henüz belge yüklenmedi',
        'upload_complete' => 'Belgeler başarıyla yüklendi',
        'media_types' => 'Davanızı desteklemek için belgeler (PDF, DOC), görseller, ses kayıtları ve videolar yükleyebilirsiniz.',
        'untitled' => 'Başlıksız Belge',
        'no_description' => 'Açıklama sağlanmadı',
        'preview' => 'Belgeyi Önizle'
    ],
    'overview' => [
        'title' => 'Hikayenizi Anlatın',
        'instructions' => 'Olayların ne olduğunu kendi kelimelerinizle kısaca özetleyin. Aşağıdaki ses kaydı özelliğini kullanabilir veya yazabilirsiniz.',
        'speak_naturally' => 'Endişelenmeyin, hata yapmaktan korkmayın. Doğal bir şekilde konuşun ve yanlış söylerseniz kendinizi düzeltin. Sistemimiz doğal konuşma kalıplarını anlayabilir.',
        'prompt' => 'Hikayeniz:',
        'placeholder' => 'Olanları, ne zaman olduğunu ve kimlerin dahil olduğunu anlatın...',
        'narrative_prompt' => 'Durumunuzu detaylıca anlatın:',
        'narrative_placeholder' => 'Olanları, ne zaman olduğunu ve kimlerin dahil olduğunu anlatın...',
        'documents_prompt' => 'Destekleyici Belgeleri Yükleyin',
        'exhibit_instructions_title' => 'Devam etmeden önce Kanıtları Yükleyin',
        'exhibit_upload_prompt' => 'Davanızla ilgili herhangi bir belge veya medya yükleyin (sözleşmeler, e-postalar, fotoğraflar, makbuzlar, ses kayıtları, videolar vb.).',
        'referencing_exhibits_title' => 'Yüklediğiniz Belgeleri Referans Gösterin',
        'referencing_exhibits_prompt' => 'Hikayenizde, yüklediğiniz belgeleri isimleriyle belirterek referans verebilirsiniz.',
        'referencing_exhibits_example' => 'Örnek: "Ek A (Kira Sözleşmesi) gösterildiği gibi, bölüm 4 onarımların mal sahibinin sorumluluğunda olduğunu belirtmektedir. Ek B (Su Hasarı Fotoğrafları) ise sızıntıyı bildirdikten sonra durumu gösteriyor."',
        'exhibit_references' => 'Yüklediğiniz Belgeler',
        'exhibit_references_description' => 'Hikayenizde bu belgeleri referans gösterebilirsiniz. Önizleme yapmak için göz simgesine tıklayın.',
        'your_exhibits' => 'Yüklediğiniz Belgeler',
        'no_exhibits' => 'Henüz belge yüklenmedi',
        'no_exhibits_yet' => 'Henüz belge yüklenmedi. Destekleyici kanıt eklemek için yukarıdaki yükleyiciyi kullanın.',
        'exhibit_reference_tip' => 'İpucu: Davanızı güçlendirmek için bu belgeleri isimleriyle hikayenizde referans gösterin.',
        'analyze_prompt' => 'Devam etmeye hazır mısınız?',
        'analyze_description' => 'Bilgilerinizi analiz etmek ve bir sonraki adıma geçmek için aşağıdaki düğmeye tıklayın.',
        'analyze_button' => 'Analiz Et ve Devam Et',
        'analyzing' => 'Analiz Ediliyor...',
        'voice_input_button' => 'Ses Girişi Kullan',
        'ready_for_fact_gathering' => 'Gerçek Toplama İçin Hazır',
        'prepare_title' => 'Görüşmeye Hazırlık',
        'prepare_description' => 'Vaka görüşmenizin gerçek toplama bölümüne başlamak üzeresiniz. Yapay zeka yardımcımız, tüm gerekli detayları toplamak için size vaka hakkında belirli sorular soracak.',
        'important_info_title' => 'Önemli Bilgiler:',
        'important_info_save_progress' => 'Görüşme süreci biraz zaman alabilir, ancak ilerlemenizi kaydedip daha sonra devam edebilirsiniz.',
        'important_info_upload_documents' => 'İlerlemeden önce ilgili vaka belgelerini yükleyin - bu, yardımcının vaka durumunuzu daha iyi anlamasına yardımcı olur.',
        'important_info_specific_questions' => 'Vaka detaylarınız hakkında size belirli sorular sorulacak.',
        'important_info_comprehensive' => 'Cevaplarınız, durumunuz hakkında kapsamlı bir anlayış oluşturmak için kullanılacaktır.',
        'ready_prompt' => 'Başlamaya hazır olduğunuzda aşağıdaki butona tıklayın. İsterseniz bu görüşmeye daha sonra da geri dönebilirsiniz.',
        'begin_fact_gathering' => 'Gerçekleri Toplamaya Başla',
        'preparing_interview' => 'Görüşme Hazırlanıyor...'
    ],
    'ai_questions' => [
        'title' => 'Ek Sorular',
        'no_questions_yet' => 'Sağladığınız bilgilere dayanarak, durumunuzu daha iyi anlamak için bazı takip soruları oluşturabiliriz.',
        'generate_questions' => 'Soruları Oluştur',
        'generating' => 'Sorular Oluşturuluyor...',
        'question_count' => 'Soru :current / :total',
        'text_placeholder' => 'Buraya cevabınızı yazın...',
        'voice_placeholder' => 'Cevabınızı kaydedin veya buraya yazın...',
        'upload_document' => 'Belge Yükle',
        'cancel_upload' => 'Yüklemeyi İptal Et',
        'document_help' => 'Bu soruya cevap veren veya destekleyici kanıt sağlayan bir belge yükleyin.',
        'default_placeholder' => 'Cevabınızı buraya girin...',
        'save_answer' => 'Cevabı Kaydet',
        'saving' => 'Kaydediliyor...',
        'complete_interview' => 'Görüşmeyi Tamamla',
        'completing' => 'Tamamlanıyor...',
        'complete_help' => 'Tüm soruları cevaplamamış olsanız bile görüşmeyi tamamlayabilirsiniz. Daha sonra tekrar gelip daha fazla bilgi sağlayabilirsiniz.'
    ]
];
