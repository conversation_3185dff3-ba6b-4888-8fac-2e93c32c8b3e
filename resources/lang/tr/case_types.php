<?php

return [
    'Contract Law' => 'Sözleşmelerin kurulması, yorumlanması ve uygulanmasını içeren iki veya daha fazla taraf arasındaki anlaşmaları kapsar.',
    'Tort Law' => 'Başka bir kişiye zarar veya hasar veren medeni suçları ele alır ve olası sorumluluğu belirler.',
    'Property Law' => 'Mülkiyet hakkı ve sorumluluklarıyla ilgili haklar ve yükümlülükleri ilgilendirir.',
    'Family Law' => 'Aile içi hukuki ilişkileri, ev<PERSON><PERSON>, bo<PERSON><PERSON><PERSON>, velayet ve evlat edinmeyi düzenler.',
    'Criminal Law' => 'Kamuya karşı suç sayılan eylemlerle ilgilenir ve cezai yaptırımlar uygular.',
    'Employment Law' => 'İşverenler ve çalışanlar arasındaki ilişkiyi düzenler.',
    'Consumer Law' => 'Mal ve hizmet satın alan bireyleri haksız veya aldatıcı uygulamalardan korur.',
    'Constitutional Law' => 'Anayasa\'yı yorumlar ve uygular, hükümet yapısını tanımlar ve hakları korur.',
    'Administrative Law' => 'İdari kurumların eylem ve kararlarını denetler.',
    'Environmental Law' => 'İnsanların çevreyle etkileşimini düzenler ve doğal kaynakları korur.',
    'Immigration Law' => 'Yabancıların ülkeye giriş ve ikamet haklarıyla ilgilidir.',
    'Estate Planning' => 'Vefat sonrası varlıkların yönetimi ve dağıtımıyla, vasiyetler ve güvenler dahil olmak üzere ilgilidir.',
    'Bankruptcy Law' => 'Borçlarını ödeyemeyen bireyler ve işletmeler için yasal süreç sağlar.',
    'Intellectual Property' => 'İcatlar, edebi eserler ve markalar gibi zihnin ürünlerini korur.'
];
