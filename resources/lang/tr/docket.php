<?php

return [
    'navigation' => [
        'docket' => 'Döküman',
        'view_docket' => 'Dökümanı Görüntüle'
    ],
    'title' => 'Döküman',
    'dashboard' => [
        'title' => 'Döküman Girişleri'
    ],
    'search' => [
        'placeholder' => 'Döküman girişlerinde ara...'
    ],
    'filter' => [
        'all_types' => 'Tüm Giriş Türleri',
        'all_statuses' => 'Tüm Durumlar'
    ],
    'empty_state' => 'Döküman girişi bulunamadı',
    'actions' => [
        'create' => '<PERSON><PERSON><PERSON>',
        'edit' => 'Girişi Düzenle',
        'delete' => 'Gir<PERSON>yi Sil',
        'back_to_case' => 'Vaka\'ya Dön'
    ],
    'entry' => [
        'create_new' => 'Yeni Döküman Girdisi Oluştur',
        'create' => 'Girdi Oluştur',
        'fields' => [
            'date' => 'Tarih',
            'type' => 'Tür',
            'title' => 'Başlık',
            'description' => 'Açıklama',
            'filing_party' => 'Dava Açan Taraf',
            'judge' => 'Hakim',
            'docket_number' => 'Dava Numarası',
            'status' => 'Durum'
        ],
        'types' => [
            'filing' => 'Başvuru',
            'order' => 'Emir',
            'hearing' => 'Duruşma',
            'notice' => 'İhtar',
            'motion' => 'İstek',
            'judgment' => 'Karar',
            'other' => 'Diğer'
        ],
        'status' => [
            'pending' => 'Beklemede',
            'granted' => 'Verildi',
            'denied' => 'Reddedildi',
            'heard' => 'Dinlendi',
            'continued' => 'Devam ediyor',
            'withdrawn' => 'İptal edildi'
        ],
        'search_filing_party' => 'Dosya tarafını ara...',
        'no_parties_found' => 'Eşleşen taraf bulunamadı',
        'search_judge' => 'Hakimi ara...',
        'no_judges_found' => 'Eşleşen hakim bulunamadı',
        'upload_documents' => 'Belgeleri Yükle',
        'documents' => [
            'title' => 'İlgili Belgeler',
            'add' => 'Belge Ekle',
            'remove' => 'Belge Kaldır',
            'none_selected' => 'Hiç belge seçilmedi',
            'search' => 'Belgelerde ara...',
            'no_results' => 'Belge bulunamadı'
        ],
        'validation' => [
            'date_required' => 'Giriş tarihi zorunludur.',
            'type_required' => 'Giriş tipi zorunludur.',
            'title_required' => 'Giriş başlığı zorunludur.',
            'description_required' => 'Açıklama zorunludur.',
            'filing_party_required' => 'Dilekçe veren taraf gereklidir.',
            'judge_required' => 'Hakim gereklidir.'
        ],
        'actions' => [
            'save' => 'Giriş Kaydet',
            'cancel' => 'İptal',
            'clear_search' => 'Aramayı Temizle'
        ]
    ],
    'messages' => [
        'created' => 'Dilekçe kaydı başarıyla oluşturuldu.',
        'updated' => 'Dilekçe kaydı başarıyla güncellendi.',
        'deleted' => 'Dilekçe kaydı başarıyla silindi.'
    ],
    'filters' => [
        'date_range' => 'Tarih Aralığı',
        'type' => 'Giriş Türü',
        'status' => 'Durum',
        'search' => 'Dava girişlerini ara...',
        'apply' => 'Filtreleri Uygula',
        'clear' => 'Filtreleri Temizle'
    ]
];
