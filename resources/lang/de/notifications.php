<?php

return [
    'title' => 'Benachrichtigungen',
    'mark_all_read' => 'Alle als gelesen markieren',
    'mark_read' => 'Als gelesen markieren',
    'no_notifications' => 'Keine neuen Benachrichtigungen',
    'view_case' => 'Fall anzeigen',
    'new_notification' => 'Neue Benachrichtigung erhalten',
    'invite_received' => 'Sie wurden eingeladen, an Fall ":case" als :role zusammenzuarbeiten',
    'access_revoked' => 'Ihr Zugriff auf den Fall ":case" wurde entzogen',
    'role_changed' => 'Ihre Rolle für den Fall ":case" wurde auf :role geändert',
    'contacts_processed' => ':count Kontakte erfolgreich verarbeitet',
    'contacts_processing_failed' => 'Fehler beim Verarbeiten der Kontakte: :message',
    'research_items_generated' => ':count Forschungsgegenstände erstellt',
    'research_initiated' => 'Forschung gestartet für ":title"',
    'research_retry_initiated' => 'Wiederholungsversuch der Forschung gestartet für ":title"',
    'research_report_removed' => 'Forschungsbericht aus der KI-Wissensdatenbank entfernt',
    'research_report_removal_failed' => 'Fehler beim Entfernen des Forschungsberichts aus der KI-Wissensdatenbank',
    'research_already_in_progress' => 'Forschung läuft bereits für ":title"',
    'research_only_failed_retry' => 'Nur fehlgeschlagene Forschungen können wiederholt werden',
    'no_research_report' => 'Kein Forschungsbericht verfügbar',
    'no_markdown_content' => 'Kein Markdown-Inhalt verfügbar',
    'lawyer_review_thanks' => 'Vielen Dank für Ihr Interesse. Ein Anwalt kann Sie nach Abschluss des gesamten Interviewprozesses kontaktieren.',
    'party_added' => ':name wurde zu den Parteien des Falls hinzugefügt',
    'invitation_sent' => 'Einladung erfolgreich versendet.',
    'permissions_updated' => 'Berechtigungen erfolgreich aktualisiert.',
    'permissions_update_failed' => 'Aktualisierung der Berechtigungen fehlgeschlagen.',
    'error' => 'Fehler: :message'
];
