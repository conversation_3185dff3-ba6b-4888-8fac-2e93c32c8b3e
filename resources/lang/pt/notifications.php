<?php

return [
    'title' => 'Notificações',
    'mark_all_read' => 'Marcar tudo como lido',
    'mark_read' => 'Marcar como lido',
    'no_notifications' => 'Nenhuma notificação nova',
    'view_case' => 'Ver Caso',
    'new_notification' => 'Nova notificação recebida',
    'invite_received' => 'Você foi convidado a colaborar no caso ":case" como :role',
    'access_revoked' => 'Seu acesso ao caso ":case" foi revogado',
    'role_changed' => 'Seu papel no caso ":case" foi alterado para :role',
    'contacts_processed' => ':count contatos processados com sucesso',
    'contacts_processing_failed' => 'Falha ao processar contatos: :message',
    'research_items_generated' => ':count itens de pesquisa gerados',
    'research_initiated' => 'Pesquisa iniciada para ":title"',
    'research_retry_initiated' => 'Reinício da pesquisa iniciado para ":title"',
    'research_report_removed' => 'Relatório de pesquisa removido da base de conhecimento da IA',
    'research_report_removal_failed' => 'Falha ao remover o relatório de pesquisa da base de conhecimento da IA',
    'research_already_in_progress' => 'A pesquisa já está em andamento para ":title"',
    'research_only_failed_retry' => 'Somente pesquisas com falha podem ser reexecutadas',
    'no_research_report' => 'Nenhum relatório de pesquisa disponível',
    'no_markdown_content' => 'Nenhum conteúdo markdown disponível',
    'lawyer_review_thanks' => 'Obrigado pelo seu interesse. Um advogado pode entrar em contato com você após completar todo o processo de entrevista.',
    'party_added' => ':name adicionado às partes do caso',
    'invitation_sent' => 'Convite enviado com sucesso.',
    'permissions_updated' => 'Permissões atualizadas com sucesso.',
    'permissions_update_failed' => 'Falha ao atualizar permissões.',
    'error' => 'Erro: :message'
];
