<?php

return [
    'interview_assistant' => '案例面试助手',
    'ai_assistant' => 'AI 助手',
    'interview' => '面试',
    'case' => '案例',
    'fact_gathering_interview' => '事实收集面试',
    'select_conversation_thread' => '选择对话线程',
    'new_conversation' => '新对话',
    'delete_thread' => '删除线程',
    'no_conversation_threads' => '还没有对话线程',
    'you' => '你',
    'thinking' => '思考中...',
    'start_new_conversation' => '开始新对话',
    'create_new_thread_prompt' => '创建新主题，与AI助手讨论此案例。',
    'title' => '标题',
    'title_placeholder' => '例如：案件策略讨论',
    'description_optional' => '描述（可选）',
    'description_placeholder' => '这次对话关于什么？',
    'category_optional' => '类别（可选）',
    'select_category' => '选择类别',
    'categories' => [
        'strategy' => '策略',
        'analysis' => '分析',
        'research' => '研究',
        'other' => '其他'
    ],
    'cancel' => '取消',
    'create' => '创建',
    'delete_conversation' => '删除对话',
    'delete_confirmation' => '您确定要删除此对话线程吗？此操作无法撤销。',
    'delete' => '删除',
    'thread_type' => '线程类型',
    'message_placeholder' => '在此输入您的消息...',
    'recording' => '录音中...',
    'transcribing' => '转录中...',
    'voice_note' => '语音笔记',
    'attach_documents' => '附加文件',
    'send' => '发送',
    'sending' => '发送中...',
    'select_documents' => '选择文件',
    'select_documents_description' => '选择要包含在消息中的文件。'
];
