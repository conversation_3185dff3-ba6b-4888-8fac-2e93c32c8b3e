<?php

return [
    'title' => '通知',
    'mark_all_read' => '全部标记为已读',
    'mark_read' => '标记为已读',
    'no_notifications' => '没有新通知',
    'view_case' => '查看案件',
    'new_notification' => '收到新通知',
    'invite_received' => '您已被邀请作为 :role 参与案件 ":case" 的协作',
    'access_revoked' => '您对案件 ":case" 的访问权限已被撤销',
    'role_changed' => '您在案件 ":case" 中的角色已变更为 :role',
    'contacts_processed' => ':count 个联系人已成功处理',
    'contacts_processing_failed' => '联系人处理失败：:message',
    'research_items_generated' => '已生成 :count 个研究项目',
    'research_initiated' => '已启动对 ":title" 的研究',
    'research_retry_initiated' => '已重新启动对 ":title" 的研究',
    'research_report_removed' => '研究报告已从 AI 知识库中移除',
    'research_report_removal_failed' => '无法从 AI 知识库中移除研究报告',
    'research_already_in_progress' => '对 ":title" 的研究已在进行中',
    'research_only_failed_retry' => '仅失败的研究可以重试',
    'no_research_report' => '暂无研究报告',
    'no_markdown_content' => '暂无 Markdown 内容',
    'lawyer_review_thanks' => '感谢您的关注。完成整个面试流程后，律师可能会与您联系。',
    'party_added' => ':name 已加入案件当事人',
    'invitation_sent' => '邀请已成功发送。',
    'permissions_updated' => '权限更新成功。',
    'permissions_update_failed' => '权限更新失败。',
    'error' => '错误：:message'
];
