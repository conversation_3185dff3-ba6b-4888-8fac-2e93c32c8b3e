<?php

return [
    'headers' => [
        'collaborators' => 'Case Collaborators',
        'current_collaborators' => 'Current Collaborators',
        'invite' => 'Invite Collaborator'
    ],
    'messages' => [
        'not_enabled' => 'Collaboration is not enabled for this case.',
        'max_reached' => 'Maximum number of collaborators reached',
        'invite_sent' => 'Invitation sent successfully',
        'already_collaborator' => 'This user is already a collaborator',
        'no_collaborators' => 'No collaborators yet.'
    ],
    'buttons' => [
        'enable' => 'Enable Collaboration',
        'invite' => 'Send Invitation',
        'remove' => 'Remove',
        'change_role' => 'Change Role'
    ],
    'roles' => [
        'viewer' => 'Viewer',
        'editor' => 'Editor',
        'manager' => 'Manager'
    ],
    'status' => [
        'active' => 'Active',
        'pending' => 'Pending',
        'revoked' => 'Revoked'
    ],
    'confirmations' => [
        'remove' => 'Are you sure you want to remove this collaborator?'
    ],
    'notifications' => [
        'enabled' => 'Collaboration has been enabled for this case',
        'invited' => 'Collaborator invited successfully',
        'removed' => 'Collaborator removed successfully',
        'role_updated' => 'Collaborator role updated successfully',
        'invitation_accepted' => 'Invitation accepted successfully',
        'invitation_declined' => 'Invitation declined',
    ],
    'labels' => [
        'email' => 'Email Address',
        'role' => 'Role',
        'added' => 'Added',
        'actions' => 'Actions'
    ],
    'invitations' => 'Invitations',
    'pending_invitations' => 'Pending Invitations',
    'role' => 'Role',
    'accept' => 'Accept',
    'decline' => 'Decline',
];
