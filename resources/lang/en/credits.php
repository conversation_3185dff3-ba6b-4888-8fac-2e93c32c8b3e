<?php

return [
    // General
    'credits' => 'Credits',
    'credit' => 'Credit',
    'credit_balance' => 'Credit Balance',
    'available_credits' => 'Available Credits',
    
    // Actions
    'buy_credits' => 'Buy Credits',
    'add_credits' => 'Add Credits',
    'use_credits' => 'Use Credits',
    'transfer_credits' => 'Transfer Credits',
    
    // History
    'credit_history' => 'Credit History',
    'transaction_history' => 'Transaction History',
    'transaction_type' => 'Transaction Type',
    'transaction_date' => 'Transaction Date',
    'transaction_amount' => 'Amount',
    'transaction_description' => 'Description',
    
    // Transaction types
    'purchase' => 'Purchase',
    'subscription' => 'Subscription',
    'payment' => 'Payment',
    'refund' => 'Refund',
    'transfer' => 'Transfer',
    'adjustment' => 'Adjustment',
    
    // Messages
    'credits_added' => 'Credits added successfully.',
    'credits_used' => 'Credits used successfully.',
    'credits_transferred' => 'Credits transferred successfully.',
    'insufficient_credits' => 'Insufficient credits.',
    
    // Errors
    'transaction_failed' => 'Transaction failed: :message',
];
