<?php

return [
    // General
    'subscription' => 'Subscription',
    'subscriptions' => 'Subscriptions',
    'manage_subscription' => 'Manage Subscription',
    
    // Plans
    'plans' => 'Plans',
    'plan' => 'Plan',
    'select_plan' => 'Select a Plan',
    'invalid_plan' => 'Invalid plan selected.',
    'unknown_plan' => 'Unknown Plan',
    
    // Status
    'active' => 'Active',
    'canceled' => 'Canceled',
    'pending' => 'Pending',
    
    // Actions
    'subscribe' => 'Subscribe',
    'cancel' => 'Cancel',
    'resume' => 'Resume',
    'update' => 'Update',
    
    // Payment
    'payment_method' => 'Payment Method',
    'update_payment_method' => 'Update Payment Method',
    
    // Success messages
    'subscription_updated' => 'Your subscription has been updated to :plan.',
    'subscription_created' => 'You have successfully subscribed to the :plan plan.',
    'subscription_cancelled' => 'Your subscription has been cancelled and will end at the end of your billing period.',
    'subscription_resumed' => 'Your subscription has been resumed.',
    'payment_method_updated' => 'Your payment method has been updated.',
    
    // Info messages
    'not_subscribed' => 'You are not currently subscribed to any plan.',
    'not_cancelled' => 'Your subscription is not cancelled.',
    
    // Error messages
    'subscription_failed' => 'Subscription failed: :message',
    'payment_update_failed' => 'Failed to update payment method: :message',
    
    // Credits
    'subscription_credits' => 'Subscription Credits',
    'new_subscription' => 'New subscription',
    'subscription_updated_credits' => 'Subscription updated',
    'subscription_created_credits' => 'subscription_created',
    'subscription_verified_credits' => 'subscription_verified',
];
