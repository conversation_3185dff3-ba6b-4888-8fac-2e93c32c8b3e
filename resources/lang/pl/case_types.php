<?php

return [
    'Contract Law' => 'Prawo umów: zajmuje się zawieraniem, interpretacją i egzekwowaniem umów między dwoma lub więcej stronami.',
    'Tort Law' => 'Prawo deliktów: odnosi się do cywilnych szkód wyrządzonych innym osobom, co może prowadzić do odpowiedzialności.',
    'Property Law' => 'Prawo własności: dotyczy praw i obowiązków związanych z posiadaniem, użytkowaniem i przenoszeniem własności.',
    'Family Law' => 'Prawo rodzinne: reguluje relacje prawne w rodzinach, w tym małżeń<PERSON>, rozwód, opiekę nad dziećmi i adopcję.',
    'Criminal Law' => 'Prawo karne: zajmuje się czynami uznawanymi za przestępstwa przeciwko społ<PERSON>, karanymi grzywnami lub więzieniem.',
    'Employment Law' => 'Prawo pracy: reguluje relacje między pracodawcami a pracownikami.',
    'Consumer Law' => 'Prawo konsumenckie: chroni osoby kupujące towary i usługi przed nieuczciwymi lub wprowadzającymi w błąd praktykami.',
    'Constitutional Law' => 'Prawo konstytucyjne: interpretuje i stosuje Konstytucję, określając strukturę rządu i chroniąc prawa obywateli.',
    'Administrative Law' => 'Prawo administracyjne: reguluje działania i decyzje agencji administracji rządowej.',
    'Environmental Law' => 'Prawo ochrony środowiska: reguluje interakcje człowieka ze środowiskiem w celu ochrony zasobów naturalnych.',
    'Immigration Law' => 'Zajmuje się prawami legalnymi obcokrajowców wjeżdżających i przebywających w kraju.',
    'Estate Planning' => 'Dotyczy zarządzania i dystrybucji majątku po śmierci, w tym testamentów i trustów.',
    'Bankruptcy Law' => 'Zapewnia procedury prawne dla osób i firm, które nie mogą spłacić swoich długów.',
    'Intellectual Property' => 'Chroni dzieła umysłu, takie jak wynalazki, utwory literackie i marki.'
];
