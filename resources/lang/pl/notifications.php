<?php

return [
    'title' => 'Powiadomienia',
    'mark_all_read' => 'Oznacz wszystkie jako przeczytane',
    'mark_read' => 'Oznacz jako przeczytane',
    'no_notifications' => 'Brak nowych powiadomień',
    'view_case' => 'Zobacz sprawę',
    'new_notification' => 'Otrzymano nowe powiadomienie',
    'invite_received' => 'Otrzymałeś zaproszenie do współpracy nad sprawą ":case" jako :role',
    'access_revoked' => 'Twój dostęp do sprawy ":case" został cofnięty',
    'role_changed' => 'Twoja rola w sprawie ":case" została zmieniona na :role',
    'contacts_processed' => ':count kontaktów pomyślnie przetworzono',
    'contacts_processing_failed' => 'Nie udało się przetworzyć kontaktów: :message',
    'research_items_generated' => ':count elementów badawczych wygenerowano',
    'research_initiated' => 'Rozpoczęto badanie dla ":title"',
    'research_retry_initiated' => 'Ponowne uruchomienie badania dla ":title"',
    'research_report_removed' => 'Raport badawczy usunięto z bazy wiedzy AI',
    'research_report_removal_failed' => 'Nie udało się usunąć raportu badawczego z bazy wiedzy AI',
    'research_already_in_progress' => 'Badanie jest już w toku dla ":title"',
    'research_only_failed_retry' => 'Można powtórzyć tylko nieudane badanie',
    'no_research_report' => 'Brak dostępnego raportu badawczego',
    'no_markdown_content' => 'Brak zawartości markdown',
    'lawyer_review_thanks' => 'Dziękujemy za zainteresowanie. Prawnik może się z Tobą skontaktować po ukończeniu całego procesu wywiadu.',
    'party_added' => ':name dodano do stron postępowania',
    'invitation_sent' => 'Zaproszenie wysłane pomyślnie.',
    'permissions_updated' => 'Uprawnienia zaktualizowane pomyślnie.',
    'permissions_update_failed' => 'Nie udało się zaktualizować uprawnień.',
    'error' => 'Błąd: :message'
];
