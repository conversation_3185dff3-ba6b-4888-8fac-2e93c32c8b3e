<?php

return [
    'title' => '通知',
    'mark_all_read' => 'すべて既読にする',
    'mark_read' => '既読にする',
    'no_notifications' => '新しい通知はありません',
    'view_case' => 'ケースを見る',
    'new_notification' => '新しい通知を受信しました',
    'invite_received' => 'ケース ":case" へのコラボ招待を受けました：役割は :role',
    'access_revoked' => 'ケース ":case" へのアクセス権が取り消されました',
    'role_changed' => 'ケース ":case" の役割が :role に変更されました',
    'contacts_processed' => ':count 件の連絡先を正常に処理しました',
    'contacts_processing_failed' => '連絡先の処理に失敗しました: :message',
    'research_items_generated' => ':count 件の調査項目が作成されました',
    'research_initiated' => '「:title」の調査を開始しました',
    'research_retry_initiated' => '「:title」の調査再試行を開始しました',
    'research_report_removed' => 'AI知識ベースから調査レポートを削除しました',
    'research_report_removal_failed' => 'AI知識ベースから調査レポートの削除に失敗しました',
    'research_already_in_progress' => '「:title」の調査は既に進行中です',
    'research_only_failed_retry' => '失敗した調査のみ再試行可能です',
    'no_research_report' => '調査レポートがありません',
    'no_markdown_content' => 'マークダウンコンテンツがありません',
    'lawyer_review_thanks' => 'ご関心ありがとうございます。面接プロセスを完了すると、弁護士から連絡がある場合があります。',
    'party_added' => ':nameが案件の関係者に追加されました',
    'invitation_sent' => '招待状が正常に送信されました。',
    'permissions_updated' => '権限が正常に更新されました。',
    'permissions_update_failed' => '権限の更新に失敗しました。',
    'error' => 'エラー: :message'
];
