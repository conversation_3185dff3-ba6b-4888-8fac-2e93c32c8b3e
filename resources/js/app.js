import './bootstrap';
import './tiptap-editor';
// Import Trix editor
import './trix-editor';
// Import Quill editor
import './quill-editor';
// Import EditorJS editor
import './editorjs-editor';
// Import editor format converters
import './editor-converters';

// Set user ID for Echo
window.userId = document.querySelector('meta[name="user-id"]')?.content;

// Theme handling
document.addEventListener('alpine:init', () => {
    Alpine.store('theme', {
        current: localStorage.getItem('theme') || 'light',

        init() {
            document.documentElement.setAttribute('data-theme', this.current);
        },

        setTheme(theme) {
            this.current = theme;
            localStorage.setItem('theme', theme);
            document.documentElement.setAttribute('data-theme', theme);
        }
    });

    // Initialize theme
    Alpine.store('theme').init();
});

// Global notification handler
window.showNotification = function(message, type = 'success') {
    if (window.Swal) {
        const Toast = Swal.mixin({
            toast: true,
            position: 'top-end',
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.addEventListener('mouseenter', Swal.stopTimer)
                toast.addEventListener('mouseleave', Swal.resumeTimer)
            }
        });

        Toast.fire({
            icon: type,
            title: message
        });
    } else {
        alert(message);
    }
};
