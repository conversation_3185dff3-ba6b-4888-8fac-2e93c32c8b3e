import axios from 'axios';
window.axios = axios;

window.axios.defaults.headers.common['X-Requested-With'] = 'XMLHttpRequest';

import Echo from 'laravel-echo';
import Pusher from 'pusher-js';

window.Pusher = Pusher;

// Initialize Echo immediately to ensure it's available for Livewire components
try {
    window.Echo = new Echo({
        broadcaster: 'reverb',
        key: 'jq_key',
        wsHost: "reverb.herd.test", // Use the same hostname as the current page
        wsPort: 8080, // Match the actual running port from the process list
        forceTLS: false,
        enabledTransports: ['ws', 'wss'],
        disableStats: true,
        authEndpoint: '/broadcasting/auth' // Make sure this endpoint exists
    });

    console.log('Echo initialized successfully');

    // Dispatch a custom event to notify components that Echo is ready
    document.dispatchEvent(new CustomEvent('echo:ready'));

    // Listen for notifications when document is ready
    document.addEventListener('DOMContentLoaded', () => {
        if (window.userId) {
            window.Echo.private(`App.Models.User.${window.userId}`)
                .notification((notification) => {
                    // This will trigger the Livewire component's handleNewNotification method
                    Livewire.dispatch('refresh-notifications');
                });
        }
    });
} catch (error) {
    console.error('Failed to initialize Echo:', error);
}

/**
 * Echo exposes an expressive API for subscribing to channels and listening
 * for events that are broadcast by Laravel. Echo and event broadcasting
 * allow your team to quickly build robust real-time web applications.
 */
