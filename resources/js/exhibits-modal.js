/**
 * Exhibits <PERSON><PERSON>ler
 *
 * This script handles the "Attach Exhibits" modal functionality for document generation.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Exhibits modal script loaded');

    // Elements
    const modal = document.getElementById('attach-exhibits-modal');
    const btnYesExhibits = document.getElementById('btn-yes-exhibits');
    const btnNoExhibits = document.getElementById('btn-no-exhibits');
    const btnGenerateWithExhibits = document.getElementById('btn-generate-with-exhibits');
    const btnCancel = document.getElementById('btn-cancel');
    const exhibitsListContainer = document.getElementById('exhibits-list-container');
    const exhibitsList = document.getElementById('exhibits-list');

    // Current active form
    let activeForm = null;

    // Check if all required elements exist
    if (!modal || !btnYesExhibits || !btnNoExhibits || !btnGenerateWithExhibits || !btnCancel || !exhibitsListContainer || !exhibitsList) {
        console.error('One or more required elements for the exhibits modal are missing');
        return;
    }

    // Find all document generation forms and attach event listeners
    const documentForms = document.querySelectorAll('form[action*="generate-document"]');
    console.log(`Found ${documentForms.length} document generation forms`);

    documentForms.forEach(form => {
        // Replace the form's submit button with our own button that shows the modal
        const submitButtons = form.querySelectorAll('button[type="submit"]');

        submitButtons.forEach(button => {
            // Create a wrapper for the button to maintain layout
            const wrapper = document.createElement('div');
            wrapper.className = button.parentElement.className;

            // Create a new button with the same appearance
            const newButton = document.createElement('button');
            newButton.type = 'button'; // Important: type="button" prevents form submission
            newButton.className = button.className;
            newButton.innerHTML = button.innerHTML;

            // Add click event to show modal
            newButton.addEventListener('click', function(e) {
                e.preventDefault();
                activeForm = form;
                showExhibitsModal();
            });

            // Replace the original button
            wrapper.appendChild(newButton);
            button.parentElement.replaceWith(wrapper);
        });
    });

    /**
     * Show the exhibits modal
     */
    function showExhibitsModal() {
        // Show the modal
        modal.classList.add('modal-open');

        // Reset the modal state
        exhibitsListContainer.classList.add('hidden');
        btnYesExhibits.classList.remove('hidden');
        btnNoExhibits.classList.remove('hidden');
        btnGenerateWithExhibits.classList.add('hidden');
        btnCancel.classList.add('hidden');
    }

    /**
     * Hide the exhibits modal
     */
    function hideExhibitsModal() {
        modal.classList.remove('modal-open');
    }

    /**
     * Load exhibits for the current case file
     */
    async function loadExhibits() {
        try {
            if (!activeForm) {
                throw new Error('No active form found');
            }

            // Get the case file ID from the form
            const caseFileId = activeForm.getAttribute('data-case-file-id');

            if (!caseFileId) {
                throw new Error('No case file ID found on the form');
            }

            console.log(`Loading exhibits for case file ID: ${caseFileId}`);

            // Fetch the exhibits
            const response = await fetch(`/api/case-files/${caseFileId}/exhibits`);

            if (!response.ok) {
                throw new Error(`Failed to load exhibits: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Exhibits data:', data);

            // Clear the exhibits list
            exhibitsList.innerHTML = '';

            // Add exhibits to the list
            if (data.exhibits && data.exhibits.length > 0) {
                data.exhibits.forEach(exhibit => {
                    exhibitsList.innerHTML += `
                        <tr>
                            <td>
                                <input type="checkbox" class="checkbox"
                                       name="selected_exhibits[]"
                                       value="${exhibit.id}"
                                       id="exhibit-${exhibit.id}">
                            </td>
                            <td>${exhibit.title || 'Untitled'}</td>
                            <td>${exhibit.document_type || 'Document'}</td>
                        </tr>
                    `;
                });
            } else {
                exhibitsList.innerHTML = `
                    <tr>
                        <td colspan="3" class="text-center py-4">
                            No exhibits found for this case.
                        </td>
                    </tr>
                `;
            }

            // Show the exhibits list
            exhibitsListContainer.classList.remove('hidden');
            btnYesExhibits.classList.add('hidden');
            btnNoExhibits.classList.add('hidden');
            btnGenerateWithExhibits.classList.remove('hidden');
            btnCancel.classList.remove('hidden');

        } catch (error) {
            console.error('Error loading exhibits:', error);
            alert('Failed to load exhibits: ' + error.message);
            hideExhibitsModal();
        }
    }

    /**
     * Generate document with selected exhibits
     */
    function generateDocumentWithExhibits() {
        if (!activeForm) {
            console.error('No active form found');
            hideExhibitsModal();
            return;
        }

        // Get selected exhibits
        const selectedExhibits = Array.from(
            document.querySelectorAll('input[name="selected_exhibits[]"]:checked')
        ).map(checkbox => checkbox.value);

        console.log('Selected exhibits:', selectedExhibits);

        // Add selected exhibits to the form
        let exhibitsInput = document.createElement('input');
        exhibitsInput.type = 'hidden';
        exhibitsInput.name = 'exhibits';
        exhibitsInput.value = JSON.stringify(selectedExhibits);
        activeForm.appendChild(exhibitsInput);

        // Hide the modal
        hideExhibitsModal();

        // Submit the form
        console.log('Submitting form with exhibits');
        activeForm.submit();
    }

    /**
     * Generate document without exhibits
     */
    function generateDocumentWithoutExhibits() {
        if (!activeForm) {
            console.error('No active form found');
            hideExhibitsModal();
            return;
        }

        // Hide the modal
        hideExhibitsModal();

        // Submit the form
        console.log('Submitting form without exhibits');
        activeForm.submit();
    }

    // Event Listeners
    btnYesExhibits.addEventListener('click', loadExhibits);
    btnNoExhibits.addEventListener('click', generateDocumentWithoutExhibits);
    btnGenerateWithExhibits.addEventListener('click', generateDocumentWithExhibits);
    btnCancel.addEventListener('click', hideExhibitsModal);

    // Expose the init function
    window.initExhibitsModal = initExhibitsModal;
});
