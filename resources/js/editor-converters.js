/**
 * Utility functions for converting between different editor formats
 */

/**
 * Convert EditorJS format to Quill Delta format
 * @param {Object} editorJSContent - Content in EditorJS format
 * @returns {Object} - Content in Quill Delta format
 */
window.convertEditorJSToQuill = function(editorJSContent) {
    // If it's a string, parse it
    if (typeof editorJSContent === 'string') {
        try {
            editorJSContent = JSON.parse(editorJSContent);
        } catch (e) {
            console.error('Error parsing EditorJS content:', e);
            return { ops: [{ insert: editorJSContent }] };
        }
    }

    // If it's not an object or doesn't have blocks, return simple text
    if (!editorJSContent || !editorJSContent.blocks || !Array.isArray(editorJSContent.blocks)) {
        const textContent = typeof editorJSContent === 'string' ? editorJSContent : JSON.stringify(editorJSContent);
        return { ops: [{ insert: textContent }] };
    }

    // Initialize Quill Delta
    const delta = { ops: [] };

    // Process each block
    editorJSContent.blocks.forEach(block => {
        const blockType = block.type || 'paragraph';
        const blockData = block.data || {};

        switch (blockType) {
            case 'header':
                // Get header text and level
                const headerText = blockData.text || '';
                const level = blockData.level || 2;
                
                // Add header to delta
                delta.ops.push({ 
                    insert: headerText + '\n',
                    attributes: { header: level }
                });
                break;

            case 'paragraph':
                // Get paragraph text
                const paragraphText = blockData.text || '';
                
                // Check if the text contains HTML
                if (paragraphText.includes('<') && paragraphText.includes('>')) {
                    // Process HTML content
                    const tempDiv = document.createElement('div');
                    tempDiv.innerHTML = paragraphText;
                    
                    // Get text content
                    const textContent = tempDiv.textContent || tempDiv.innerText || paragraphText;
                    delta.ops.push({ insert: textContent + '\n' });
                } else {
                    // Plain text paragraph
                    delta.ops.push({ insert: paragraphText + '\n' });
                }
                break;

            case 'list':
                // Get list items
                const listItems = blockData.items || [];
                const listStyle = blockData.style || 'unordered';
                
                // Add each list item
                listItems.forEach(item => {
                    delta.ops.push({ 
                        insert: item + '\n',
                        attributes: { 
                            list: listStyle === 'ordered' ? 'ordered' : 'bullet'
                        }
                    });
                });
                break;

            case 'quote':
                // Get quote text
                const quoteText = blockData.text || '';
                const caption = blockData.caption || '';
                
                // Add quote to delta
                delta.ops.push({ 
                    insert: quoteText + (caption ? '\n' + caption : '') + '\n',
                    attributes: { blockquote: true }
                });
                break;

            default:
                // For any other block type, just add the text
                let text = '';
                
                // Try to extract text from the block data
                if (blockData.text) {
                    text = blockData.text;
                } else if (typeof blockData === 'string') {
                    text = blockData;
                } else {
                    // If we can't find text, stringify the data
                    text = JSON.stringify(blockData);
                }
                
                delta.ops.push({ insert: text + '\n' });
                break;
        }
    });

    return delta;
};

/**
 * Convert Quill Delta format to EditorJS format
 * @param {Object} quillContent - Content in Quill Delta format
 * @returns {Object} - Content in EditorJS format
 */
window.convertQuillToEditorJS = function(quillContent) {
    // If it's a string, parse it
    if (typeof quillContent === 'string') {
        try {
            quillContent = JSON.parse(quillContent);
        } catch (e) {
            console.error('Error parsing Quill content:', e);
            return {
                time: new Date().getTime(),
                blocks: [
                    {
                        id: Date.now().toString(),
                        type: 'paragraph',
                        data: {
                            text: quillContent
                        }
                    }
                ],
                version: '2.22.2'
            };
        }
    }

    // If it's not an object or doesn't have ops, return simple text
    if (!quillContent || !quillContent.ops || !Array.isArray(quillContent.ops)) {
        const textContent = typeof quillContent === 'string' ? quillContent : JSON.stringify(quillContent);
        return {
            time: new Date().getTime(),
            blocks: [
                {
                    id: Date.now().toString(),
                    type: 'paragraph',
                    data: {
                        text: textContent
                    }
                }
            ],
            version: '2.22.2'
        };
    }

    // Initialize EditorJS structure
    const editorJS = {
        time: new Date().getTime(),
        blocks: [],
        version: '2.22.2'
    };

    // Process each operation
    let currentText = '';
    let currentAttributes = null;

    quillContent.ops.forEach(op => {
        if (op.insert) {
            const text = op.insert;
            const attributes = op.attributes || {};

            // Check if this is a newline
            if (text === '\n') {
                // Create a block with the accumulated text
                if (currentText) {
                    const block = createEditorJSBlock(currentText, currentAttributes);
                    editorJS.blocks.push(block);
                    currentText = '';
                    currentAttributes = null;
                }
            } else if (text.includes('\n')) {
                // Split by newline and create blocks
                const lines = text.split('\n');
                
                for (let i = 0; i < lines.length; i++) {
                    const line = lines[i];
                    
                    // Add to current text if not empty
                    if (line) {
                        currentText += line;
                    }
                    
                    // If this is not the last line or the text ends with newline
                    if (i < lines.length - 1 || text.endsWith('\n')) {
                        // Create a block with the accumulated text
                        if (currentText || i === lines.length - 1) {
                            const block = createEditorJSBlock(currentText, attributes);
                            editorJS.blocks.push(block);
                            currentText = '';
                        }
                    }
                }
                
                currentAttributes = attributes;
            } else {
                // Add to current text
                currentText += text;
                currentAttributes = attributes;
            }
        }
    });

    // Add any remaining text as a block
    if (currentText) {
        const block = createEditorJSBlock(currentText, currentAttributes);
        editorJS.blocks.push(block);
    }

    // If no blocks were created, add an empty paragraph
    if (editorJS.blocks.length === 0) {
        editorJS.blocks.push({
            id: Date.now().toString(),
            type: 'paragraph',
            data: {
                text: ''
            }
        });
    }

    return editorJS;
};

/**
 * Helper function to create an EditorJS block based on text and attributes
 */
function createEditorJSBlock(text, attributes) {
    attributes = attributes || {};
    
    // Generate a unique ID
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 5);
    
    // Determine block type based on attributes
    if (attributes.header) {
        return {
            id,
            type: 'header',
            data: {
                text,
                level: attributes.header
            }
        };
    } else if (attributes.list === 'bullet') {
        return {
            id,
            type: 'list',
            data: {
                style: 'unordered',
                items: [text]
            }
        };
    } else if (attributes.list === 'ordered') {
        return {
            id,
            type: 'list',
            data: {
                style: 'ordered',
                items: [text]
            }
        };
    } else if (attributes.blockquote) {
        return {
            id,
            type: 'quote',
            data: {
                text,
                caption: ''
            }
        };
    } else {
        // Default to paragraph
        return {
            id,
            type: 'paragraph',
            data: {
                text
            }
        };
    }
}
