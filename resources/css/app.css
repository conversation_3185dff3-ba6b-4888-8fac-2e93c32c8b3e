/* Import EditorJS styles */
@import 'editorjs.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

[x-cloak] {
    display: none !important;
}

/* Mobile optimizations for exhibit uploader */
@media (max-width: 640px) {
    .upload-pulse-ring {
        width: 60px;
        height: 60px;
    }

    /* Adjust padding for mobile */
    .exhibit-uploader-card {
        padding: 0.75rem !important;
    }

    /* Make inputs larger on mobile for better touch targets */
    .exhibit-uploader-card input,
    .exhibit-uploader-card textarea,
    .exhibit-uploader-card button {
        font-size: 16px !important; /* Prevents iOS zoom on focus */
    }
}

/* Document list mobile optimizations */
@media (max-width: 640px) {
    /* Improve document card readability on mobile */
    .card.bg-base-200 .card-body {
        padding: 0.75rem !important;
    }

    /* Ensure text wraps properly on small screens */
    .card.bg-base-200 h4 {
        max-width: 100%;
        word-break: break-word;
    }

    /* Make document description more readable */
    .card.bg-base-200 p {
        max-width: 100%;
        word-break: break-word;
    }

    /* Adjust button sizes for better touch targets */
    .card.bg-base-200 .btn-sm {
        height: 2.5rem;
        min-height: 2.5rem;
        font-size: 0.875rem;
    }
}

/* Custom prose styles for chat messages */
.chat-bubble .prose {
    font-size: 1rem; /* Adjust size as needed */
}

.chat-bubble .prose p {
    margin-top: 0.75em;
    margin-bottom: 0.75em;
}

.chat-bubble .prose code {
    font-size: 0.9em;
}

/* Private chat interface styles */
.chat-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    overflow: hidden;
}

#messages-container {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    scrollbar-width: thin;
}

/* Ensure chat bubbles don't overflow */
.chat-bubble {
    word-wrap: break-word;
    overflow-wrap: break-word;
    max-width: 80%;
}

/* Ensure the chat interface takes up the full height */
@media (min-height: 600px) {
    .h-\[calc\(80vh-4rem\)\] {
        height: calc(80vh - 4rem);
        min-height: 500px;
    }
}
