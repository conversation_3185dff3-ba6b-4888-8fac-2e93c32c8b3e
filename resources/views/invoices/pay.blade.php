<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100/50 dark:bg-neutral-focus/50 backdrop-blur-xl shadow-xl sm:rounded-lg p-8">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                    <h2 class="text-2xl font-bold">{{ __('invoices.pay_invoice') }} #{{ $invoice->invoice_number }}</h2>

                    <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-ghost btn-sm sm:btn-md">
                        {{ __('invoices.back_to_invoice') }}
                    </a>
                </div>

                @if ($errors->any())
                    <div class="alert alert-error mb-6">
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                    <div class="card bg-base-200 shadow-md">
                        <div class="card-body">
                            <h3 class="card-title text-xl">Invoice Summary</h3>

                            <div class="mt-4 space-y-4">
                                <div>
                                    <p class="text-sm text-base-content/70">From</p>
                                    <p class="text-lg">{{ $invoice->creator->name }}</p>
                                </div>

                                <div>
                                    <p class="text-sm text-base-content/70">Invoice Date</p>
                                    <p class="text-lg">{{ $invoice->issued_date->format('M j, Y') }}</p>
                                </div>

                                <div>
                                    <p class="text-sm text-base-content/70">Due Date</p>
                                    <p class="text-lg">{{ $invoice->due_date->format('M j, Y') }}</p>
                                </div>

                                <div>
                                    <p class="text-sm text-base-content/70">Total Amount</p>
                                    <p class="text-lg font-bold">{{ $invoice->formatted_total }}</p>
                                </div>

                                @if($invoice->status === 'partial')
                                    <div>
                                        <p class="text-sm text-base-content/70">Amount Paid</p>
                                        <p class="text-lg">${{ number_format($invoice->getTotalPaidAttribute() / 100, 2) }}</p>
                                    </div>

                                    <div>
                                        <p class="text-sm text-base-content/70">Balance Due</p>
                                        <p class="text-lg font-bold">${{ number_format($invoice->getRemainingBalanceAttribute() / 100, 2) }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="card bg-base-200 shadow-md">
                        <div class="card-body">
                            <h3 class="card-title text-xl">Payment Method</h3>

                            <div class="mt-4">
                                <div class="tabs tabs-boxed mb-6 flex flex-wrap">
                                    <a class="tab tab-active flex-grow sm:flex-grow-0" id="tab-credit-card">Credit Card</a>
                                    @if($hasCredits)
                                        <a class="tab flex-grow sm:flex-grow-0" id="tab-platform-credits">Platform Credits</a>
                                    @endif
                                </div>

                                <form id="payment-form" action="{{ route('invoices.process-payment', $invoice) }}" method="POST">
                                    @csrf
                                    <input type="hidden" name="payment_method" value="credit_card" id="payment-method-input">
                                    <input type="hidden" name="amount_cents" value="{{ $invoice->getRemainingBalanceAttribute() }}" id="amount-cents-input">

                                    <div id="credit-card-form">
                                        <div class="mb-4">
                                            <label for="card-element" class="block text-sm font-medium mb-2">
                                                Credit or debit card
                                            </label>
                                            <div id="card-element" class="p-3 border rounded-md bg-base-100">
                                                <!-- Stripe Card Element will be inserted here -->
                                            </div>
                                            <div id="card-errors" class="text-error text-sm mt-2" role="alert"></div>
                                        </div>
                                    </div>

                                    <div id="platform-credits-form" style="display: none;">
                                        <div class="alert alert-info mb-4">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                                            <span>You will use {{ number_format($invoice->getRemainingBalanceAttribute() / 100, 2) }} credits from your account balance.</span>
                                        </div>
                                    </div>

                                    <div class="form-control mt-6">
                                        <label class="label">
                                            <span class="label-text">Payment Amount</span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text">$</span>
                                            <input type="number" id="payment-amount" class="input input-bordered w-full" value="{{ number_format($invoice->getRemainingBalanceAttribute() / 100, 2) }}" min="0.01" max="{{ number_format($invoice->getRemainingBalanceAttribute() / 100, 2) }}" step="0.01" required>
                                        </div>
                                        <label class="label">
                                            <span class="label-text-alt">Enter the amount you want to pay. Maximum: ${{ number_format($invoice->getRemainingBalanceAttribute() / 100, 2) }}</span>
                                        </label>
                                    </div>

                                    <div class="flex justify-end mt-6">
                                        <button type="submit" id="submit-button" class="btn btn-primary btn-sm sm:btn-md">
                                            {{ __('invoices.pay_now') }}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            @if(isset($requiresAction) && $requiresAction && isset($paymentIntent))
            // Handle the case where additional action is required
            const stripe = Stripe('{{ env('STRIPE_KEY') }}');
            stripe.handleCardAction('{{ $paymentIntent->client_secret }}')
                .then(function(result) {
                    if (result.error) {
                        // Show error to customer
                        const errorElement = document.getElementById('card-errors');
                        errorElement.textContent = result.error.message;
                    } else {
                        // The card action has been handled
                        // The PaymentIntent can be confirmed again on the server
                        window.location.href = '{{ route('invoices.show', $invoice) }}';
                    }
                });
            @endif
            // Initialize Stripe
            const stripe = Stripe('{{ env('STRIPE_KEY') }}');
            const elements = stripe.elements();

            // Create card element
            const cardElement = elements.create('card');
            cardElement.mount('#card-element');

            // Handle form submission
            const form = document.getElementById('payment-form');
            const submitButton = document.getElementById('submit-button');
            const paymentMethodInput = document.getElementById('payment-method-input');
            const amountCentsInput = document.getElementById('amount-cents-input');
            const paymentAmountInput = document.getElementById('payment-amount');

            // Tab switching
            const creditCardTab = document.getElementById('tab-credit-card');
            const platformCreditsTab = document.getElementById('tab-platform-credits');
            const creditCardForm = document.getElementById('credit-card-form');
            const platformCreditsForm = document.getElementById('platform-credits-form');

            if (platformCreditsTab) {
                creditCardTab.addEventListener('click', function() {
                    creditCardTab.classList.add('tab-active');
                    platformCreditsTab.classList.remove('tab-active');
                    creditCardForm.style.display = 'block';
                    platformCreditsForm.style.display = 'none';
                    paymentMethodInput.value = 'credit_card';
                });

                platformCreditsTab.addEventListener('click', function() {
                    platformCreditsTab.classList.add('tab-active');
                    creditCardTab.classList.remove('tab-active');
                    platformCreditsForm.style.display = 'block';
                    creditCardForm.style.display = 'none';
                    paymentMethodInput.value = 'platform_credits';
                });
            }

            // Update amount cents when payment amount changes
            paymentAmountInput.addEventListener('input', function() {
                const amountDollars = parseFloat(paymentAmountInput.value);
                const amountCents = Math.round(amountDollars * 100);
                amountCentsInput.value = amountCents;
            });

            form.addEventListener('submit', async function(event) {
                event.preventDefault();

                // Disable the submit button to prevent multiple submissions
                submitButton.disabled = true;
                submitButton.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Processing...';

                const paymentMethod = paymentMethodInput.value;

                if (paymentMethod === 'credit_card') {
                    try {
                        // Create payment method
                        const {paymentMethod: stripePaymentMethod, error} = await stripe.createPaymentMethod({
                            type: 'card',
                            card: cardElement,
                        });

                        if (error) {
                            // Show error to customer
                            const errorElement = document.getElementById('card-errors');
                            errorElement.textContent = error.message;

                            // Re-enable the submit button
                            submitButton.disabled = false;
                            submitButton.innerHTML = 'Pay Now';
                            return;
                        }

                        // Add payment method ID to form
                        const hiddenInput = document.createElement('input');
                        hiddenInput.setAttribute('type', 'hidden');
                        hiddenInput.setAttribute('name', 'payment_method_id');
                        hiddenInput.setAttribute('value', stripePaymentMethod.id);
                        form.appendChild(hiddenInput);

                        // Check for URL parameters that indicate a redirect back from Stripe
                        const urlParams = new URLSearchParams(window.location.search);
                        const paymentIntentId = urlParams.get('payment_intent');
                        const paymentIntentClientSecret = urlParams.get('payment_intent_client_secret');

                        if (paymentIntentId && paymentIntentClientSecret) {
                            // Handle the redirect back from Stripe
                            const {error} = await stripe.retrievePaymentIntent(paymentIntentClientSecret);

                            if (error) {
                                const errorElement = document.getElementById('card-errors');
                                errorElement.textContent = error.message;

                                // Re-enable the submit button
                                submitButton.disabled = false;
                                submitButton.innerHTML = 'Pay Now';
                                return;
                            }
                        }
                    } catch (err) {
                        console.error('Error:', err);
                        const errorElement = document.getElementById('card-errors');
                        errorElement.textContent = 'An unexpected error occurred. Please try again.';

                        // Re-enable the submit button
                        submitButton.disabled = false;
                        submitButton.innerHTML = 'Pay Now';
                        return;
                    }
                }

                // Submit the form
                form.submit();
            });
        });
    </script>
    @endpush
</x-app-layout>
