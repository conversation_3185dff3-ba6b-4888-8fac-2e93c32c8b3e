<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100/50 dark:bg-neutral-focus/50 backdrop-blur-xl shadow-xl sm:rounded-lg p-8">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                    <h2 class="text-2xl font-bold">{{ __('invoices.create_invoice') }}</h2>

                    <a href="{{ route('invoices.index') }}" class="btn btn-ghost btn-sm sm:btn-md">
                        {{ __('invoices.back_to_invoices') }}
                    </a>
                </div>

                @if ($errors->any())
                    <div class="alert alert-error mb-6">
                        <ul>
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <form action="{{ route('invoices.store') }}" method="POST">
                    @csrf

                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                        <div class="card bg-base-200 shadow-md">
                            <div class="card-body">
                                <h3 class="card-title text-xl">{{ __('invoices.invoice_details') }}</h3>

                                <div class="form-control mt-4">
                                    <label class="label">
                                        <span class="label-text">{{ __('invoices.recipient') }}</span>
                                    </label>
                                    <select name="recipient_id" class="select select-bordered w-full" required>
                                        <option value="">{{ __('invoices.select_recipient') }}</option>
                                        @foreach($collaborators as $collaborator)
                                            <option value="{{ $collaborator->id }}" {{ (old('recipient_id') == $collaborator->id || (isset($selectedRecipient) && $selectedRecipient->id == $collaborator->id)) ? 'selected' : '' }}>
                                                {{ $collaborator->name }} ({{ $collaborator->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-control mt-4">
                                    <label class="label">
                                        <span class="label-text">{{ __('invoices.case') }} ({{ __('general.optional') }})</span>
                                    </label>
                                    <select name="case_file_id" class="select select-bordered w-full">
                                        <option value="">{{ __('invoices.select_case') }}</option>
                                        @foreach($caseFiles as $caseFile)
                                            <option value="{{ $caseFile->id }}" {{ (old('case_file_id') == $caseFile->id || (isset($selectedCaseFile) && $selectedCaseFile->id == $caseFile->id)) ? 'selected' : '' }}>
                                                {{ $caseFile->title }}
                                            </option>
                                        @endforeach
                                    </select>
                                </div>

                                <div class="form-control mt-4">
                                    <label class="label">
                                        <span class="label-text">{{ __('invoices.due_date') }}</span>
                                    </label>
                                    <input type="date" name="due_date" class="input input-bordered" value="{{ old('due_date', now()->addDays(30)->format('Y-m-d')) }}" required>
                                </div>

                                <div class="form-control mt-4">
                                    <label class="label">
                                        <span class="label-text">{{ __('invoices.notes') }}</span>
                                    </label>
                                    <textarea name="notes" class="textarea textarea-bordered h-24">{{ old('notes') }}</textarea>
                                </div>
                            </div>
                        </div>

                        <div class="card bg-base-200 shadow-md">
                            <div class="card-body">
                                <h3 class="card-title text-xl">{{ __('invoices.invoice_items') }}</h3>

                                <div id="invoice-items" class="mt-4 space-y-6">
                                    <div class="invoice-item border border-base-300 rounded-lg p-4">
                                        <div class="form-control">
                                            <label class="label">
                                                <span class="label-text">{{ __('invoices.description') }}</span>
                                            </label>
                                            <input type="text" name="items[0][description]" class="input input-bordered" value="{{ old('items.0.description') }}" required>
                                        </div>

                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
                                            <div class="form-control">
                                                <label class="label">
                                                    <span class="label-text">{{ __('invoices.quantity') }}</span>
                                                </label>
                                                <input type="number" name="items[0][quantity]" class="input input-bordered" value="{{ old('items.0.quantity', 1) }}" min="0.01" step="0.01" required>
                                            </div>

                                            <div class="form-control">
                                                <label class="label">
                                                    <span class="label-text">{{ __('invoices.unit_price') }}</span>
                                                </label>
                                                <input type="number" name="items[0][unit_price_display]" class="input input-bordered unit-price" value="{{ old('items.0.unit_price_display', old('items.0.unit_price_cents') ? old('items.0.unit_price_cents')/100 : 0) }}" min="0" step="0.01" required data-index="0">
                                                <input type="hidden" name="items[0][unit_price_cents]" class="unit-price-cents" value="{{ old('items.0.unit_price_cents', 0) }}">
                                            </div>
                                        </div>

                                        <div class="form-control mt-4">
                                            <label class="label">
                                                <span class="label-text">{{ __('invoices.tax_rate') }}</span>
                                            </label>
                                            <input type="number" name="items[0][tax_rate]" class="input input-bordered" value="{{ old('items.0.tax_rate', 0) }}" min="0" max="100" step="0.01">
                                        </div>

                                        <div class="flex justify-end mt-4">
                                            <button type="button" class="btn btn-sm btn-error remove-item" style="display: none;">
                                                {{ __('invoices.remove') }}
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                <div class="flex justify-center mt-6">
                                    <button type="button" id="add-item" class="btn btn-outline">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 3a1 1 0 00-1 1v5H4a1 1 0 100 2h5v5a1 1 0 102 0v-5h5a1 1 0 100-2h-5V4a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                        {{ __('invoices.add_item') }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="flex flex-wrap justify-end gap-3">
                        <button type="submit" name="send_now" value="0" class="btn btn-outline btn-sm sm:btn-md">
                            {{ __('invoices.save_as_draft') }}
                        </button>

                        <button type="submit" name="send_now" value="1" class="btn btn-primary btn-sm sm:btn-md">
                            {{ __('invoices.save_and_send') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const invoiceItems = document.getElementById('invoice-items');
            const addItemButton = document.getElementById('add-item');

            // Add item
            addItemButton.addEventListener('click', function() {
                const itemCount = invoiceItems.querySelectorAll('.invoice-item').length;
                const newIndex = itemCount;

                const newItem = document.createElement('div');
                newItem.className = 'invoice-item border border-base-300 rounded-lg p-4';
                newItem.innerHTML = `
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">${document.querySelector('span.label-text:contains("' + '{{ __('invoices.description') }}' + '")').textContent}</span>
                        </label>
                        <input type="text" name="items[${newIndex}][description]" class="input input-bordered" required>
                    </div>

                    <div class="grid grid-cols-2 gap-4 mt-4">
                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">${document.querySelector('span.label-text:contains("' + '{{ __('invoices.quantity') }}' + '")').textContent}</span>
                            </label>
                            <input type="number" name="items[${newIndex}][quantity]" class="input input-bordered" value="1" min="0.01" step="0.01" required>
                        </div>

                        <div class="form-control">
                            <label class="label">
                                <span class="label-text">${document.querySelector('span.label-text:contains("' + '{{ __('invoices.unit_price') }}' + '")').textContent}</span>
                            </label>
                            <input type="number" name="items[${newIndex}][unit_price_display]" class="input input-bordered unit-price" value="0" min="0" step="0.01" required data-index="${newIndex}">
                            <input type="hidden" name="items[${newIndex}][unit_price_cents]" class="unit-price-cents" value="0">
                        </div>
                    </div>

                    <div class="form-control mt-4">
                        <label class="label">
                            <span class="label-text">${document.querySelector('span.label-text:contains("' + '{{ __('invoices.tax_rate') }}' + '")').textContent}</span>
                        </label>
                        <input type="number" name="items[${newIndex}][tax_rate]" class="input input-bordered" value="0" min="0" max="100" step="0.01">
                    </div>

                    <div class="flex justify-end mt-4">
                        <button type="button" class="btn btn-sm btn-error remove-item">
                            ${document.querySelector('button.remove-item').textContent.trim()}
                        </button>
                    </div>
                `;

                invoiceItems.appendChild(newItem);

                // Show remove buttons if there's more than one item
                if (invoiceItems.querySelectorAll('.invoice-item').length > 1) {
                    invoiceItems.querySelectorAll('.remove-item').forEach(button => {
                        button.style.display = 'block';
                    });
                }

                // Add event listener to the new unit price input
                const newUnitPriceInput = newItem.querySelector('.unit-price');
                newUnitPriceInput.addEventListener('input', handleUnitPriceChange);

                // Add event listener to the new remove button
                const newRemoveButton = newItem.querySelector('.remove-item');
                newRemoveButton.addEventListener('click', handleRemoveItem);
            });

            // Handle unit price change
            function handleUnitPriceChange(event) {
                const input = event.target;
                const index = input.dataset.index;
                const dollars = parseFloat(input.value) || 0;
                const cents = Math.round(dollars * 100);
                const centsInput = document.querySelector(`input[name="items[${index}][unit_price_cents]"]`);
                centsInput.value = cents;
            }

            // Add event listeners to existing unit price inputs
            document.querySelectorAll('.unit-price').forEach(input => {
                input.addEventListener('input', handleUnitPriceChange);
                // Initialize the hidden cents field with the correct value
                handleUnitPriceChange({target: input});
            });

            // Handle remove item
            function handleRemoveItem(event) {
                const item = event.target.closest('.invoice-item');
                item.remove();

                // Hide remove buttons if there's only one item left
                if (invoiceItems.querySelectorAll('.invoice-item').length <= 1) {
                    invoiceItems.querySelectorAll('.remove-item').forEach(button => {
                        button.style.display = 'none';
                    });
                }

                // Reindex the remaining items
                invoiceItems.querySelectorAll('.invoice-item').forEach((item, index) => {
                    item.querySelectorAll('input, select').forEach(input => {
                        const name = input.getAttribute('name');
                        if (name) {
                            const newName = name.replace(/items\[\d+\]/, `items[${index}]`);
                            input.setAttribute('name', newName);
                        }
                    });

                    const unitPriceInput = item.querySelector('.unit-price');
                    if (unitPriceInput) {
                        unitPriceInput.dataset.index = index;
                    }
                });
            }

            // Add event listeners to existing remove buttons
            document.querySelectorAll('.remove-item').forEach(button => {
                button.addEventListener('click', handleRemoveItem);
            });
        });
    </script>
    @endpush
</x-app-layout>
