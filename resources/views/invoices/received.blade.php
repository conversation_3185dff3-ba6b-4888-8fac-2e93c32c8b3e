<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100/50 dark:bg-neutral-focus/50 backdrop-blur-xl shadow-xl sm:rounded-lg p-8">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                    <h2 class="text-2xl font-bold">{{ __('invoices.received_invoices') }}</h2>

                    <a href="{{ route('invoices.index') }}" class="btn btn-ghost btn-sm sm:btn-md">
                        {{ __('invoices.view_sent_invoices') }}
                    </a>
                </div>

{{--                @if (session('success'))--}}
{{--                    <div class="alert alert-success mb-6">--}}
{{--                        {{ session('success') }}--}}
{{--                    </div>--}}
{{--                @endif--}}

{{--                @if ($errors->any())--}}
{{--                    <div class="alert alert-error mb-6">--}}
{{--                        <ul>--}}
{{--                            @foreach ($errors->all() as $error)--}}
{{--                                <li>{{ $error }}</li>--}}
{{--                            @endforeach--}}
{{--                        </ul>--}}
{{--                    </div>--}}
{{--                @endif--}}

                <!-- Desktop Table (hidden on mobile) -->
                <div class="hidden md:block overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr>
                                <th>{{ __('invoices.invoice_number') }}</th>
                                <th>{{ __('invoices.from') }}</th>
                                <th>{{ __('invoices.amount') }}</th>
                                <th>{{ __('invoices.status') }}</th>
                                <th>{{ __('invoices.due_date') }}</th>
                                <th>{{ __('app.actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($invoices as $invoice)
                                <tr>
                                    <td>{{ $invoice->invoice_number }}</td>
                                    <td>{{ $invoice->creator->name }}</td>
                                    <td>{{ $invoice->formatted_total }}</td>
                                    <td>
                                        @if($invoice->status === 'draft')
                                            <span class="badge badge-ghost">{{ __('invoices.draft') }}</span>
                                        @elseif($invoice->status === 'sent')
                                            <span class="badge badge-info">{{ __('invoices.sent') }}</span>
                                        @elseif($invoice->status === 'paid')
                                            <span class="badge badge-success">{{ __('invoices.paid') }}</span>
                                        @elseif($invoice->status === 'partial')
                                            <span class="badge badge-warning">{{ __('invoices.partial') }}</span>
                                        @elseif($invoice->status === 'overdue')
                                            <span class="badge badge-error">{{ __('invoices.overdue') }}</span>
                                        @elseif($invoice->status === 'cancelled')
                                            <span class="badge badge-error">{{ __('invoices.cancelled') }}</span>
                                        @endif
                                    </td>
                                    <td>{{ $invoice->due_date->format('M j, Y') }}</td>
                                    <td>
                                        <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-sm btn-ghost">
                                            {{ __('app.view') }}
                                        </a>

                                        @if(in_array($invoice->status, ['sent', 'partial']) && !$invoice->is_overdue)
                                            <a href="{{ route('invoices.pay', $invoice) }}" class="btn btn-sm btn-primary">
                                                {{ __('invoices.pay_now') }}
                                            </a>
                                        @endif
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="text-center py-4">
                                        {{ __('invoices.no_invoices_received') }}
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Mobile Card Layout (visible only on mobile) -->
                <div class="md:hidden space-y-4">
                    @forelse($invoices as $invoice)
                        <div class="card bg-base-200 shadow-sm">
                            <div class="card-body p-4">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h3 class="font-bold">#{{ $invoice->invoice_number }}</h3>
                                        <p class="text-sm">{{ __('invoices.from') }}: {{ $invoice->creator->name }}</p>
                                    </div>
                                    <div>
                                        @if($invoice->status === 'draft')
                                            <span class="badge badge-ghost">{{ __('invoices.draft') }}</span>
                                        @elseif($invoice->status === 'sent')
                                            <span class="badge badge-info">{{ __('invoices.sent') }}</span>
                                        @elseif($invoice->status === 'paid')
                                            <span class="badge badge-success">{{ __('invoices.paid') }}</span>
                                        @elseif($invoice->status === 'partial')
                                            <span class="badge badge-warning">{{ __('invoices.partial') }}</span>
                                        @elseif($invoice->status === 'overdue')
                                            <span class="badge badge-error">{{ __('invoices.overdue') }}</span>
                                        @elseif($invoice->status === 'cancelled')
                                            <span class="badge badge-error">{{ __('invoices.cancelled') }}</span>
                                        @endif
                                    </div>
                                </div>

                                <div class="grid grid-cols-2 gap-2 mt-2 text-sm">
                                    <div>
                                        <span class="text-base-content/70">{{ __('invoices.amount') }}:</span>
                                        <span class="font-medium">{{ $invoice->formatted_total }}</span>
                                    </div>
                                    <div>
                                        <span class="text-base-content/70">{{ __('invoices.due_date') }}:</span>
                                        <span>{{ $invoice->due_date->format('M j, Y') }}</span>
                                    </div>
                                </div>

                                <div class="card-actions justify-end mt-3 flex-wrap gap-2">
                                    <a href="{{ route('invoices.show', $invoice) }}" class="btn btn-sm btn-ghost">
                                        {{ __('app.view') }}
                                    </a>

                                    @if(in_array($invoice->status, ['sent', 'partial']) && !$invoice->is_overdue)
                                        <a href="{{ route('invoices.pay', $invoice) }}" class="btn btn-sm btn-primary">
                                            {{ __('invoices.pay_now') }}
                                        </a>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8 px-4 bg-base-200 rounded-lg">
                            <p>{{ __('invoices.no_invoices_received') }}</p>
                        </div>
                    @endforelse
                </div>

                <div class="mt-4">
                    {{ $invoices->links() }}
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
