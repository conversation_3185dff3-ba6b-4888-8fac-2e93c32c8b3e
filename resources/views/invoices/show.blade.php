<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100/50 dark:bg-neutral-focus/50 backdrop-blur-xl shadow-xl sm:rounded-lg p-8">
                <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
                    <h2 class="text-2xl font-bold">{{ __('invoices.invoice') }} #{{ $invoice->invoice_number }}</h2>

                    <div class="flex flex-wrap gap-2 w-full sm:w-auto">
                        @if($isCreator)
                            <a href="{{ route('invoices.index') }}" class="btn btn-ghost btn-sm sm:btn-md">
                                {{ __('invoices.back_to_invoices') }}
                            </a>

                            @if($invoice->status === 'draft')
                                <form action="{{ route('invoices.update-status', $invoice) }}" method="POST">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="status" value="sent">
                                    <button type="submit" class="btn btn-primary btn-sm sm:btn-md">
                                        {{ __('invoices.send_invoice') }}
                                    </button>
                                </form>
                            @endif

                            @if(in_array($invoice->status, ['draft', 'sent']) && !$invoice->is_overdue)
                                <form action="{{ route('invoices.update-status', $invoice) }}" method="POST">
                                    @csrf
                                    @method('PUT')
                                    <input type="hidden" name="status" value="cancelled">
                                    <button type="submit" class="btn btn-error btn-sm sm:btn-md" onclick="return confirm('{{ __('invoices.confirm_cancel') }}')">
                                        {{ __('invoices.cancel_invoice') }}
                                    </button>
                                </form>
                            @endif
                        @else
                            <a href="{{ route('invoices.received') }}" class="btn btn-ghost btn-sm sm:btn-md">
                                {{ __('invoices.back_to_invoices') }}
                            </a>

                            @if(in_array($invoice->status, ['sent', 'partial']) && !$invoice->is_overdue)
                                <a href="{{ route('invoices.pay', $invoice) }}" class="btn btn-primary btn-sm sm:btn-md">
                                    {{ __('invoices.pay_invoice') }}
                                </a>
                            @endif
                        @endif
                    </div>
                </div>

{{--                @if (session('success'))--}}
{{--                    <div class="alert alert-success mb-6">--}}
{{--                        {{ session('success') }}--}}
{{--                    </div>--}}
{{--                @endif--}}

{{--                @if ($errors->any())--}}
{{--                    <div class="alert alert-error mb-6">--}}
{{--                        <ul>--}}
{{--                            @foreach ($errors->all() as $error)--}}
{{--                                <li>{{ $error }}</li>--}}
{{--                            @endforeach--}}
{{--                        </ul>--}}
{{--                    </div>--}}
{{--                @endif--}}

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div class="card bg-base-200 shadow-md">
                        <div class="card-body">
                            <h3 class="card-title text-xl">Invoice Details</h3>

                            <div class="mt-4 space-y-4">
                                <div>
                                    <p class="text-sm text-base-content/70">Status</p>
                                    <p class="text-lg">
                                        @if($invoice->status === 'draft')
                                            <span class="badge badge-ghost">Draft</span>
                                        @elseif($invoice->status === 'sent')
                                            <span class="badge badge-info">Sent</span>
                                        @elseif($invoice->status === 'paid')
                                            <span class="badge badge-success">Paid</span>
                                        @elseif($invoice->status === 'partial')
                                            <span class="badge badge-warning">Partial</span>
                                        @elseif($invoice->status === 'overdue')
                                            <span class="badge badge-error">Overdue</span>
                                        @elseif($invoice->status === 'cancelled')
                                            <span class="badge badge-error">Cancelled</span>
                                        @endif

                                        @if($invoice->is_overdue && !in_array($invoice->status, ['paid', 'cancelled']))
                                            <span class="badge badge-error ml-2">Overdue</span>
                                        @endif
                                    </p>
                                </div>

                                <div>
                                    <p class="text-sm text-base-content/70">Invoice Date</p>
                                    <p class="text-lg">{{ $invoice->issued_date->format('M j, Y') }}</p>
                                </div>

                                <div>
                                    <p class="text-sm text-base-content/70">Due Date</p>
                                    <p class="text-lg">{{ $invoice->due_date->format('M j, Y') }}</p>
                                </div>

                                @if($invoice->paid_date)
                                    <div>
                                        <p class="text-sm text-base-content/70">Paid Date</p>
                                        <p class="text-lg">{{ $invoice->paid_date->format('M j, Y') }}</p>
                                    </div>
                                @endif

                                @if($invoice->case_file_id)
                                    <div>
                                        <p class="text-sm text-base-content/70">Case</p>
                                        <p class="text-lg">
                                            <a href="{{ route('case-files.show', $invoice->case_file_id) }}" class="link link-primary">
                                                {{ $invoice->caseFile->title }}
                                            </a>
                                        </p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <div class="card bg-base-200 shadow-md">
                        <div class="card-body">
                            <h3 class="card-title text-xl">{{ $isCreator ? 'Client' : 'From' }}</h3>

                            <div class="mt-4 space-y-4">
                                <div>
                                    <p class="text-sm text-base-content/70">Name</p>
                                    <p class="text-lg">{{ $isCreator ? $invoice->recipient->name : $invoice->creator->name }}</p>
                                </div>

                                <div>
                                    <p class="text-sm text-base-content/70">Email</p>
                                    <p class="text-lg">{{ $isCreator ? $invoice->recipient->email : $invoice->creator->email }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card bg-base-200 shadow-md mb-8">
                    <div class="card-body">
                        <h3 class="card-title text-xl">Invoice Items</h3>

                        <!-- Desktop Table (hidden on mobile) -->
                        <div class="hidden md:block overflow-x-auto mt-4">
                            <table class="table w-full">
                                <thead>
                                    <tr>
                                        <th>Description</th>
                                        <th class="text-right">Quantity</th>
                                        <th class="text-right">Unit Price</th>
                                        <th class="text-right">Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($invoice->items as $item)
                                        <tr>
                                            <td>{{ $item->description }}</td>
                                            <td class="text-right">{{ $item->quantity }}</td>
                                            <td class="text-right">{{ $item->formatted_unit_price }}</td>
                                            <td class="text-right">{{ $item->formatted_amount }}</td>
                                        </tr>
                                    @endforeach
                                </tbody>
                                <tfoot>
                                    <tr>
                                        <td colspan="3" class="text-right font-bold">Subtotal</td>
                                        <td class="text-right">{{ $invoice->formatted_amount }}</td>
                                    </tr>

                                    @if($invoice->tax_cents > 0)
                                        <tr>
                                            <td colspan="3" class="text-right font-bold">Tax</td>
                                            <td class="text-right">${{ number_format($invoice->tax_cents / 100, 2) }}</td>
                                        </tr>
                                    @endif

                                    <tr>
                                        <td colspan="3" class="text-right font-bold">Total</td>
                                        <td class="text-right font-bold">{{ $invoice->formatted_total }}</td>
                                    </tr>

                                    @if($invoice->status === 'partial')
                                        <tr>
                                            <td colspan="3" class="text-right font-bold">Paid</td>
                                            <td class="text-right">${{ number_format($invoice->getTotalPaidAttribute() / 100, 2) }}</td>
                                        </tr>

                                        <tr>
                                            <td colspan="3" class="text-right font-bold">Balance Due</td>
                                            <td class="text-right font-bold">${{ number_format($invoice->getRemainingBalanceAttribute() / 100, 2) }}</td>
                                        </tr>
                                    @endif
                                </tfoot>
                            </table>
                        </div>

                        <!-- Mobile Layout (visible only on mobile) -->
                        <div class="md:hidden mt-4 space-y-4">
                            @foreach($invoice->items as $item)
                                <div class="border-b border-base-300 pb-3">
                                    <div class="font-medium">{{ $item->description }}</div>
                                    <div class="grid grid-cols-2 gap-2 mt-1 text-sm">
                                        <div>
                                            <span class="text-base-content/70">Quantity:</span>
                                            <span>{{ $item->quantity }}</span>
                                        </div>
                                        <div>
                                            <span class="text-base-content/70">Unit Price:</span>
                                            <span>{{ $item->formatted_unit_price }}</span>
                                        </div>
                                        <div class="col-span-2 text-right font-medium">
                                            <span class="text-base-content/70">Amount:</span>
                                            <span>{{ $item->formatted_amount }}</span>
                                        </div>
                                    </div>
                                </div>
                            @endforeach

                            <div class="border-t border-base-300 pt-3 space-y-2">
                                <div class="flex justify-between">
                                    <span class="font-medium">Subtotal</span>
                                    <span>{{ $invoice->formatted_amount }}</span>
                                </div>

                                @if($invoice->tax_cents > 0)
                                    <div class="flex justify-between">
                                        <span class="font-medium">Tax</span>
                                        <span>${{ number_format($invoice->tax_cents / 100, 2) }}</span>
                                    </div>
                                @endif

                                <div class="flex justify-between">
                                    <span class="font-bold">Total</span>
                                    <span class="font-bold">{{ $invoice->formatted_total }}</span>
                                </div>

                                @if($invoice->status === 'partial')
                                    <div class="flex justify-between">
                                        <span class="font-medium">Paid</span>
                                        <span>${{ number_format($invoice->getTotalPaidAttribute() / 100, 2) }}</span>
                                    </div>

                                    <div class="flex justify-between">
                                        <span class="font-bold">Balance Due</span>
                                        <span class="font-bold">${{ number_format($invoice->getRemainingBalanceAttribute() / 100, 2) }}</span>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>

                @if($invoice->notes)
                    <div class="card bg-base-200 shadow-md mb-8">
                        <div class="card-body">
                            <h3 class="card-title text-xl">Notes</h3>

                            <div class="mt-4">
                                <p class="whitespace-pre-line">{{ $invoice->notes }}</p>
                            </div>
                        </div>
                    </div>
                @endif

                @if($invoice->payments->count() > 0)
                    <div class="card bg-base-200 shadow-md">
                        <div class="card-body">
                            <h3 class="card-title text-xl">Payment History</h3>

                            <!-- Desktop Table (hidden on mobile) -->
                            <div class="hidden md:block overflow-x-auto mt-4">
                                <table class="table w-full">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Amount</th>
                                            <th>Method</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($invoice->payments as $payment)
                                            <tr>
                                                <td>{{ $payment->created_at->format('M j, Y') }}</td>
                                                <td>{{ $payment->formatted_amount }}</td>
                                                <td>
                                                    @if($payment->payment_method === 'credit_card')
                                                        Credit Card
                                                    @elseif($payment->payment_method === 'platform_credits')
                                                        Platform Credits
                                                    @else
                                                        {{ ucfirst($payment->payment_method) }}
                                                    @endif
                                                </td>
                                                <td>
                                                    @if($payment->status === 'completed')
                                                        <span class="badge badge-success">Completed</span>
                                                    @elseif($payment->status === 'pending')
                                                        <span class="badge badge-warning">Pending</span>
                                                    @elseif($payment->status === 'failed')
                                                        <span class="badge badge-error">Failed</span>
                                                    @elseif($payment->status === 'refunded')
                                                        <span class="badge badge-info">Refunded</span>
                                                    @endif
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <!-- Mobile Layout (visible only on mobile) -->
                            <div class="md:hidden mt-4 space-y-4">
                                @foreach($invoice->payments as $payment)
                                    <div class="border-b border-base-300 pb-3 last:border-b-0 last:pb-0">
                                        <div class="flex justify-between items-start">
                                            <div>
                                                <div class="font-medium">{{ $payment->created_at->format('M j, Y') }}</div>
                                                <div class="text-sm">{{ $payment->formatted_amount }}</div>
                                            </div>
                                            <div>
                                                @if($payment->status === 'completed')
                                                    <span class="badge badge-success">Completed</span>
                                                @elseif($payment->status === 'pending')
                                                    <span class="badge badge-warning">Pending</span>
                                                @elseif($payment->status === 'failed')
                                                    <span class="badge badge-error">Failed</span>
                                                @elseif($payment->status === 'refunded')
                                                    <span class="badge badge-info">Refunded</span>
                                                @endif
                                            </div>
                                        </div>
                                        <div class="text-sm mt-1">
                                            <span class="text-base-content/70">Method:</span>
                                            <span>
                                                @if($payment->payment_method === 'credit_card')
                                                    Credit Card
                                                @elseif($payment->payment_method === 'platform_credits')
                                                    Platform Credits
                                                @else
                                                    {{ ucfirst($payment->payment_method) }}
                                                @endif
                                            </span>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
