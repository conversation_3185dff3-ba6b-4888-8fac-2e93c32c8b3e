<x-form-section submit="updateProfileInformation">
    <x-slot name="title">
        {{ __('Profile Information') }}
    </x-slot>

    <x-slot name="description">
        {{ __('Update your account\'s profile information and email address.') }}
    </x-slot>

    <x-slot name="form">
        <!-- Profile Photo -->
        @if (Laravel\Jetstream\Jetstream::managesProfilePhotos())
            <div x-data="{photoName: null, photoPreview: null}" class="col-span-6 sm:col-span-4">
                <!-- Profile Photo File Input -->
                <input type="file" id="photo" class="hidden"
                            wire:model.live="photo"
                            x-ref="photo"
                            x-on:change="
                                    photoName = $refs.photo.files[0].name;
                                    const reader = new FileReader();
                                    reader.onload = (e) => {
                                        photoPreview = e.target.result;
                                    };
                                    reader.readAsDataURL($refs.photo.files[0]);
                            " />

                <x-label for="photo" value="{{ __('Photo') }}" />

                <!-- Current Profile Photo -->
                <div class="mt-2" x-show="! photoPreview">
                    <img src="{{ $this->user->profile_photo_url }}" alt="{{ $this->user->name }}" class="rounded-full size-20 object-cover">
                </div>

                <!-- New Profile Photo Preview -->
                <div class="mt-2" x-show="photoPreview" style="display: none;">
                    <span class="block rounded-full size-20 bg-cover bg-no-repeat bg-center"
                          x-bind:style="'background-image: url(\'' + photoPreview + '\');'">
                    </span>
                </div>

                <x-secondary-button class="mt-2 me-2" type="button" x-on:click.prevent="$refs.photo.click()">
                    {{ __('Select A New Photo') }}
                </x-secondary-button>

                @if ($this->user->profile_photo_path)
                    <x-secondary-button type="button" class="mt-2" wire:click="deleteProfilePhoto">
                        {{ __('Remove Photo') }}
                    </x-secondary-button>
                @endif

                <x-input-error for="photo" class="mt-2" />
            </div>
        @endif

        <!-- Name -->
        <div class="col-span-6 sm:col-span-4">
            <x-label for="name" value="{{ __('Name') }}" />
            <x-input id="name" type="text" class="mt-1 block w-full" wire:model="state.name" required autocomplete="name" />
            <x-input-error for="name" class="mt-2" />
        </div>

        <!-- Username -->
        <div class="col-span-6 sm:col-span-4">
            <x-label for="username" value="{{ __('Username') }}" />
            <x-input id="username" type="text" class="mt-1 block w-full" wire:model.live="state.username" required autocomplete="username" />
            <x-input-error for="username" class="mt-2" />
            <p class="mt-1 text-sm text-gray-500">
                {{ __('Username must be unique, 3-25 characters, and can only contain letters, numbers, dashes, and underscores.') }}
            </p>
        </div>

        <!-- Email -->
        <div class="col-span-6 sm:col-span-4">
            <x-label for="email" value="{{ __('Email') }}" />
            <x-input id="email" type="email" class="mt-1 block w-full" wire:model="state.email" required autocomplete="email" />
            <x-input-error for="email" class="mt-2" />
        </div>

        <!-- Attorney Status -->
        <div class="col-span-6 sm:col-span-4">
            <label class="flex items-center">
                <x-checkbox wire:model.live="state.is_attorney" />
                <span class="ml-2 text-sm text-gray-600">{{ __('I am an attorney') }}</span>
            </label>
        </div>

        <!-- Bar Card Number (only shown if is_attorney is true) -->
        @if(isset($state['is_attorney']) && $state['is_attorney'])
        <div class="col-span-6 sm:col-span-4 mt-4">
            <x-label for="bar_card_number" value="{{ __('Bar Card Number') }}" />
            <x-input id="bar_card_number" type="text" class="mt-1 block w-full" wire:model.live="state.bar_card_number" required />
            <x-input-error for="state.bar_card_number" class="mt-2" />
            <p class="mt-1 text-sm text-gray-500">
                {{ __('Please enter your bar card number or attorney license number.') }}
            </p>
        </div>
        @endif

        <!-- ZIP Code -->
        <div class="col-span-6 sm:col-span-4 mt-4">
            <x-label for="profile_zip_code" value="{{ __('ZIP Code') }}" />
            <div x-data="{
                retryCount: 0,
                maxRetries: 20,
                retryInterval: 200,
                initGooglePlaces() {
                    if (typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.places === 'undefined') {
                        this.retryCount++;
                        if (this.retryCount <= this.maxRetries) {
                            console.log(`Waiting for Google Maps API to load (attempt ${this.retryCount}/${this.maxRetries})`);
                            setTimeout(() => this.initGooglePlaces(), this.retryInterval);
                        } else {
                            console.error('Google Maps API failed to load after maximum retries');
                        }
                        return;
                    }

                    console.log('Google Maps Places API loaded successfully');
                    const input = document.getElementById('profile_zip_code');
                    const options = {
                        componentRestrictions: { country: 'us' },
                        fields: ['address_components', 'geometry'],
                        types: ['postal_code']
                    };

                    const autocomplete = new google.maps.places.Autocomplete(input, options);

                autocomplete.addListener('place_changed', () => {
                    const place = autocomplete.getPlace();

                    if (!place.geometry || !place.geometry.location) {
                        return;
                    }

                    // Extract ZIP code from place
                    let zipCode = '';
                    for (const component of place.address_components) {
                        if (component.types.includes('postal_code')) {
                            zipCode = component.short_name;
                            break;
                        }
                    }

                    // Set ZIP code and coordinates
                    if (zipCode) {
                        @this.set('state.zip_code', zipCode);
                        @this.set('state.latitude', place.geometry.location.lat());
                        @this.set('state.longitude', place.geometry.location.lng());

                        // Show notification in development environment
                        if ('{{ app()->environment('local', 'development', 'testing') }}' === '1') {
                            const notification = document.getElementById('coordinates-notification');
                            if (notification) {
                                notification.textContent = `Coordinates captured: ${place.geometry.location.lat()}, ${place.geometry.location.lng()}`;
                                notification.classList.remove('hidden');
                                setTimeout(() => notification.classList.add('hidden'), 5000);
                            }
                        }
                    }
                });
            } }" x-init="initGooglePlaces()">
                <x-input id="profile_zip_code" class="block mt-1 w-full" type="text" wire:model="state.zip_code" required autocomplete="new-password" placeholder="Enter ZIP code" />
            </div>
            <x-input-error for="state.zip_code" class="mt-2" />
            <p class="mt-1 text-sm text-gray-500">
                {{ __('Please enter your ZIP code to help us connect you with local attorneys.') }}
            </p>

            @if(app()->environment('local', 'development', 'testing'))
                <div id="coordinates-notification" class="mt-2 text-sm text-green-600 hidden"></div>
            @endif
        </div>

            @if (Laravel\Fortify\Features::enabled(Laravel\Fortify\Features::emailVerification()) && ! $this->user->hasVerifiedEmail())
                <p class="text-sm mt-2 dark:text-base-100">
                    {{ __('Your email address is unverified.') }}

                    <button type="button" class="underline text-sm text-base-content/60 dark:text-base-content/60 hover:text-base-content dark:hover:text-base-content/90 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800" wire:click.prevent="sendEmailVerification">
                        {{ __('Click here to re-send the verification email.') }}
                    </button>
                </p>

                @if ($this->verificationLinkSent)
                    <p class="mt-2 font-medium text-sm text-green-600 dark:text-green-400">
                        {{ __('A new verification link has been sent to your email address.') }}
                    </p>
                @endif
            @endif
        </div>
    </x-slot>

    <x-slot name="actions">
        <x-action-message class="me-3" on="saved">
            {{ __('Saved.') }}
        </x-action-message>

        <x-button class="w-full sm:w-auto px-4 py-2 bg-primary hover:bg-primary-focus text-white rounded-md shadow-sm transition-colors" wire:loading.attr="disabled" wire:target="photo">
            <span wire:loading.remove wire:target="photo ">{{ __('Save') }}</span>
            <span wire:loading wire:target="photo" class=" flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ __('Saving...') }}
            </span>
        </x-button>
    </x-slot>
</x-form-section>
