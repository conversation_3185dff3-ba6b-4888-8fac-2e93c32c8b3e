<script>
    document.addEventListener('alpine:init', () => {
        Alpine.data('exhibitUploader', ({ files = [], titles = [], descriptions = [] } = {}) => ({
            isDropping: false,
            isUploading: false,
            uploadingMessage: 'Preparing upload...',
            pendingUploads: 0,
            files: files,
            titles: titles,
            descriptions: descriptions,
            maxFileSize: 150 * 1024 * 1024, // 25MB in bytes
            allowedTypes: [
                'application/pdf',
                'application/msword',
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'image/jpeg',
                'image/png',
                // Audio types
                'audio/mpeg',
                'audio/mp3',
                'audio/wav',
                'audio/m4a',
                'audio/mp4',
                'audio/x-m4a',
                'audio/ogg',
                // Video types
                'video/mp4',
                'video/quicktime',
                'video/webm'
            ],

            onFileDropped(event) {
                this.handleFiles(event.dataTransfer.files);
            },

            onFileInputChanged(event) {
                this.handleFiles(event.target.files);
            },
            handleFiles(fileList) {
                this.isUploading = true;
                this.uploadingMessage = 'Processing files...';
                this.pendingUploads = fileList.length;

                Array.from(fileList).forEach(file => {
                    if (!this.validateFile(file)) {
                        this.pendingUploads--;
                        if (this.pendingUploads === 0) {
                            this.isUploading = false;
                        }
                        return;
                    }

                    // Add isSaving property when creating the file object
                    file.isSaving = false;

                    this.$wire.upload('files', file,
                        (uploadedFile) => {
                            // Use nextTick from Alpine instead
                            this.$nextTick(() => {
                                this.pendingUploads--;
                                this.uploadingMessage = this.pendingUploads > 0
                                    ? `Processing ${this.pendingUploads} remaining files...`
                                    : 'Upload complete!';

                                if (this.pendingUploads === 0) {
                                    setTimeout(() => {
                                        this.isUploading = false;
                                    }, 500);
                                }
                            });
                        },
                        () => {
                            this.uploadingMessage = '{{ __('documents.errors.upload_failed', ['message' => '']) }}';
                            this.$wire.dispatch('fileValidationError', {
                                message: `{{ __('documents.errors.upload_failed', ['message' => ':message']) }}`.replace(':message', file.name)
                            });
                            this.pendingUploads--;
                            if (this.pendingUploads === 0) {
                                setTimeout(() => {
                                    this.isUploading = false;
                                }, 1000);
                            }
                        },
                        (progressEvent) => {
                            const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
                            this.uploadingMessage = `Uploading... ${progress}%`;

                            const fileIndex = this.files.findIndex(f => f.metadata.name === file.name);
                            if (fileIndex !== -1 && progressEvent.total > 0) {
                                this.files[fileIndex].metadata.progress = progress;
                            }
                        }
                    );
                });
            },
            validateFile(file) {
                if (!this.allowedTypes.includes(file.type)) {
                    this.$wire.dispatch('fileValidationError', {
                        message: `{{ __('documents.validation.file_type') }}: ${file.name}`
                    });
                    return false;
                }

                if (file.size > this.maxFileSize) {
                    const sizeMB = (file.size / (1024 * 1024)).toFixed(2);
                    const maxSizeMB = (this.maxFileSize / (1024 * 1024)).toFixed(0);
                    this.$wire.dispatch('fileValidationError', {
                        message: `{{ __('documents.errors.file_too_large', ['name' => ':name', 'size' => ':size', 'max' => ':max']) }}`
                            .replace(':name', file.name)
                            .replace(':size', sizeMB)
                            .replace(':max', maxSizeMB)
                    });
                    return false;
                }

                return true;
            },

            simulateUpload(index) {
                const file = this.files[index];
                let progress = 0;

                const interval = setInterval(() => {
                    progress += 10;
                    file.progress = progress;

                    if (progress >= 100) {
                        clearInterval(interval);
                    }
                }, 100);
            },

            removeFile(index) {
                this.files.splice(index, 1);
                this.titles.splice(index, 1);
                this.descriptions.splice(index, 1);
                this.$wire.removeFile(index);
            },

            formatFileSize(bytes) {
                if (bytes < 1024) return bytes + ' B';
                else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
                else return (bytes / 1048576).toFixed(1) + ' MB';
            }
        }));
    });
</script>
