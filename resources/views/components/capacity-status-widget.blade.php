@php
    $capacityService = app(\App\Services\CapacityAlertService::class);
    $status = $capacityService->getCapacityStatus();
@endphp

<div class="bg-base-200 rounded-lg p-6">
    <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg font-semibold">OpenAI Capacity Status</h3>
        <div class="flex items-center space-x-2">
            @if($status['status'] === 'critical')
                <span class="w-3 h-3 bg-error rounded-full animate-pulse"></span>
                <span class="text-error font-medium">Critical</span>
            @elseif($status['status'] === 'warning')
                <span class="w-3 h-3 bg-warning rounded-full"></span>
                <span class="text-warning font-medium">Warning</span>
            @else
                <span class="w-3 h-3 bg-success rounded-full"></span>
                <span class="text-success font-medium">Good</span>
            @endif
        </div>
    </div>

    <div class="space-y-3">
        <!-- Status Message -->
        <div class="p-3 rounded-md {{ $status['status'] === 'critical' ? 'bg-error/10 text-error' : ($status['status'] === 'warning' ? 'bg-warning/10 text-warning' : 'bg-success/10 text-success') }}">
            <p class="font-medium">{{ $status['message'] }}</p>
        </div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-2 gap-4">
            <div class="text-center">
                <div class="text-2xl font-bold">{{ $status['projects_accepting'] }}/{{ $status['projects_total'] }}</div>
                <div class="text-sm text-base-content/60">Projects Available</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold">{{ $status['available_storage_gb'] }}GB</div>
                <div class="text-sm text-base-content/60">Available Storage</div>
            </div>
        </div>

        <!-- New User Capacity -->
        <div class="border-t border-base-300 pt-3">
            <div class="text-sm font-medium mb-2">New User Capacity:</div>
            <div class="grid grid-cols-3 gap-2 text-xs">
                <div class="text-center">
                    <div class="font-bold">{{ $status['new_user_capacity']['basic'] }}</div>
                    <div class="text-base-content/60">Basic</div>
                </div>
                <div class="text-center">
                    <div class="font-bold">{{ $status['new_user_capacity']['standard'] }}</div>
                    <div class="text-base-content/60">Standard</div>
                </div>
                <div class="text-center">
                    <div class="font-bold">{{ $status['new_user_capacity']['pro'] }}</div>
                    <div class="text-base-content/60">Pro</div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-2 pt-2">
            <a href="{{ route('openai.projects.index') }}" class="btn btn-sm btn-primary flex-1">
                Manage Projects
            </a>
            @if($status['status'] === 'critical' || $status['status'] === 'warning')
                <a href="{{ route('openai.projects.create') }}" class="btn btn-sm btn-error flex-1">
                    Add Project
                </a>
            @endif
        </div>

        <!-- Last Updated -->
        <div class="text-xs text-base-content/50 text-center">
            Last checked: {{ \Carbon\Carbon::parse($status['last_checked'])->diffForHumans() }}
        </div>
    </div>
</div>

@if($status['status'] === 'critical')
    <script>
        // Auto-refresh every 5 minutes when critical
        setTimeout(() => {
            window.location.reload();
        }, 300000);
    </script>
@endif
