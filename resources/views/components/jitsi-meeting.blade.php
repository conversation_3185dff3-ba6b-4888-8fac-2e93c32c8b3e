@props([
    'roomName' => null,
    'userDisplayName' => null,
    'userEmail' => null,
    'height' => '600px',
    'width' => '100%',
    'domain' => 'meet.jit.si',
    'options' => '{}',
])

@php
    // Generate a random room name if none is provided
    $roomName = $roomName ?? 'herd_' . \Illuminate\Support\Str::random(10);
    
    // Get user info if authenticated
    $userDisplayName = $userDisplayName ?? (auth()->check() ? auth()->user()->name : 'Guest');
    $userEmail = $userEmail ?? (auth()->check() ? auth()->user()->email : null);
    
    // Build the iframe URL with parameters
    $iframeUrl = "https://{$domain}/{$roomName}";
    $queryParams = [];
    
    if ($userDisplayName) {
        $queryParams['userInfo.displayName'] = $userDisplayName;
    }
    
    if ($userEmail) {
        $queryParams['userInfo.email'] = $userEmail;
    }
    
    if (!empty($queryParams)) {
        $iframeUrl .= '?' . http_build_query($queryParams);
    }
@endphp

<div {{ $attributes->merge(['class' => 'jitsi-container']) }}>
    <iframe
        src="{{ $iframeUrl }}"
        allow="camera; microphone; fullscreen; display-capture; autoplay"
        style="height: {{ $height }}; width: {{ $width }}; border: 0;"
        allowfullscreen="true"
    ></iframe>
</div>

<script>
    // We'll add JavaScript API integration here later
</script>
