@props(['draft', 'caseFile'])

<div id="generate-document-modal-{{ $draft->id }}" class="modal">
    <div class="w-full mx-4 modal-box max-w-none">
        <h3 class="text-lg font-bold">Would you like to attach exhibits to this document?</h3>

        <form action="{{ route('drafts.generate-document', $draft) }}" method="POST"
            id="generate-document-form-{{ $draft->id }}">
            @csrf

            <div class="py-4">
                <div id="exhibits-list-container-{{ $draft->id }}" class="hidden">
                    <p class="mb-4">Select the exhibits you want to attach:</p>

                    <div class="overflow-y-auto max-h-60">
                        <table class="table w-full table-compact">
                            <thead>
                                <tr>
                                    <th class="w-12"></th>
                                    <th>Exhibit</th>
                                    <th>Title</th>
                                    <th>Type</th>
                                </tr>
                            </thead>
                            <tbody>
                                @php
                                    $documents = $caseFile
                                        ->documents()
                                        ->whereHas('exhibit')
                                        ->whereNotNull('storage_path')
                                        ->where('document_type', 'exhibit')
                                        ->get();
                                @endphp

                                @forelse ($documents as $document)
                                    <tr>

                                        <td>
                                            <input type="checkbox" class="checkbox" name="selected_exhibits[]"
                                                value="{{ $document->id }}" id="exhibit-{{ $document->id }}">
                                        </td>
                                        <td>{{ $document->exhibit->label }}</td>
                                        <td>
                                            {{ $document->title ?? ($document->original_filename ?? 'Untitled') }}
                                            @if (\Illuminate\Support\Facades\Storage::exists($document->storage_path))
                                                <a href="{{ route('documents.download', $document) }}" target="_blank"
                                                    class="ml-2 text-xs text-blue-500">(preview)</a>
                                            @endif
                                        </td>
                                        <td>{{ $document->mime_type ?? 'Unknown' }}</td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="3" class="py-4 text-center">
                                            No documents found for this case.
                                        </td>
                                    </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <div class="mt-4 text-sm text-base-content/70">
                        <p>Selected exhibits will be added to the end of your document with title pages.</p>
                    </div>
                </div>
            </div>

            <div class="modal-action">
                <button type="button" id="btn-cancel-{{ $draft->id }}" class="btn btn-ghost">Cancel</button>
                <button type="button" id="btn-no-exhibits-{{ $draft->id }}" class="btn btn-ghost">No, Continue
                    Without Exhibits</button>
                <button type="button" id="btn-yes-exhibits-{{ $draft->id }}" class="btn btn-primary">Yes, Select
                    Exhibits</button>
                <button type="submit" id="btn-generate-with-exhibits-{{ $draft->id }}"
                    class="hidden btn btn-primary">Download Document</button>
                <button type="button" id="btn-back-{{ $draft->id }}" class="hidden btn btn-ghost">Back</button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Elements
        const modal = document.getElementById('generate-document-modal-{{ $draft->id }}');
        const btnYesExhibits = document.getElementById('btn-yes-exhibits-{{ $draft->id }}');
        const btnNoExhibits = document.getElementById('btn-no-exhibits-{{ $draft->id }}');
        const btnGenerateWithExhibits = document.getElementById(
            'btn-generate-with-exhibits-{{ $draft->id }}');
        const btnCancel = document.getElementById('btn-cancel-{{ $draft->id }}');
        const btnBack = document.getElementById('btn-back-{{ $draft->id }}');
        const exhibitsListContainer = document.getElementById('exhibits-list-container-{{ $draft->id }}');
        const form = document.getElementById('generate-document-form-{{ $draft->id }}');

        // Function to reset the workflow
        function resetWorkflow() {
            // Hide exhibits list
            exhibitsListContainer.classList.add('hidden');

            // Reset checkboxes
            const checkboxes = form.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.checked = false;
            });

            // Show initial buttons
            btnYesExhibits.classList.remove('hidden');
            btnNoExhibits.classList.remove('hidden');

            // Hide other buttons
            btnGenerateWithExhibits.classList.add('hidden');
            btnBack.classList.add('hidden');

            // Close the modal
            modal.classList.remove('modal-open');
        }

        // Event Listeners
        btnYesExhibits.addEventListener('click', function() {
            // Show exhibits list
            exhibitsListContainer.classList.remove('hidden');
            btnYesExhibits.classList.add('hidden');
            btnNoExhibits.classList.add('hidden');
            btnGenerateWithExhibits.classList.remove('hidden');
            btnBack.classList.remove('hidden');
        });

        btnNoExhibits.addEventListener('click', function() {
            // Submit the form without exhibits
            form.submit();
        });

        btnGenerateWithExhibits.addEventListener('click', function() {
            // Form will automatically submit the selected checkboxes
            form.submit();
        });

        btnCancel.addEventListener('click', function() {
            // Reset the workflow and close the modal
            resetWorkflow();
        });

        btnBack.addEventListener('click', function() {
            // Go back to the initial state
            exhibitsListContainer.classList.add('hidden');
            btnYesExhibits.classList.remove('hidden');
            btnNoExhibits.classList.remove('hidden');
            btnGenerateWithExhibits.classList.add('hidden');
            btnBack.classList.add('hidden');
        });
    });
</script>
