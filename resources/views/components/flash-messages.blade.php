@if (session('registered'))
    <div class="alert alert-success shadow-lg mb-6">
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            <span>{{ session('registered') }}</span>
            <h3>Pick your theme!</h3>
            <select class=" rounded-lg select select-bordered text-base-content focus:outline-none"
                    x-model="$store.theme.current" @change="$store.theme.setTheme($event.target.value)">
                <option value="light" class="flex items-center gap-2">🌝 Light</option>
                <option value="dark" class="flex items-center gap-2">🌚 Dark</option>
                <option value="cupcake" class="flex items-center gap-2">🧁 Cupcake</option>
                <option value="bumblebee" class="flex items-center gap-2">🐝 Bumblebee</option>
                <option value="emerald" class="flex items-center gap-2">✳️ Emerald</option>
                <option value="corporate" class="flex items-center gap-2">🏢 Corporate</option>
                <option value="synthwave" class="flex items-center gap-2">🌃 Synthwave</option>
                <option value="retro" class="flex items-center gap-2">👾 Retro</option>
                <option value="cyberpunk" class="flex items-center gap-2">🤖 Cyberpunk</option>
                <option value="valentine" class="flex items-center gap-2">🌸 Valentine</option>
                <option value="halloween" class="flex items-center gap-2">🎃 Halloween</option>
                <option value="garden" class="flex items-center gap-2">🌷 Garden</option>
                <option value="forest" class="flex items-center gap-2">🌲 Forest</option>
                <option value="aqua" class="flex items-center gap-2">💧 Aqua</option>
                <option value="lofi" class="flex items-center gap-2">📻 Lo-Fi</option>
                <option value="pastel" class="flex items-center gap-2">🎨 Pastel</option>
                <option value="fantasy" class="flex items-center gap-2">🧚 Fantasy</option>
                <option value="wireframe" class="flex items-center gap-2">📱 Wireframe</option>
                <option value="black" class="flex items-center gap-2">⚫ Black</option>
                <option value="luxury" class="flex items-center gap-2">💎 Luxury</option>
                <option value="dracula" class="flex items-center gap-2">🧛 Dracula</option>
                <option value="cmyk" class="flex items-center gap-2">🖨️ CMYK</option>
                <option value="autumn" class="flex items-center gap-2">🍂 Autumn</option>
                <option value="business" class="flex items-center gap-2">💼 Business</option>
                <option value="acid" class="flex items-center gap-2">🧪 Acid</option>
                <option value="lemonade" class="flex items-center gap-2">🍋 Lemonade</option>
                <option value="night" class="flex items-center gap-2">🌙 Night</option>
                <option value="coffee" class="flex items-center gap-2">☕ Coffee</option>
            </select>
            <button type="button" class="btn btn-sm btn-primary" onclick="window.location.reload()">Save</button>
        </div>
    </div>
@endif


@if (session('success'))
    <div class="alert alert-success shadow-lg mb-6">
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            <span>{{ session('success') }}</span>
        </div>
    </div>
@endif

@if (session('error'))
    <div class="alert alert-error shadow-lg mb-6">
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            <span>{{ session('error') }}</span>
        </div>
    </div>
@endif

@if (session('info'))
    <div class="alert alert-info shadow-lg mb-6">
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current flex-shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
            <span>{{ session('info') }}</span>
        </div>
    </div>
@endif

@if (session('warning'))
    <div class="alert alert-warning shadow-lg mb-6">
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
            <span>{{ session('warning') }}</span>
        </div>
    </div>
@endif

@if ($errors->any())
    <div class="alert alert-error shadow-lg mb-6">
        <div>
            <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            <ul>
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    </div>
@endif
