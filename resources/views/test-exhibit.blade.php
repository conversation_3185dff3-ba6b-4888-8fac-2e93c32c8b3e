<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Test Exhibit Integration') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-xl sm:rounded-lg p-6">
                <h3 class="text-lg font-medium mb-4">Exhibit Sidebar Test</h3>
                
                @livewire('drafts.exhibit-sidebar', ['caseFileId' => 1, 'draftId' => 1])
                
                <div class="mt-8">
                    <h3 class="text-lg font-medium mb-4">Text Insertion Test</h3>
                    <textarea id="active-section-content" class="w-full h-32 p-2 border rounded" placeholder="Text will be inserted here..."></textarea>
                    
                    <script>
                        document.addEventListener('DOMContentLoaded', function() {
                            // Listen for the insertTextAtCursor event
                            window.addEventListener('insertTextAtCursor', event => {
                                const text = event.detail.text;
                                const textarea = document.getElementById('active-section-content');
                                
                                if (!textarea) return;
                                
                                // Get cursor position
                                const startPos = textarea.selectionStart;
                                const endPos = textarea.selectionEnd;
                                
                                // Insert text at cursor position
                                const textBefore = textarea.value.substring(0, startPos);
                                const textAfter = textarea.value.substring(endPos);
                                
                                // Update textarea value
                                textarea.value = textBefore + text + textAfter;
                                
                                // Set cursor position after inserted text
                                textarea.selectionStart = startPos + text.length;
                                textarea.selectionEnd = startPos + text.length;
                                
                                // Focus the textarea
                                textarea.focus();
                            });
                        });
                    </script>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
