<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Justice Quest: Fight for Your Rights - AI Legal Platform</title>

    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio"></script>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bangers&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Bangers', cursive;
            letter-spacing: 0.05em;
        }
        [x-cloak] { display: none !important; }
        .fade-in { opacity: 0; transform: translateY(20px); transition: opacity 0.6s ease-out, transform 0.6s ease-out; }
        .fade-in.show { opacity: 1; transform: translateY(0); }

        .text-gradient {
            background: linear-gradient(to right, #ef4444, #f97316); /* red-500 to orange-500 */
            -webkit-background-clip: text;
            background-clip: text;
            color: transparent;
        }

        .btn-primary {
            @apply px-6 py-3 bg-gradient-to-r from-red-500 to-orange-600 text-white font-bold rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg tracking-wide;
        }
        .btn-secondary {
            @apply px-6 py-3 bg-gray-800 text-red-400 border border-red-500 font-bold rounded-lg shadow-sm transition duration-300 ease-in-out transform hover:scale-105 hover:bg-gray-700;
        }

        .feature-card {
            @apply bg-gray-800 p-6 rounded-xl shadow-lg text-center transform transition duration-300 hover:-translate-y-2 hover:shadow-xl border border-gray-700;
        }

        .grunge-bg {
            background-image: url('data:image/svg+xml,%3Csvg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200"%3E%3Cfilter id="noise"%3E%3CfeTurbulence type="fractalNoise" baseFrequency="0.7" numOctaves="3" stitchTiles="stitch"/%3E%3C/filter%3E%3Crect width="200" height="200" filter="url(%23noise)" opacity="0.1"/%3E%3C/svg%3E');
            background-size: cover;
        }

        ::-webkit-scrollbar {
            width: 8px;
        }
        ::-webkit-scrollbar-track {
            background: #1f2937;
        }
        ::-webkit-scrollbar-thumb {
            background: #ef4444;
            border-radius: 4px;
        }
        ::-webkit-scrollbar-thumb:hover {
            background: #f97316;
        }
    </style>
</head>
<body class="antialiased text-gray-200 bg-gray-900">

    <header class="sticky top-0 z-50 shadow-md bg-gray-900/90 backdrop-blur-sm" x-data="{ mobileMenuOpen: false }">
        <nav class="container flex items-center justify-between px-6 py-3 mx-auto">
            <a href="#" class="flex items-center space-x-2">
                <span class="text-xl font-bold text-gradient">Justice Quest</span>
            </a>

            <div class="items-center hidden space-x-6 md:flex">
                <a href="#features" class="font-bold tracking-wide text-gray-400 transition duration-300 hover:text-red-400">Features</a>
                <a href="#how-it-works" class="font-bold tracking-wide text-gray-400 transition duration-300 hover:text-red-400">How It Works</a>
                <a href="#about" class="font-bold tracking-wide text-gray-400 transition duration-300 hover:text-red-400">About</a>
                <a href="#" class="px-5 py-2 text-sm btn-primary">Get in the Ring</a>
            </div>

            <div class="md:hidden">
                <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-gray-400 focus:outline-none">
                    <i class="text-2xl fas fa-bars"></i>
                </button>
            </div>
        </nav>

        <div x-show="mobileMenuOpen" x-cloak
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform -translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform -translate-y-2"
             @click.away="mobileMenuOpen = false"
             class="absolute left-0 right-0 z-40 bg-gray-800 border-t border-gray-700 shadow-lg md:hidden top-full">
            <a href="#features" @click="mobileMenuOpen = false" class="block px-6 py-3 font-bold tracking-wide text-gray-400 transition duration-300 hover:bg-gray-700 hover:text-red-400">Features</a>
            <a href="#how-it-works" @click="mobileMenuOpen = false" class="block px-6 py-3 font-bold tracking-wide text-gray-400 transition duration-300 hover:bg-gray-700 hover:text-red-400">How It Works</a>
            <a href="#about" @click="mobileMenuOpen = false" class="block px-6 py-3 font-bold tracking-wide text-gray-400 transition duration-300 hover:bg-gray-700 hover:text-red-400">About</a>
            <a href="#" class="block w-full px-6 py-4 font-bold tracking-wide text-center text-white transition duration-300 bg-gradient-to-r from-red-500 to-orange-600 rounded-b-md">Get in the Ring</a>
        </div>
    </header>

    <section class="relative pt-20 pb-32 overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-gray-700 grunge-bg">
        <div class="absolute inset-0 pointer-events-none opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width=\'52\' height=\'26\' viewBox=\'0 0 52 26\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'currentColor\' fill-opacity=\'0.3\'%3E%3Cpath d=\'M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z\' /%3E%3C/g%3E%3C/svg%3E');"></div>

        <div class="container relative z-10 px-6 mx-auto text-center">
            <img src="https://justicequest.us-mia-1.linodeobjects.com/public/images/en/JQ.png" alt="Justice Quest Logo"
                 class="w-auto h-32 mx-auto mb-4 md:h-48 drop-shadow-xl"
                 x-data="{}" x-intersect.threshold.half="el => el.classList.add('show')" class="fade-in">

            <p class="mb-6 text-lg font-bold tracking-widest text-orange-400 uppercase md:text-xl"
               x-data="{}" x-intersect.threshold.half="el => el.classList.add('show')" class="fade-in" style="transition-delay: 100ms;">
                Liberty and Justice for All
            </p>

            <h1 class="mb-4 text-4xl font-extrabold leading-tight text-white md:text-6xl"
                x-data="{}" x-intersect.threshold.half="el => el.classList.add('show')" class="fade-in" style="transition-delay: 200ms;">
                Fight the <span class="text-gradient">Legal Battle</span> with AI Muscle
            </h1>

            <p class="max-w-3xl mx-auto mb-8 text-lg text-gray-300 md:text-xl"
                x-data="{}" x-intersect.threshold.half="el => el.classList.add('show')" class="fade-in" style="transition-delay: 300ms;">
                Justice Quest is your corner man, packing AI power to manage cases, draft documents, and throw punches at legal complexity.
            </p>

            <div class="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4"
                 x-data="{}" x-intersect.threshold.half="el => el.classList.add('show')" class="fade-in" style="transition-delay: 400ms;">
                <a href="#" class="btn-primary">
                    Step into the Ring <i class="ml-2 fas fa-fist-raised"></i>
                </a>
                <a href="#features" class="btn-secondary">
                    Check Your Gear
                </a>
            </div>
            <p class="mt-4 text-sm text-gray-500">Free trial to test your moves!</p>
        </div>
    </section>

    <section id="features" class="py-20 bg-gray-800 grunge-bg">
        <div class="container px-6 mx-auto">
            <h2 class="mb-4 text-3xl font-bold text-center text-white md:text-4xl"
                x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in">
                Your <span class="text-gradient">Street Fight Arsenal</span>
            </h2>
            <p class="max-w-2xl mx-auto mb-12 text-center text-gray-400"
               x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in" style="transition-delay: 100ms;">
               Gear up with AI tools to dominate the legal streets.
            </p>
            <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
                <div class="feature-card"
                     x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in">
                    <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 text-3xl text-white rounded-full shadow-lg bg-gradient-to-r from-red-500 to-orange-600">
                        <i class="fas fa-map-marked-alt"></i>
                    </div>
                    <h3 class="mb-2 text-xl font-bold text-white uppercase">Battle Map</h3>
                    <p class="text-sm text-gray-400">Your war room. Track cases, dates, files, and progress like a street boss.</p>
                </div>
                <div class="feature-card"
                     x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in" style="transition-delay: 100ms;">
                     <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 text-3xl text-white rounded-full shadow-lg bg-gradient-to-r from-orange-500 to-yellow-500">
                        <i class="fas fa-brain"></i>
                     </div>
                    <h3 class="mb-2 text-xl font-bold text-white uppercase">AI Enforcer</h3>
                    <p class="text-sm text-gray-400">Your street-smart ally. Get case-specific intel, file breakdowns, and answers on demand.</p>
                </div>
                <div class="feature-card"
                     x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in" style="transition-delay: 200ms;">
                     <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 text-3xl text-white rounded-full shadow-lg bg-gradient-to-r from-yellow-500 to-green-500">
                        <i class="fas fa-pen-nib"></i>
                     </div>
                    <h3 class="mb-2 text-xl font-bold text-white uppercase">Doc Forge</h3>
                    <p class="text-sm text-gray-400">Craft legal docs like a pro with templates, AI drafting, and street-ready formatting.</p>
                </div>
                <div class="feature-card"
                     x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in" style="transition-delay: 300ms;">
                     <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 text-3xl text-white rounded-full shadow-lg bg-gradient-to-r from-green-500 to-blue-500">
                         <i class="fas fa-handshake"></i>
                     </div>
                    <h3 class="mb-2 text-xl font-bold text-white uppercase">Crew Link</h3>
                    <p class="text-sm text-gray-400">Team up tight. Share cases, lock down access, and keep comms in check.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="how-it-works" class="py-20 bg-gray-900 grunge-bg">
        <div class="container px-6 mx-auto text-center">
            <h2 class="mb-12 text-3xl font-bold text-white md:text-4xl"
                x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in">
                Get in the Fight: 4 Quick Moves
            </h2>
            <div class="relative max-w-4xl mx-auto">
                <div class="absolute left-0 hidden w-full h-1 rounded-full md:block top-8 bg-red-500/30 -z-10"></div>
                <div class="relative grid grid-cols-1 gap-12 md:grid-cols-4 md:gap-8">
                    <div class="flex flex-col items-center"
                         x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in">
                        <div class="flex items-center justify-center w-16 h-16 mb-4 text-2xl font-bold text-white border-4 border-gray-800 rounded-full shadow-lg bg-gradient-to-br from-red-500 to-orange-600">1</div>
                        <i class="mb-3 text-3xl text-red-500 fas fa-file-import"></i>
                        <h3 class="mb-1 text-lg font-bold text-white uppercase">Drop Your Case</h3>
                        <p class="text-sm text-gray-400">Load up case details and files. Locked down tight.</p>
                    </div>
                    <div class="flex flex-col items-center"
                         x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in" style="transition-delay: 150ms;">
                         <div class="flex items-center justify-center w-16 h-16 mb-4 text-2xl font-bold text-white border-4 border-gray-800 rounded-full shadow-lg bg-gradient-to-br from-orange-500 to-yellow-500">2</div>
                        <i class="mb-3 text-3xl text-orange-500 fas fa-eye"></i>
                        <h3 class="mb-1 text-lg font-bold text-white uppercase">Get AI Recon</h3>
                        <p class="text-sm text-gray-400">AI scopes your case and delivers street-level insights.</p>
                    </div>
                    <div class="flex flex-col items-center"
                         x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in" style="transition-delay: 300ms;">
                         <div class="flex items-center justify-center w-16 h-16 mb-4 text-2xl font-bold text-white border-4 border-gray-800 rounded-full shadow-lg bg-gradient-to-br from-yellow-500 to-green-500">3</div>
                        <i class="mb-3 text-3xl text-yellow-500 fas fa-pen-square"></i>
                        <h3 class="mb-1 text-lg font-bold text-white uppercase">Forge Docs</h3>
                        <p class="text-sm text-gray-400">Build legal docs fast with AI and templates.</p>
                    </div>
                    <div class="flex flex-col items-center"
                         x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in" style="transition-delay: 450ms;">
                        <div class="flex items-center justify-center w-16 h-16 mb-4 text-2xl font-bold text-white border-4 border-gray-800 rounded-full shadow-lg bg-gradient-to-br from-green-500 to-blue-500">4</div>
                        <i class="mb-3 text-3xl text-green-500 fas fa-gavel"></i>
                        <h3 class="mb-1 text-lg font-bold text-white uppercase">Run the Game</h3>
                        <p class="text-sm text-gray-400">Stay sharp, organized, and ready to strike.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <section id="about" class="py-20 bg-gray-800 grunge-bg">
        <div class="container px-6 mx-auto">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="mb-6 text-3xl font-bold text-white md:text-4xl"
                    x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in">
                    <span class="text-gradient">No One Fights Alone</span>
                </h2>
                <p class="mb-8 text-lg text-gray-300"
                    x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in" style="transition-delay: 100ms;">
                    Justice Quest backs up pro se fighters, legal aid crews, and law pros taking on the system. We bring AI firepower to level the streets and fight for what's right.
                </p>
                <div class="flex flex-wrap justify-center gap-6 mb-8 text-gray-300"
                    x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in" style="transition-delay: 200ms;">
                    <span class="flex items-center px-4 py-2 space-x-2 text-sm font-bold text-red-400 bg-gray-700 rounded-full"><i class="fas fa-user-shield"></i><span>Pro Se Fighters</span></span>
                    <span class="flex items-center px-4 py-2 space-x-2 text-sm font-bold text-orange-400 bg-gray-700 rounded-full"><i class="fas fa-hands-helping"></i><span>Legal Aid Crews</span></span>
                    <span class="flex items-center px-4 py-2 space-x-2 text-sm font-bold text-yellow-400 bg-gray-700 rounded-full"><i class="fas fa-user-tie"></i><span>Law Pros</span></span>
                </div>
            </div>
        </div>
    </section>

    <section class="py-20 text-white bg-gradient-to-tl from-red-600 via-orange-500 to-yellow-500 grunge-bg">
        <div class="container px-6 mx-auto text-center">
            <h2 class="mb-6 text-3xl font-extrabold md:text-4xl"
                x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in">
                Ready to Throw Down for Justice?
            </h2>
            <p class="max-w-2xl mx-auto mb-8 text-lg text-white"
                x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in" style="transition-delay: 100ms;">
                Join Justice Quest, grab your AI backup, and start swinging with a free trial!
            </p>
            <a href="#" class="inline-block px-8 py-3 text-lg font-bold tracking-wide text-white transition duration-300 transform bg-gray-900 rounded-lg shadow-lg hover:bg-gray-800 hover:scale-105"
                x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in" style="transition-delay: 200ms;">
                Join the Fight <i class="ml-2 fas fa-bolt"></i>
            </a>
        </div>
    </section>

    <footer class="py-8 bg-gray-900 border-t border-gray-700 grunge-bg">
        <div class="container px-6 mx-auto text-center text-gray-500">
            <div class="flex justify-center mb-4 space-x-6">
                <a href="#" class="transition duration-300 hover:text-red-400"><i class="text-xl fab fa-twitter"></i></a>
                <a href="#" class="transition duration-300 hover:text-red-400"><i class="text-xl fab fa-linkedin-in"></i></a>
                <a href="#" class="transition duration-300 hover:text-red-400"><i class="text-xl fab fa-github"></i></a>
            </div>
            <p class="mb-2 text-sm">
                © <span x-text="new Date().getFullYear()">2025</span> Justice Quest. Liberty and Justice for All.
            </p>
            <div class="space-x-4 text-sm">
                <a href="#" class="transition duration-300 hover:text-red-400">Privacy Policy</a>
                <span>|</span>
                <a href="#" class="transition duration-300 hover:text-red-400">Terms of Service</a>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.directive('intersect', (el, { value, modifiers, expression }, { evaluateLater, cleanup }) => {
                let observer = new IntersectionObserver(entries => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            el.classList.add('show')
                            if (modifiers.includes('once')) {
                                observer.unobserve(el)
                            }
                        }
                    })
                }, {
                    threshold: evaluateLater(expression || '0.1')(),
                    rootMargin: modifiers.includes('margin') ? modifiers[modifiers.indexOf('margin') + 1] : '0px'
                })
                observer.observe(el)
                cleanup(() => { observer.disconnect() })
            })
        })
    </script>

</body>
</html>