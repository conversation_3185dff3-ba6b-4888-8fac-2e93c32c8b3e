<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-base-content leading-tight">
                {{ __('Create New Template') }}
            </h2>
            <a href="{{ route('document-templates.index') }}" class="btn btn-ghost btn-sm">
                ← {{ __('Back to Templates') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100 overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <form action="{{ route('document-templates.store') }}" method="POST">
                        @csrf
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="mb-6">
                                <label for="name" class="block text-sm font-medium text-base-content mb-2">
                                    {{ __('Template Name') }} <span class="text-error">*</span>
                                </label>
                                <input type="text" id="name" name="name" class="input input-bordered w-full @error('name') input-error @enderror" value="{{ old('name') }}" required>
                                @error('name')
                                    <p class="mt-1 text-sm text-error">{{ $message }}</p>
                                @enderror
                            </div>
                            
                            <div class="mb-6">
                                <label for="document_type" class="block text-sm font-medium text-base-content mb-2">
                                    {{ __('Document Type') }} <span class="text-error">*</span>
                                </label>
                                <select id="document_type" name="document_type" class="select select-bordered w-full @error('document_type') select-error @enderror" required>
                                    <option value="" disabled selected>{{ __('Select document type') }}</option>
                                    @foreach($documentTypes as $value => $label)
                                        <option value="{{ $value }}" {{ old('document_type') == $value ? 'selected' : '' }}>{{ $label }}</option>
                                    @endforeach
                                </select>
                                @error('document_type')
                                    <p class="mt-1 text-sm text-error">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                        
                        <div class="mb-6">
                            <label for="description" class="block text-sm font-medium text-base-content mb-2">
                                {{ __('Description') }}
                            </label>
                            <textarea id="description" name="description" rows="3" class="textarea textarea-bordered w-full @error('description') textarea-error @enderror" placeholder="Brief description of this template">{{ old('description') }}</textarea>
                            @error('description')
                                <p class="mt-1 text-sm text-error">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-6">
                            <label for="structure" class="block text-sm font-medium text-base-content mb-2">
                                {{ __('Document Structure') }} <span class="text-error">*</span>
                            </label>
                            <textarea id="structure" name="structure" rows="10" class="textarea textarea-bordered w-full font-mono @error('structure') textarea-error @enderror" placeholder='[{"id":"caption","name":"Caption","required":true,"order":1},{"id":"introduction","name":"Introduction","required":true,"order":2}]'>{{ old('structure') }}</textarea>
                            <p class="mt-1 text-xs text-base-content/70">{{ __('JSON array of document sections. Each section should have id, name, required, and order properties.') }}</p>
                            @error('structure')
                                <p class="mt-1 text-sm text-error">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-6">
                            <label for="default_content" class="block text-sm font-medium text-base-content mb-2">
                                {{ __('Default Content') }}
                            </label>
                            <textarea id="default_content" name="default_content" rows="6" class="textarea textarea-bordered w-full font-mono @error('default_content') textarea-error @enderror" placeholder='{"caption":"IN THE SUPERIOR COURT OF...","introduction":"COMES NOW the Plaintiff..."}'>{{ old('default_content') }}</textarea>
                            <p class="mt-1 text-xs text-base-content/70">{{ __('JSON object with default content for each section. Keys should match section IDs.') }}</p>
                            @error('default_content')
                                <p class="mt-1 text-sm text-error">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-6">
                            <label for="ai_prompts" class="block text-sm font-medium text-base-content mb-2">
                                {{ __('AI Generation Prompts') }}
                            </label>
                            <textarea id="ai_prompts" name="ai_prompts" rows="6" class="textarea textarea-bordered w-full font-mono @error('ai_prompts') textarea-error @enderror" placeholder='{"caption":"Generate a caption for a {document_type} in {jurisdiction}","introduction":"Write an introduction for a {document_type} regarding {case_type}"}'>{{ old('ai_prompts') }}</textarea>
                            <p class="mt-1 text-xs text-base-content/70">{{ __('JSON object with AI prompts for generating content for each section. Keys should match section IDs.') }}</p>
                            @error('ai_prompts')
                                <p class="mt-1 text-sm text-error">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-6">
                            <label for="metadata" class="block text-sm font-medium text-base-content mb-2">
                                {{ __('Metadata') }}
                            </label>
                            <textarea id="metadata" name="metadata" rows="4" class="textarea textarea-bordered w-full font-mono @error('metadata') textarea-error @enderror" placeholder='{"jurisdiction":"California","court_type":"Superior Court","document_category":"Pleading"}'>{{ old('metadata') }}</textarea>
                            <p class="mt-1 text-xs text-base-content/70">{{ __('JSON object with additional metadata for the template.') }}</p>
                            @error('metadata')
                                <p class="mt-1 text-sm text-error">{{ $message }}</p>
                            @enderror
                        </div>
                        
                        <div class="mb-6">
                            <label class="cursor-pointer label justify-start">
                                <input type="checkbox" name="is_active" class="checkbox checkbox-primary" value="1" {{ old('is_active', '1') == '1' ? 'checked' : '' }}>
                                <span class="label-text ml-2">{{ __('Active') }}</span>
                            </label>
                            <p class="mt-1 text-xs text-base-content/70">{{ __('Inactive templates will not be available for creating new drafts.') }}</p>
                        </div>
                        
                        <div class="flex justify-end">
                            <button type="submit" class="btn btn-primary">
                                {{ __('Create Template') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        // Helper function to validate JSON
        function isValidJSON(str) {
            try {
                JSON.parse(str);
                return true;
            } catch (e) {
                return false;
            }
        }
        
        // Add JSON validation to the form
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.querySelector('form');
            const jsonFields = ['structure', 'default_content', 'ai_prompts', 'metadata'];
            
            form.addEventListener('submit', function(e) {
                let hasError = false;
                
                jsonFields.forEach(field => {
                    const input = document.getElementById(field);
                    const value = input.value.trim();
                    
                    if (value && !isValidJSON(value)) {
                        e.preventDefault();
                        hasError = true;
                        input.classList.add('input-error');
                        
                        // Add error message if it doesn't exist
                        let errorMsg = input.parentNode.querySelector('.text-error');
                        if (!errorMsg) {
                            errorMsg = document.createElement('p');
                            errorMsg.className = 'mt-1 text-sm text-error';
                            errorMsg.textContent = 'Invalid JSON format';
                            input.parentNode.appendChild(errorMsg);
                        }
                    }
                });
                
                if (hasError) {
                    alert('Please fix the JSON format errors before submitting.');
                }
            });
        });
    </script>
</x-app-layout>
