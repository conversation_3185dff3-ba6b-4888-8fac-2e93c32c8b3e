<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-base-content leading-tight">
            {{ __('OpenAI Projects!') }}
        </h2>
        <p class="text-base-content/60">
            OpenAI limits each project to 100GB of storage for assistants and vector stores. This system automatically
            distributes cases across multiple projects to scale beyond this limit.<br>

            <span class="text-primary">Storage usage is tracked automatically</span> and cases are assigned to projects
            with the lowest usage. Each API key must be from a separate OpenAI project.
        </p>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100 overflow-hidden shadow-xl sm:rounded-lg p-6">
                @if (session('success'))
                    <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative mb-4">
                        {{ session('success') }}
                    </div>
                @endif

                <!-- Summary Cards -->
                @php
                    $totalProjects = $projects->count();
                    $activeProjects = $projects->where('is_active', true);
                    $totalStorageUsed = $activeProjects->sum('storage_used');
                    $totalStorageGB = round($totalStorageUsed / (1024 * 1024 * 1024), 2);
                    $maxPossibleStorage = $activeProjects->count() * 100; // 100GB per project
                    $overallPercentage = $maxPossibleStorage > 0 ? ($totalStorageGB / $maxPossibleStorage) * 100 : 0;
                    $nearCapacityProjects = $activeProjects->filter(fn($p) => $p->isNearingCapacity())->count();
                @endphp

                <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-base-200 rounded-lg p-4">
                        <div class="text-sm text-base-content/60">Total Projects</div>
                        <div class="text-2xl font-bold">{{ $totalProjects }}</div>
                        <div class="text-xs text-base-content/60">{{ $activeProjects->count() }} active</div>
                    </div>

                    <div class="bg-base-200 rounded-lg p-4">
                        <div class="text-sm text-base-content/60">Total Storage Used</div>
                        <div class="text-2xl font-bold">{{ $totalStorageGB }} GB</div>
                        <div class="text-xs text-base-content/60">of {{ $maxPossibleStorage }} GB available</div>
                    </div>

                    <div class="bg-base-200 rounded-lg p-4">
                        <div class="text-sm text-base-content/60">Overall Usage</div>
                        <div class="text-2xl font-bold">{{ number_format($overallPercentage, 1) }}%</div>
                        <div class="w-full bg-base-300 rounded-full h-2 mt-1">
                            <div class="h-2 rounded-full bg-primary" style="width: {{ min($overallPercentage, 100) }}%"></div>
                        </div>
                    </div>

                    <div class="bg-base-200 rounded-lg p-4">
                        <div class="text-sm text-base-content/60">Capacity Alerts</div>
                        <div class="text-2xl font-bold {{ $nearCapacityProjects > 0 ? 'text-warning' : 'text-success' }}">
                            {{ $nearCapacityProjects }}
                        </div>
                        <div class="text-xs text-base-content/60">projects over 90%</div>
                    </div>
                </div>

                <div class="flex justify-end mb-4">
                    <a href="{{ route('openai.projects.create') }}" class="inline-flex items-center px-4 py-2 bg-neutral-focus dark:bg-base-200 border border-transparent rounded-md font-semibold text-xs text-base-100 dark:text-base-content/80 uppercase tracking-widest hover:bg-neutral-focus dark:hover:bg-base-100">
                        {{ __('Add New Project') }}
                    </a>
                </div>

                @if($projects->count() > 0)
                <table class="min-w-full">
                    <thead>
                        <tr>
                            <th class="px-6 py-3 text-left">Name</th>
                            <th class="px-6 py-3 text-left">Organization ID</th>
                            <th class="px-6 py-3 text-left">Storage Usage</th>
                            <th class="px-6 py-3 text-left">Cases</th>
                            <th class="px-6 py-3 text-left">Status</th>
                            <th class="px-6 py-3 text-left">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($projects as $project)
                            @php
                                $storageInfo = $project->getStorageInfo();
                                $caseCount = $project->caseFiles()->count();
                                $documentCount = $project->caseFiles()->withCount('documents')->get()->sum('documents_count');
                                $capacity = $project->capacity;
                                $capacityInfo = $capacity ? $capacity->getCapacityInfo() : null;
                            @endphp
                            <tr class="border-b border-base-200">
                                <td class="px-6 py-4">
                                    <div class="font-medium">{{ $project->name }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <span class="text-sm text-base-content/70">{{ $project->organization_id ?: 'N/A' }}</span>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="space-y-2">
                                        <!-- Storage Usage Text -->
                                        <div class="flex items-center justify-between text-sm">
                                            <span class="font-medium">{{ $storageInfo['used_gb'] }} GB / 100 GB</span>
                                            <span class="text-base-content/60">({{ number_format($storageInfo['percentage'], 1) }}%)</span>
                                        </div>

                                        <!-- Progress Bar -->
                                        <div class="w-full bg-base-200 rounded-full h-2">
                                            <div class="h-2 rounded-full transition-all duration-300
                                                @if($storageInfo['percentage'] >= 95)
                                                    bg-error
                                                @elseif($storageInfo['percentage'] >= 90)
                                                    bg-warning
                                                @elseif($storageInfo['percentage'] >= 75)
                                                    bg-info
                                                @else
                                                    bg-success
                                                @endif
                                            " style="width: {{ min($storageInfo['percentage'], 100) }}%"></div>
                                        </div>

                                        <!-- Additional Info -->
                                        <div class="text-xs text-base-content/60">
                                            {{ $storageInfo['remaining_gb'] }} GB remaining
                                            @if($documentCount > 0)
                                                • {{ number_format($documentCount) }} documents
                                            @endif
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm space-y-1">
                                        <div class="font-medium">{{ number_format($caseCount) }} cases</div>
                                        @if($documentCount > 0)
                                            <div class="text-base-content/60">{{ number_format($documentCount) }} docs</div>
                                        @endif
                                        @if($capacityInfo)
                                            <div class="text-xs text-base-content/50">
                                                {{ $capacityInfo['total_users'] }} users allocated
                                                ({{ $capacityInfo['basic_users'] }}B + {{ $capacityInfo['standard_users'] }}S + {{ $capacityInfo['pro_users'] }}P)
                                            </div>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex flex-col space-y-1">
                                        <!-- Active/Inactive Status -->
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full w-fit {{ $project->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ $project->is_active ? 'Active' : 'Inactive' }}
                                        </span>

                                        <!-- Storage Status -->
                                        @if($project->is_active)
                                            @if($storageInfo['percentage'] >= 95)
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800 w-fit">
                                                    🔴 Critical
                                                </span>
                                            @elseif($storageInfo['percentage'] >= 90)
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800 w-fit">
                                                    🟡 Warning
                                                </span>
                                            @elseif($storageInfo['percentage'] >= 75)
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800 w-fit">
                                                    🔵 High Usage
                                                </span>
                                            @else
                                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800 w-fit">
                                                    🟢 Good
                                                </span>
                                            @endif
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 flex space-x-2">
                                    <x-button href="{{ route('openai.projects.edit', $project) }}" class="text-sm">
                                        Edit
                                    </x-button>
                                    <form action="{{ route('openai.projects.destroy', $project) }}" method="POST" class="inline">
                                        @csrf
                                        @method('DELETE')
                                        <x-button type="submit" class="text-sm bg-red-600 hover:bg-red-700" onclick="return confirm('Are you sure you want to delete this project?')">
                                            Delete
                                        </x-button>
                                    </form>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
                @else
                <div class="text-center py-12">
                    <div class="text-6xl mb-4">📊</div>
                    <h3 class="text-lg font-medium text-base-content mb-2">No OpenAI Projects</h3>
                    <p class="text-base-content/60 mb-6">Get started by adding your first OpenAI project to enable load balancing across multiple API keys.</p>
                    <a href="{{ route('openai.projects.create') }}" class="inline-flex items-center px-4 py-2 bg-primary text-primary-content rounded-md font-semibold text-sm uppercase tracking-widest hover:bg-primary-focus">
                        Add Your First Project
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
