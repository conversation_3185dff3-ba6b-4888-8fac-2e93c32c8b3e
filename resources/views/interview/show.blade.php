<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('cases.interview.title') }}: {{ $caseFile->title }}
        </h2>
        <!-- Back to case button -->
        <div class="mt-8">
            <a href="{{ route('case-files.show', $caseFile) }}" class="inline-flex items-center px-4 py-2 bg-gray-800 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-gray-700 active:bg-gray-900 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                {{ __('cases.actions.back_to_case') }}
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6 bg-white border-b border-gray-200">
                    <livewire:interview.interview-component :caseFile="$caseFile" />
                </div>
            </div>
        </div>
    </div>

    <!-- Chat Interface Component -->
    <livewire:chat.chat-interface />
</x-app-layout>
