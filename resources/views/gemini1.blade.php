<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Justice Quest: Liberty and Justice for All - AI Legal Platform</title>

    <script src="https://cdn.tailwindcss.com?plugins=forms,typography,aspect-ratio"></script>

    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" integrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />

    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700;800&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Poppins', sans-serif;
        }
        [x-cloak] { display: none !important; }
        .fade-in { opacity: 0; transform: translateY(20px); transition: opacity 0.6s ease-out, transform 0.6s ease-out; }
        .fade-in.show { opacity: 1; transform: translateY(0); }

        .text-gradient {
          background: linear-gradient(to right, #38bdf8, #6366f1); /* sky-500 to indigo-500 */
          -webkit-background-clip: text;
          background-clip: text;
          color: transparent;
        }

        .btn-primary {
            @apply px-6 py-3 bg-gradient-to-r from-cyan-500 to-blue-600 text-white font-semibold rounded-lg shadow-md transition duration-300 ease-in-out transform hover:scale-105 hover:shadow-lg;
        }
        .btn-secondary {
             @apply px-6 py-3 bg-white text-blue-600 border border-blue-600 font-semibold rounded-lg shadow-sm transition duration-300 ease-in-out transform hover:scale-105 hover:bg-blue-50;
        }

        .feature-card {
             @apply bg-white p-6 rounded-xl shadow-lg text-center transform transition duration-300 hover:-translate-y-2 hover:shadow-xl border border-gray-100;
        }

    </style>
</head>
<body class="antialiased text-gray-800 bg-gray-50">

    <header class="sticky top-0 z-50 shadow-sm bg-white/90 backdrop-blur-sm" x-data="{ mobileMenuOpen: false }">
        <nav class="container flex items-center justify-between px-6 py-3 mx-auto">
            <a href="#" class="flex items-center space-x-2">
                 <span class="text-lg font-bold text-gradient">Justice Quest</span>
            </a>

            <div class="items-center hidden space-x-6 md:flex">
                <a href="#features" class="font-medium text-gray-600 transition duration-300 hover:text-blue-600">Features</a>
                <a href="#how-it-works" class="font-medium text-gray-600 transition duration-300 hover:text-blue-600">How It Works</a>
                <a href="#about" class="font-medium text-gray-600 transition duration-300 hover:text-blue-600">About</a>
                <a href="#" class="px-5 py-2 text-sm btn-primary">Get Started</a>
            </div>

            <div class="md:hidden">
                <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-gray-700 focus:outline-none">
                    <i class="text-2xl fas fa-bars"></i>
                </button>
            </div>
        </nav>

        <div x-show="mobileMenuOpen" x-cloak
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform -translate-y-2"
             x-transition:enter-end="opacity-100 transform translate-y-0"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform translate-y-0"
             x-transition:leave-end="opacity-0 transform -translate-y-2"
             @click.away="mobileMenuOpen = false"
             class="absolute left-0 right-0 z-40 bg-white border-t border-gray-100 shadow-lg md:hidden top-full">
            <a href="#features" @click="mobileMenuOpen = false" class="block px-6 py-3 text-gray-600 transition duration-300 hover:bg-blue-50 hover:text-blue-600">Features</a>
            <a href="#how-it-works" @click="mobileMenuOpen = false" class="block px-6 py-3 text-gray-600 transition duration-300 hover:bg-blue-50 hover:text-blue-600">How It Works</a>
            <a href="#about" @click="mobileMenuOpen = false" class="block px-6 py-3 text-gray-600 transition duration-300 hover:bg-blue-50 hover:text-blue-600">About</a>
            <a href="#" class="block w-full px-6 py-4 font-semibold text-center text-white transition duration-300 bg-gradient-to-r from-cyan-500 to-blue-600 rounded-b-md">Get Started</a>
        </div>
    </header>

    <section class="relative pt-20 pb-32 overflow-hidden bg-gradient-to-br from-sky-100 via-blue-100 to-indigo-100">
         <div class="absolute inset-0 pointer-events-none opacity-10" style="background-image: url('data:image/svg+xml,%3Csvg width=\'52\' height=\'26\' viewBox=\'0 0 52 26\' xmlns=\'http://www.w3.org/2000/svg\'%3E%3Cg fill=\'currentColor\' fill-opacity=\'0.3\'%3E%3Cpath d=\'M10 10c0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6h2c0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4 3.314 0 6 2.686 6 6 0 2.21 1.79 4 4 4v2c-3.314 0-6-2.686-6-6 0-2.21-1.79-4-4-4-3.314 0-6-2.686-6-6zm25.464-1.95l8.486 8.486-1.414 1.414-8.486-8.486 1.414-1.414z\' /%3E%3C/g%3E%3C/svg%3E');"></div>

        <div class="container relative z-10 px-6 mx-auto text-center">
            <img src="https://justicequest.us-mia-1.linodeobjects.com/public/images/en/JQ.png" alt="Justice Quest Logo"
                 class="w-auto h-24 mx-auto mb-4 md:h-32"
                 x-data="{}" x-intersect.threshold.half="el => el.classList.add('show')" class="fade-in">

            <p class="mb-6 text-lg italic font-semibold text-indigo-700 md:text-xl"
               x-data="{}" x-intersect.threshold.half="el => el.classList.add('show')" class="fade-in" style="transition-delay: 100ms;">
                "Liberty and justice for all."
            </p>

            <h1 class="mb-4 text-4xl font-extrabold leading-tight text-gray-900 md:text-5xl"
                x-data="{}" x-intersect.threshold.half="el => el.classList.add('show')" class="fade-in" style="transition-delay: 200ms;">
                AI-Powered Legal Assistance <span class="text-gradient">Simplified</span>
            </h1>

            <p class="max-w-3xl mx-auto mb-8 text-lg text-gray-700 md:text-xl"
                x-data="{}" x-intersect.threshold.half="el => el.classList.add('show')" class="fade-in" style="transition-delay: 300ms;">
                Justice Quest helps you manage your case, draft documents, and navigate the legal system with an intelligent assistant.
            </p>

            <div class="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4"
                 x-data="{}" x-intersect.threshold.half="el => el.classList.add('show')" class="fade-in" style="transition-delay: 400ms;">
                <a href="#" class="btn-primary">
                    Start Your Quest <i class="ml-2 fas fa-arrow-right"></i>
                </a>
                <a href="#features" class="btn-secondary">
                    Explore Features
                </a>
            </div>
             <p class="mt-4 text-sm text-gray-500">Free trial available!</p>
        </div>
    </section>

    <section id="features" class="py-20 bg-white">
        <div class="container px-6 mx-auto">
            <h2 class="mb-4 text-3xl font-bold text-center text-gray-900 md:text-4xl"
                x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in">
                Your <span class="text-gradient">AI Toolkit</span> for Justice
            </h2>
            <p class="max-w-2xl mx-auto mb-12 text-center text-gray-600"
               x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in" style="transition-delay: 100ms;">
               Everything you need, supercharged with AI.
            </p>
            <div class="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
                <div class="feature-card"
                     x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in">
                    <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 text-3xl text-white rounded-full shadow-lg bg-gradient-to-r from-cyan-400 to-blue-500">
                        <i class="fas fa-folder-open"></i>
                    </div>
                    <h3 class="mb-2 text-xl font-semibold text-gray-800">Case Dashboard</h3>
                    <p class="text-sm text-gray-600">Your command center. Organize details, track dates, manage files, and see progress at a glance.</p>
                </div>
                <div class="feature-card"
                     x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in" style="transition-delay: 100ms;">
                     <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 text-3xl text-white rounded-full shadow-lg bg-gradient-to-r from-purple-400 to-indigo-500">
                        <i class="fas fa-wand-magic-sparkles"></i>
                    </div>
                    <h3 class="mb-2 text-xl font-semibold text-gray-800">AI Legal Guide</h3>
                    <p class="text-sm text-gray-600">Your smart companion. Get case-specific guidance, document summaries, and answers 24/7.</p>
                </div>
                <div class="feature-card"
                     x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in" style="transition-delay: 200ms;">
                     <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 text-3xl text-white rounded-full shadow-lg bg-gradient-to-r from-emerald-400 to-teal-500">
                        <i class="fas fa-file-signature"></i>
                     </div>
                    <h3 class="mb-2 text-xl font-semibold text-gray-800">AI Doc Crafter</h3>
                    <p class="text-sm text-gray-600">Draft like a pro. Use templates, section editing, and let AI help write and format legal docs.</p>
                </div>
                <div class="feature-card"
                     x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in" style="transition-delay: 300ms;">
                     <div class="flex items-center justify-center w-16 h-16 mx-auto mb-4 text-3xl text-white rounded-full shadow-lg bg-gradient-to-r from-pink-400 to-rose-500">
                         <i class="fas fa-users-gear"></i>
                     </div>
                    <h3 class="mb-2 text-xl font-semibold text-gray-800">Team Up</h3>
                    <p class="text-sm text-gray-600">Work together easily. Securely share cases, manage permissions, and track communications.</p>
                </div>
            </div>
        </div>
    </section>

    <section id="how-it-works" class="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
        <div class="container px-6 mx-auto text-center">
            <h2 class="mb-12 text-3xl font-bold text-gray-900 md:text-4xl"
                x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in">
                Start Your Quest in Minutes
            </h2>
            <div class="relative max-w-4xl mx-auto">
                <div class="absolute left-0 hidden w-full h-1 bg-blue-200 rounded-full md:block top-8 -z-10"></div>
                <div class="relative grid grid-cols-1 gap-12 md:grid-cols-4 md:gap-8">
                    <div class="flex flex-col items-center"
                         x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in">
                        <div class="flex items-center justify-center w-16 h-16 mb-4 text-2xl font-bold text-white border-4 border-white rounded-full shadow-lg bg-gradient-to-br from-cyan-400 to-blue-500">1</div>
                        <i class="mb-3 text-3xl text-blue-500 fas fa-upload"></i>
                        <h3 class="mb-1 text-lg font-semibold text-gray-800">Upload Case Info</h3>
                        <p class="text-sm text-gray-600">Add case details & docs. Securely stored.</p>
                    </div>
                    <div class="flex flex-col items-center"
                         x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in" style="transition-delay: 150ms;">
                         <div class="flex items-center justify-center w-16 h-16 mb-4 text-2xl font-bold text-white border-4 border-white rounded-full shadow-lg bg-gradient-to-br from-purple-400 to-indigo-500">2</div>
                        <i class="mb-3 text-3xl text-indigo-500 fas fa-lightbulb"></i>
                        <h3 class="mb-1 text-lg font-semibold text-gray-800">Get AI Insights</h3>
                        <p class="text-sm text-gray-600">AI analyzes info & answers your questions.</p>
                    </div>
                    <div class="flex flex-col items-center"
                         x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in" style="transition-delay: 300ms;">
                         <div class="flex items-center justify-center w-16 h-16 mb-4 text-2xl font-bold text-white border-4 border-white rounded-full shadow-lg bg-gradient-to-br from-emerald-400 to-teal-500">3</div>
                        <i class="mb-3 text-3xl text-teal-500 fas fa-feather-alt"></i>
                        <h3 class="mb-1 text-lg font-semibold text-gray-800">Draft Documents</h3>
                        <p class="text-sm text-gray-600">Use the AI editor to create legal papers easily.</p>
                    </div>
                    <div class="flex flex-col items-center"
                         x-data="{}" x-intersect.once.margin.-50px="el => el.classList.add('show')" class="fade-in" style="transition-delay: 450ms;">
                        <div class="flex items-center justify-center w-16 h-16 mb-4 text-2xl font-bold text-white border-4 border-white rounded-full shadow-lg bg-gradient-to-br from-pink-400 to-rose-500">4</div>
                        <i class="mb-3 text-3xl fas fa-gavel text-rose-500"></i>
                        <h3 class="mb-1 text-lg font-semibold text-gray-800">Manage & Proceed</h3>
                        <p class="text-sm text-gray-600">Stay organized and move forward confidently.</p>
                    </div>
                </div>
             </div>
        </div>
    </section>

    <section id="about" class="py-20 bg-white">
        <div class="container px-6 mx-auto">
            <div class="max-w-4xl mx-auto text-center">
                <h2 class="mb-6 text-3xl font-bold text-gray-900 md:text-4xl"
                    x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in">
                    <span class="text-gradient">Empowering Everyone</span> in the Legal Arena
                </h2>
                <p class="mb-8 text-lg text-gray-700"
                    x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in" style="transition-delay: 100ms;">
                    Whether you're representing yourself (pro se), part of a legal aid team, or a legal pro helping others, Justice Quest provides the high-tech tools you need to simplify complexity and strive for fairness.
                </p>
                 <div class="flex flex-wrap justify-center gap-6 mb-8 text-gray-700"
                    x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in" style="transition-delay: 200ms;">
                    <span class="flex items-center px-4 py-2 space-x-2 text-sm font-medium rounded-full bg-sky-100 text-sky-700"><i class="fas fa-user-shield"></i><span>Pro Se Litigants</span></span>
                    <span class="flex items-center px-4 py-2 space-x-2 text-sm font-medium text-indigo-700 bg-indigo-100 rounded-full"><i class="fas fa-hands-helping"></i><span>Legal Aid Orgs</span></span>
                    <span class="flex items-center px-4 py-2 space-x-2 text-sm font-medium text-teal-700 bg-teal-100 rounded-full"><i class="fas fa-user-tie"></i><span>Legal Professionals</span></span>
                 </div>
            </div>
        </div>
    </section>

    <section class="py-20 text-white bg-gradient-to-tl from-blue-600 via-cyan-500 to-sky-400">
        <div class="container px-6 mx-auto text-center">
            <h2 class="mb-6 text-3xl font-extrabold md:text-4xl"
                x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in">
                Ready to Conquer Your Legal Challenges?
            </h2>
            <p class="max-w-2xl mx-auto mb-8 text-lg text-blue-100"
                x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in" style="transition-delay: 100ms;">
                Unlock the power of AI for your case. Sign up for Justice Quest and start your free trial today!
            </p>
            <a href="#" class="inline-block px-8 py-3 text-lg font-bold text-blue-600 transition duration-300 transform bg-white rounded-lg shadow-lg hover:bg-gray-100 hover:scale-105"
                x-data="{}" x-intersect.once="el => el.classList.add('show')" class="fade-in" style="transition-delay: 200ms;">
                Claim Your Free Trial Now <i class="ml-2 fas fa-rocket"></i>
            </a>
        </div>
    </section>

    <footer class="py-8 bg-gray-100 border-t border-gray-200">
        <div class="container px-6 mx-auto text-center text-gray-500">
            <div class="flex justify-center mb-4 space-x-6">
                <a href="#" class="transition duration-300 hover:text-blue-600"><i class="text-xl fab fa-twitter"></i></a>
                <a href="#" class="transition duration-300 hover:text-blue-600"><i class="text-xl fab fa-linkedin-in"></i></a>
                <a href="#" class="transition duration-300 hover:text-blue-600"><i class="text-xl fab fa-github"></i></a>
            </div>
            <p class="mb-2 text-sm">
                &copy; <span x-text="new Date().getFullYear()">2025</span> Justice Quest. Liberty and justice for all.
            </p>
            <div class="space-x-4 text-sm">
                <a href="#" class="transition duration-300 hover:text-blue-600">Privacy Policy</a>
                <span>|</span>
                <a href="#" class="transition duration-300 hover:text-blue-600">Terms of Service</a>
            </div>
        </div>
    </footer>

    <script>
        document.addEventListener('alpine:init', () => {
            Alpine.directive('intersect', (el, { value, modifiers, expression }, { evaluateLater, cleanup }) => {
                let observer = new IntersectionObserver(entries => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            el.classList.add('show')
                            if (modifiers.includes('once')) {
                                observer.unobserve(el)
                            }
                        } else if (!modifiers.includes('once') && !modifiers.includes('enter')) {
                           // Reset fade-in if element scrolls out of view (optional)
                           // el.classList.remove('show');
                        }
                    })
                }, {
                    threshold: evaluateLater(expression || '0.1')(),
                    rootMargin: modifiers.includes('margin') ? modifiers[modifiers.indexOf('margin') + 1] : '0px'
                 })
                observer.observe(el)
                cleanup(() => { observer.disconnect() })
            })
        })
    </script>

</body>
</html>