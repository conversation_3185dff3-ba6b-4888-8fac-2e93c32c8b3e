<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" x-data :data-theme="$store.theme.current">

<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @auth
        <meta name="user-id" content="{{ auth()->id() }}">
    @endauth

    <title>{{ config('app.name', 'Laravel') }}</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ asset('images/jq-icon.ico') }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ asset('images/jq-icon.ico') }}" type="image/x-icon">
    <!-- Larger Icons for Different Devices -->
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/jq-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/jq-icon.png') }}">
    <link rel="icon" type="image/png" sizes="48x48" href="{{ asset('images/jq-icon.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/jq-logo.png') }}">
    <link rel="apple-touch-icon" sizes="152x152" href="{{ asset('images/jq-logo.png') }}">
    <link rel="apple-touch-icon" sizes="167x167" href="{{ asset('images/jq-logo.png') }}">
    <meta name="msapplication-TileImage" content="{{ asset('images/jq-logo.png') }}">
    <meta name="msapplication-TileColor" content="#e94560">

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- SweetAlert2 -->
    <link href="https://cdn.jsdelivr.net/npm/@sweetalert2/theme-dark@4/dark.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>

    <!-- Trix Editor -->
    <link rel="stylesheet" type="text/css" href="https://unpkg.com/trix@2.0.0/dist/trix.css">
    <script type="text/javascript" src="https://unpkg.com/trix@2.0.0/dist/trix.umd.min.js"></script>
    <style>
        /* Custom Trix Editor Styles */
        /* trix-toolbar {
            padding: 0.5rem;
            background-color: #f8f9fa;
            border-bottom: 1px solid #e2e8f0;
            border-top-left-radius: 0.375rem;
            border-top-right-radius: 0.375rem;
        }

        trix-toolbar .trix-button-group {
            margin-bottom: 0.25rem;
            border-radius: 0.25rem;
            overflow: hidden;
        }

        trix-toolbar .trix-button {
            border: 1px solid #d1d5db;
            margin: 0;
            background-color: white;
        }

        trix-toolbar .trix-button.trix-active {
            background-color: #e5e7eb;
        }

        trix-toolbar .trix-button:hover:not(.trix-active) {
            background-color: #f3f4f6;
        }

        trix-editor {
            padding: 1rem;
            min-height: 200px;
            max-height: 600px;
            overflow-y: auto;
        } */

        /* Fix for cursor jumping */
        trix-editor [data-trix-cursor-target] {
            display: inline !important;
        }
    </style>

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js', 'resources/js/document-editor.js'])

    <!-- Styles -->
    @livewireStyles
    @stack('styles')

    <!-- Add this in the head section of your layout file -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css"
        integrity="sha512-z3gLpd7yknf1YoNbCzqRKc4qyor8gaKU1qmn+CShxbuBusANI9QpRohGBreCFkKxLhei6S9CQXFEbbKuqLg0DA=="
        crossorigin="anonymous" referrerpolicy="no-referrer" />

    <!-- Google Places API -->
    <script src="https://maps.googleapis.com/maps/api/js?key={{ env('GOOGLE_MAPS_API_KEY') }}&libraries=places" defer>
    </script>

<body class="font-sans antialiased" :data-theme="$store.theme.current">
    <x-banner />

    <div class="min-h-screen bg-base-200 dark:bg-neutral-focus">
        @if(auth()->check())
            @livewire('navigation-menu')
        @endif
        <!-- Page Heading -->
        @if (isset($header))
            <header class="shadow bg-base-300">
                <div class="px-4 py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
                    {{ $header }}
                </div>
            </header>
        @endif

        <!-- Page Content -->
        <main>
            <div class="container px-4 py-4 mx-auto sm:px-6 lg:px-8">
                <x-flash-messages />
            </div>
            {{ $slot }}
        </main>
    </div>

    @stack('modals')
    <x-markdown-modal />
    @livewireScripts
    <x-alpine-scripts />
    @stack('scripts')

    <!-- DocxJS Preview -->
    <script src="https://unpkg.com/jszip/dist/jszip.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/docx-preview@0.3.5/dist/docx-preview.min.js"></script>
    <script src="{{ asset('js/docx-preview.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/docx-test.js') }}?v={{ time() }}"></script>
    <script src="{{ asset('js/text-preview.js') }}?v={{ time() }}"></script>

    <!-- Markdown Support -->
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <style>
        .markdown-body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Helvetica, Arial, sans-serif;
            font-size: 16px;
            line-height: 1.5;
            word-wrap: break-word;
        }

        .markdown-body h1,
        .markdown-body h2,
        .markdown-body h3,
        .markdown-body h4,
        .markdown-body h5,
        .markdown-body h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }

        .markdown-body h1 {
            font-size: 2em;
        }

        .markdown-body h2 {
            font-size: 1.5em;
        }

        .markdown-body h3 {
            font-size: 1.25em;
        }

        .markdown-body p,
        .markdown-body blockquote,
        .markdown-body ul,
        .markdown-body ol,
        .markdown-body dl,
        .markdown-body table,
        .markdown-body pre {
            margin-top: 0;
            margin-bottom: 16px;
        }

        .markdown-body code {
            padding: 0.2em 0.4em;
            margin: 0;
            font-size: 85%;
            background-color: rgba(175, 184, 193, 0.2);
            border-radius: 6px;
        }

        .markdown-body pre {
            padding: 16px;
            overflow: auto;
            font-size: 85%;
            line-height: 1.45;
            background-color: #f6f8fa;
            border-radius: 6px;
        }

        .markdown-body pre code {
            padding: 0;
            background-color: transparent;
        }

        .markdown-body blockquote {
            padding: 0 1em;
            color: #57606a;
            border-left: 0.25em solid #d0d7de;
        }
    </style>

    <script>
        console.log('DocxJS Preview: Scripts loaded in layout');
        // Add a global function to test docx preview with any URL
        window.testDocxWithUrl = function(url) {
            if (typeof testDocxRendering === 'function') {
                testDocxRendering(url);
            } else {
                alert('Test function not available');
            }
        };
    </script>
</body>

</html>
