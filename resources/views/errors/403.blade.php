<x-app-layout>
    <div class="flex items-center justify-center min-h-screen">
        <div class="max-w-md p-8 text-center card bg-base-200">
            <div class="card-body">
                <div class="flex justify-center mb-6">
                    <div class="p-4 rounded-full bg-error/10">
                        <svg class="w-16 h-16 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                    </div>
                </div>
                <h2 class="mb-4 text-3xl font-bold">{{ __('Access Denied') }}</h2>
                <p class="mb-6 text-base-content/70">
                    {{ __('You don\'t have permission to access this page.') }}
                </p>
                <div class="justify-center space-x-4 card-actions">
                    <a href="{{ route('dashboard') }}" class="btn btn-primary">
                        {{ __('Go to Dashboard') }}
                    </a>
                    <a href="{{ url()->previous() }}" class="btn btn-outline">
                        {{ __('Go Back') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>