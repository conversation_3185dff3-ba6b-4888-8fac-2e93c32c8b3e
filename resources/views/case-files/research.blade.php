<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
            <h2 class="text-xl font-semibold leading-tight text-base-content flex items-center gap-2">
                <span class="text-2xl">🔍</span>
                {{ __('research.title') }} - {{ $caseFile->title }}
            </h2>
            <a href="{{ route('case-files.show', $caseFile) }}" class="btn btn-sm btn-outline">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                {{ __('common.back_to_case') }}
            </a>
        </div>
    </x-slot>

    <div class="py-6 sm:py-12">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <livewire:case-files.research-items :case-file="$caseFile" />
        </div>
    </div>
</x-app-layout>
