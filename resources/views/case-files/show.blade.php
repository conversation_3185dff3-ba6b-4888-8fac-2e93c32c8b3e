<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
            <h2 class="text-xl font-semibold leading-tight text-base-content/80 flex items-center gap-2">
                <span class="text-2xl">📋</span>
                {{ __('cases.details') }}
            </h2>
            <div class="flex flex-wrap items-center gap-2 justify-center sm:justify-end w-full sm:w-auto">
                <a href="{{ route('dashboard') }}"
                   class="btn btn-ghost btn-sm whitespace-nowrap">
                    ← {{ __('cases.actions.back_to_dashboard') }}
                </a>
                <a href="{{ route('case-files.docket', $caseFile) }}"
                   class="btn btn-sm btn-ghost hover:btn-primary whitespace-nowrap">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    <span>{{ __('docket.navigation.view_docket') }}</span>
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
            <livewire:case-files.show :case-file="$caseFile" />
        </div>
    </div>
    <livewire:chat.chat-interface />

    <!-- Video Call Modal -->
{{--    <x-video-call-modal id="case-video-call-modal" />--}}

    <script>
        function startCaseVideoCall(caseId) {
            // Make an AJAX request to create the meeting
            fetch('{{ route("video-meetings.create") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    type: 'case',
                    id: caseId,
                    name: 'Case Collaboration'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Open the meeting in a new tab
                    window.open(data.meeting_url, '_blank');
                } else {
                    console.error('Failed to create meeting:', data.error);
                    alert('Failed to create meeting. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error creating meeting:', error);
                alert('An error occurred. Please try again.');
            });
        }
    </script>
</x-app-layout>
