<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
            <h2 class="text-xl font-semibold leading-tight text-base-content/80 flex items-center gap-2 text-center sm:text-left">
                <span class="text-2xl">📄</span>
                <span class="truncate">{{ __('documents.document_management') }} <br> <i class="font-medium text-gray-500">{{ $caseFile->title }}</i></span>
            </h2>
            <div class="flex flex-wrap items-center gap-2 justify-center sm:justify-end w-full sm:w-auto">
                <a href="{{ route('case-files.show', $caseFile) }}"
                   class="btn btn-ghost btn-sm whitespace-nowrap">
                    ← {{ __('documents.back_to_case') }}
                </a>

                <a href="{{ route('case-files.drafts.index', $caseFile) }}"
                   class="btn btn-ghost btn-sm whitespace-nowrap">
                    ← {{ __('Back to drafts') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
            <div class="p-6 overflow-hidden bg-base-100 shadow-xl sm:rounded-lg">
                <!-- Document Management Interface -->
                <livewire:document-bucket :case-file="$caseFile" />
            </div>
        </div>
    </div>
</x-app-layout>
