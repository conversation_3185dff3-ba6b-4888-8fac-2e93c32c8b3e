<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="flex items-center gap-2 text-xl font-semibold leading-tight text-base-content/80">
                <span class="text-2xl">📄</span>
                {{ $document->title }}
            </h2>
            @php($caseFile = $document->caseFile)
            <div class="flex items-center gap-2">
                <a href="{{ route('case-files.drafts.index', $caseFile) }}" class="btn btn-ghost btn-sm">
                    ← {{ __('Back to Drafts') }}
                </a>
                <a href="{{ route('case-files.documents.index', $caseFile) }}" class="btn btn-ghost btn-sm">
                    ← {{ __('Back to Documents') }}
                </a>
                <a href="{{ route('documents.download', $document) }}" class="btn btn-primary btn-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                    </svg>
                    Download
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
            <div class="p-6 overflow-hidden shadow-xl bg-base-100 sm:rounded-lg">
                <!-- Document Details -->
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-base-content">Document Details</h3>
                    <div class="grid grid-cols-1 gap-4 mt-2 md:grid-cols-2">
                        <div>
                            <p class="text-sm text-base-content/70">Title</p>
                            <p class="font-medium">{{ $document->title }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-base-content/70">File Type</p>
                            <p class="font-medium">
                                {{ strtoupper(pathinfo($document->original_filename, PATHINFO_EXTENSION)) }}</p>
                        </div>
                        <div>
                            <p class="text-sm text-base-content/70">Created</p>
                            <p class="font-medium">{{ $document->created_at->format('M d, Y h:i A') }}</p>
                        </div>
                        @if ($document->file_size)
                            <div>
                                <p class="text-sm text-base-content/70">Size</p>
                                <p class="font-medium">{{ $document->human_file_size }}</p>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Document Preview -->
                <div class="mt-6">
                    <h3 class="mb-4 text-lg font-medium text-base-content">Document Preview</h3>

                    @if (str_starts_with($document->mime_type, 'image/'))
                        <img src="{{ Storage::url('public/' . $document->storage_path) }}" alt="{{ $document->title }}"
                            class="max-w-full rounded-lg shadow-md">
                    @elseif(str_starts_with($document->mime_type, 'audio/'))
                        <audio controls class="w-full">
                            <source src="{{ Storage::url('public/' . $document->storage_path) }}"
                                type="{{ $document->mime_type }}">
                            {{ __('Audio not supported') }}
                        </audio>

                        @if ($document->transcription)
                            <div class="p-4 mt-4 rounded-lg bg-base-200">
                                <h5 class="text-sm font-semibold text-base-content/70">Transcription</h5>
                                <div class="mt-2 text-sm whitespace-pre-wrap text-base-content/60">
                                    {{ $document->transcription }}</div>
                            </div>
                        @endif
                    @elseif(str_starts_with($document->mime_type, 'video/'))
                        <video controls class="w-full rounded-lg shadow-md">
                            <source src="{{ Storage::url('public/' . $document->storage_path) }}"
                                type="{{ $document->mime_type }}">
                            {{ __('Video not supported') }}
                        </video>

                        @if ($document->transcription)
                            <div class="p-4 mt-4 rounded-lg bg-base-200">
                                <h5 class="text-sm font-semibold text-base-content/70">Transcription</h5>
                                <div class="mt-2 text-sm whitespace-pre-wrap text-base-content/60">
                                    {{ $document->transcription }}</div>
                            </div>
                        @endif
                    @elseif(str_contains($document->mime_type, 'pdf'))
                        <div class="p-4 text-center rounded-lg bg-base-200">
                            <p class="mb-4">PDF preview is not available. Please download the document to view it.</p>
                            <a href="{{ route('documents.download', $document) }}" class="btn btn-primary">
                                Download PDF
                            </a>
                        </div>
                    @elseif(str_contains($document->mime_type, 'word') || str_contains($document->mime_type, 'officedocument'))
                        <div class="p-4 text-center rounded-lg bg-base-200">
                            <p class="mb-4">Word document preview is not available. Please download the document to
                                view it.</p>
                            <a href="{{ route('documents.download', $document) }}" class="btn btn-primary">
                                Download Document
                            </a>
                        </div>
                    @else
                        <div class="p-4 text-center rounded-lg bg-base-200">
                            <p class="mb-4">Preview not available for this file type. Please download the document to
                                view it.</p>
                            <a href="{{ route('documents.download', $document) }}" class="btn btn-primary">
                                Download File
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
