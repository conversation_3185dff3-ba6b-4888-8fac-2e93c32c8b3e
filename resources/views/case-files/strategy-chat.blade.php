<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold leading-tight text-base-content/80 flex items-center gap-2">
                <span class="text-2xl">⚖️</span>
                {{ __('strategy.title') }} - {{ __('strategy.conversation.title') }}
            </h2>
            <div class="flex gap-2">
                <a href="{{ route('case-files.strategy', $caseFile) }}"
                   class="btn btn-ghost btn-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    {{ __('strategy.guided_mode') }}
                </a>
                <a href="{{ route('case-files.show', $caseFile) }}"
                   class="btn btn-ghost btn-sm">
                    ← {{ __('cases.actions.back_to_case') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="mx-auto max-w-7xl sm:px-6 lg:px-8">
            <div class="bg-base-100 shadow-xl sm:rounded-lg overflow-hidden">
                <div class="p-6 border-b border-base-content/10">
                    <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
                        <div class="space-y-1">
                            <h3 class="text-2xl font-medium text-base-content">
                                {{ __('strategy.conversation.title') }}
                            </h3>
                            <p class="text-base text-base-content/60">
                                {{ __('strategy.conversation.subtitle') }}
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="h-[700px]">
                    <livewire:chat.strategy-chat-interface :caseFileId="$caseFile->id" />
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
