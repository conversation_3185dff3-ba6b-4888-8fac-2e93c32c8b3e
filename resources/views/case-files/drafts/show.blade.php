<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-base-content leading-tight">
                <span class="capitalize">{{ $draft->draft_type }}</span> {{ __('app.draft') }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('case-files.drafts.index', $caseFile) }}" class="btn btn-ghost btn-sm">
                    ← {{ __('app.back_to_drafts') }}
                </a>
                <a href="{{ route('case-files.drafts.edit', [$caseFile, $draft]) }}" class="btn btn-primary btn-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    {{ __('app.edit_draft') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Draft Details Card -->
            <div class="bg-base-100 overflow-hidden shadow-xl sm:rounded-lg mb-6">
                <div class="p-6">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-medium text-base-content">{{ __('app.draft_details') }}</h3>
                            <p class="text-sm text-base-content/70 mt-1">
                                {{ $draft->description ?? __('app.no_description_provided') }}</p>
                        </div>
                        <span
                            class="badge {{ $draft->status === 'draft' ? 'badge-primary' : ($draft->status === 'review' ? 'badge-warning' : 'badge-success') }} capitalize">
                            {{ $draft->status }}
                        </span>
                    </div>

                    <div class="divider"></div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 class="text-sm font-medium text-base-content/70">{{ __('app.document_type') }}</h4>
                            <p class="capitalize">{{ $draft->draft_type }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-base-content/70">{{ __('app.created') }}</h4>
                            <p>{{ $draft->created_at->format('M d, Y g:i A') }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-base-content/70">{{ __('app.last_updated') }}</h4>
                            <p>{{ $draft->updated_at->format('M d, Y g:i A') }}</p>
                        </div>
                        @if ($draft->published_at)
                            <div>
                                <h4 class="text-sm font-medium text-base-content/70">{{ __('app.published') }}</h4>
                                <p>{{ $draft->published_at->format('M d, Y g:i A') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Draft Content Card -->
            <div id="editor" class="bg-base-100 overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg font-medium text-base-content">{{ __('app.document_content') }}</h3>
                        <div class="flex space-x-2">
                            <div class="dropdown dropdown-end">
                                <label tabindex="0" class="btn btn-primary btn-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                    </svg>
                                    {{ __('app.open_editor') }}
                                </label>
                                <ul tabindex="0"
                                    class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                    <li>
                                        <a href="#editor" class="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                            </svg>
                                            {{ __('app.basic_editor') }}
                                        </a>
                                    </li>
                                    <li>
                                        <a href="{{ route('drafts.ai-editor', [$caseFile, $draft]) }}"
                                            class="flex items-center">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none"
                                                viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                                            </svg>
                                            {{ __('app.ai_powered_editor') }}
                                        </a>
                                    </li>
                                </ul>
                            </div>
                            <button class="btn btn-outline btn-sm">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                                </svg>
                                {{ __('app.download') }}
                            </button>
                        </div>
                    </div>

                    @livewire('drafts.draft-editor', ['caseFile' => $caseFile, 'draft' => $draft])
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
