<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
            <h2 class="text-xl font-semibold leading-tight text-base-content">
                <span class="capitalize">{{ $draft->draft_type }}</span> {{ __('app.draft_details') }}
            </h2>
            <div class="flex flex-wrap gap-2">
                <a href="{{ route('case-files.drafts.index', $caseFile) }}" class="btn btn-ghost btn-sm">
                    ← {{ __('app.back_to_drafts') }}
                </a>
                <a href="{{ route('case-files.drafts.show', [$caseFile, $draft]) }}" class="btn btn-primary btn-sm">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 sm:w-5 sm:h-5 mr-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                    </svg>
                    {{ __('app.open_editor') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-6 sm:py-12">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <!-- Draft Details Card -->
            <div class="mb-6 overflow-hidden shadow-xl bg-base-100 rounded-lg">
                <div class="p-4 sm:p-6">
                    <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-2 sm:gap-0">
                        <div class="mb-2 sm:mb-0">
                            <h3 class="text-lg font-medium text-base-content">{{ __('app.draft_details') }}</h3>
                            <p class="mt-1 text-sm text-base-content/70">
                                {{ $draft->description ?? __('app.no_description_provided') }}</p>
                        </div>
                        <span
                            class="badge badge-outline {{ $draft->status === 'draft' ? 'badge-primary' : ($draft->status === 'review' ? 'badge-warning' : 'badge-success') }} capitalize self-start sm:self-center">
                            {{ $draft->status }}
                        </span>
                    </div>

                    <div class="divider my-2 sm:my-4"></div>

                    <div class="grid grid-cols-1 gap-y-3 gap-x-4 sm:grid-cols-2">
                        <div>
                            <h4 class="text-sm font-medium text-base-content/70">{{ __('app.document_type') }}</h4>
                            <p class="capitalize">{{ $draft->draft_type }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-base-content/70">{{ __('app.created') }}</h4>
                            <p>{{ $draft->created_at->format('M d, Y g:i A') }}</p>
                        </div>
                        <div>
                            <h4 class="text-sm font-medium text-base-content/70">{{ __('app.last_updated') }}</h4>
                            <p>{{ $draft->updated_at->format('M d, Y g:i A') }}</p>
                        </div>
                        @if ($draft->published_at)
                            <div>
                                <h4 class="text-sm font-medium text-base-content/70">{{ __('app.published') }}</h4>
                                <p>{{ $draft->published_at->format('M d, Y g:i A') }}</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Actions Card -->
            <div class="overflow-hidden shadow-xl bg-base-100 rounded-lg">
                <div class="p-4 sm:p-6">
                    <h3 class="mb-4 text-lg font-medium text-base-content">{{ __('app.actions') }}</h3>

                    <div class="grid grid-cols-1 sm:flex sm:flex-wrap gap-3 sm:gap-4">
                        <a href="{{ route('case-files.drafts.show', [$caseFile, $draft]) }}" class="btn btn-primary btn-sm sm:btn-md w-full sm:w-auto">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                            </svg>
                            {{ __('app.open_in_editor') }}
                        </a>

                        <a href="{{ route('case-files.drafts.edit', [$caseFile, $draft]) }}" class="btn btn-outline btn-sm sm:btn-md w-full sm:w-auto">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            {{ __('app.edit_properties') }}
                        </a>

                        <button type="button" class="btn btn-outline btn-sm sm:btn-md w-full sm:w-auto"
                            onclick="document.getElementById('generate-document-modal-{{ $draft->id }}').classList.add('modal-open')">
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            {{ __('app.generate_document') }}
                        </button>

                        <x-exhibits-modal :draft="$draft" :caseFile="$caseFile" />

                        <form action="{{ route('case-files.drafts.destroy', [$caseFile, $draft]) }}" method="POST"
                            class="w-full sm:w-auto">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-error btn-sm sm:btn-md w-full"
                                onclick="return confirm('{{ __('app.delete_draft_confirm') }}')">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                {{ __('app.delete_draft') }}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function showTab(tabId) {
            // Hide all tab contents
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.add('hidden');
            });

            // Remove active class from all tabs
            document.querySelectorAll('.tab').forEach(tab => {
                tab.classList.remove('tab-active');
            });

            // Show the selected tab content
            document.getElementById(tabId + '-tab').classList.remove('hidden');

            // Add active class to the clicked tab
            event.target.classList.add('tab-active');
        }
    </script>
</x-app-layout>
