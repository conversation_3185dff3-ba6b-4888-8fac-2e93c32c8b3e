<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row justify-between items-center gap-4">
            <h2 class="font-semibold text-xl text-base-content leading-tight text-center sm:text-left">
                <span class="block sm:inline">{{ __('app.draft_documents') }}</span>
                <span class="block sm:inline sm:ml-1 truncate max-w-[250px] sm:max-w-md text-gray-500 text-sm"> <i>{{ $caseFile->title }}</i></span>
            </h2>
            <div class="flex flex-wrap justify-center sm:justify-end gap-2 w-full sm:w-auto">
                <a href="{{ route('case-files.show', $caseFile) }}" class="btn btn-ghost btn-sm whitespace-nowrap">
                    ← {{ __('app.back_to_case') }}
                </a>
                <a href="{{ route('case-files.drafts.create', $caseFile) }}" class="btn btn-primary btn-sm whitespace-nowrap">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    {{ __('app.new_draft') }}
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100 overflow-hidden shadow-xl sm:rounded-lg">
                <div class="p-6">
                    @if ($drafts->isEmpty())
                        <div class="text-center py-8">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-base-content/30"
                                fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <h3 class="mt-4 text-lg font-medium text-base-content">{{ __('app.no_drafts_yet') }}</h3>
                            <p class="mt-1 text-sm text-base-content/70">
                                {{ __('app.get_started_draft') }}</p>
                            <div class="mt-6">
                                <a href="{{ route('case-files.drafts.create', $caseFile) }}" class="btn btn-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 4v16m8-8H4" />
                                    </svg>
                                    {{ __('app.create_draft') }}
                                </a>
                            </div>
                        </div>
                    @else
                        <!-- Define type icons once for reuse -->
                        @php
                            $typeIcons = [
                                'complaint' =>
                                    '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>',
                                'motion' =>
                                    '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" /></svg>',
                                'affidavit' =>
                                    '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>',
                                'letter' =>
                                    '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>',
                                'contract' =>
                                    '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg>',
                            ];
                            $defaultIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" /></svg>';

                            $statusColors = [
                                'draft' => 'badge-primary',
                                'review' => 'badge-warning',
                                'published' => 'badge-success',
                            ];
                        @endphp

                        <!-- Desktop Table (hidden on mobile) -->
                        <div class="hidden md:block overflow-x-auto">
                            <table class="table w-full">
                                <thead>
                                    <tr>
                                        <th>{{ __('app.type') }}</th>
                                        <th>{{ __('app.description') }}</th>
                                        <th>{{ __('app.status') }}</th>
                                        <th>{{ __('app.created') }}</th>
                                        <th>{{ __('app.actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($drafts as $draft)
                                        <tr class="hover">
                                            <td class="font-medium">
                                                @php
                                                    $icon = $typeIcons[$draft->draft_type] ?? $defaultIcon;
                                                @endphp
                                                <div class="flex items-center">
                                                    {!! $icon !!}
                                                    <span class="capitalize">{{ $draft->draft_type }}</span>
                                                </div>
                                            </td>
                                            <td>{{ $draft->description ?? __('app.no_description') }}</td>
                                            <td>
                                                @php
                                                    $badgeClass = $statusColors[$draft->status] ?? 'badge-ghost';
                                                @endphp
                                                <span class="badge {{ $badgeClass }} capitalize">{{ $draft->status }}</span>
                                            </td>
                                            <td>{{ $draft->created_at->format('M d, Y') }}</td>
                                            <td>
                                                <div class="flex space-x-2">
                                                    <a href="{{ route('case-files.drafts.show', [$caseFile, $draft]) }}"
                                                        class="btn btn-sm btn-primary" title="Open in Editor">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4"
                                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                        </svg>
                                                    </a>
                                                    <a href="{{ route('case-files.drafts.details', [$caseFile, $draft]) }}"
                                                        class="btn btn-sm btn-ghost" title="View Details">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4"
                                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                        </svg>
                                                    </a>
{{--                                                    <a href="{{ route('case-files.drafts.edit', [$caseFile, $draft]) }}"--}}
{{--                                                        class="btn btn-sm btn-ghost">--}}
{{--                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4"--}}
{{--                                                            fill="none" viewBox="0 0 24 24" stroke="currentColor">--}}
{{--                                                            <path stroke-linecap="round" stroke-linejoin="round"--}}
{{--                                                                stroke-width="2"--}}
{{--                                                                d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />--}}
{{--                                                        </svg>--}}
{{--                                                    </a>--}}
                                                    <form
                                                        action="{{ route('case-files.drafts.destroy', [$caseFile, $draft]) }}"
                                                        method="POST" class="inline">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-ghost"
                                                            onclick="return confirm('{{ __('app.delete_draft_confirmation') }}')">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4"
                                                                fill="none" viewBox="0 0 24 24"
                                                                stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round"
                                                                    stroke-width="2"
                                                                    d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                            </svg>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Mobile Card Layout (hidden on desktop) -->
                        <div class="md:hidden space-y-4">
                            @foreach ($drafts as $draft)
                                @php
                                    $icon = $typeIcons[$draft->draft_type] ?? $defaultIcon;
                                    $badgeClass = $statusColors[$draft->status] ?? 'badge-ghost';
                                @endphp
                                <div class="card bg-base-100 shadow-sm border border-base-200 hover:shadow-md transition-shadow duration-200">
                                    <div class="card-body p-4">
                                        <!-- Header with Type and Status -->
                                        <div class="flex justify-between items-start mb-2">
                                            <div class="flex items-center">
                                                {!! $icon !!}
                                                <span class="font-medium capitalize">{{ $draft->draft_type }}</span>
                                            </div>
                                            <span class="badge {{ $badgeClass }} capitalize">{{ $draft->status }}</span>
                                        </div>

                                        <!-- Description -->
                                        <div class="mb-3">
                                            <div class="text-sm font-medium text-base-content/70 mb-1">{{ __('app.description') }}:</div>
                                            <p class="text-sm">{{ $draft->description ?? __('app.no_description') }}</p>
                                        </div>

                                        <!-- Date -->
                                        <div class="text-xs text-base-content/60 mb-3">
                                            {{ __('app.created') }}: {{ $draft->created_at->format('M d, Y') }}
                                        </div>

                                        <!-- Actions -->
                                        <div class="flex flex-wrap gap-2 justify-between items-center pt-2 border-t border-base-200">
                                            <div class="flex gap-1">
                                                <a href="{{ route('case-files.drafts.show', [$caseFile, $draft]) }}"
                                                    class="btn btn-sm btn-primary" title="Open in Trix Editor">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                    </svg>
                                                    <span class="text-xs">Edit</span>
                                                </a>
                                                <a href="{{ route('case-files.drafts.details', [$caseFile, $draft]) }}"
                                                    class="btn btn-sm btn-ghost" title="View Details">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    <span class="text-xs">Details</span>
                                                </a>
                                            </div>
                                            <div class="flex gap-1">
                                                <a href="{{ route('case-files.drafts.edit', [$caseFile, $draft]) }}"
                                                    class="btn btn-sm btn-ghost">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4"
                                                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                    </svg>
                                                    <span class="text-xs">Settings</span>
                                                </a>
                                                <form
                                                    action="{{ route('case-files.drafts.destroy', [$caseFile, $draft]) }}"
                                                    method="POST" class="inline">
                                                    @csrf
                                                    @method('DELETE')
                                                    <button type="submit" class="btn btn-sm btn-ghost text-error"
                                                        onclick="return confirm('{{ __('app.delete_draft_confirmation') }}')">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4"
                                                            fill="none" viewBox="0 0 24 24"
                                                            stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round"
                                                                stroke-width="2"
                                                                d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                        </svg>
                                                        <span class="text-xs">Delete</span>
                                                    </button>
                                                </form>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
