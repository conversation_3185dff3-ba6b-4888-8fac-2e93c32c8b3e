<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" x-data="{ mobileMenuOpen: false, darkMode: false, showLoginModal: false, showRegisterModal: false, is_attorney: false, terms: false }" :class="{ 'dark': darkMode }">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Justice Quest: AI-powered legal assistant helping you fight for your rights with document generation, case management, and legal research tools.">
    <title>{{ "JUSTICE QUEST: AI Legal Platform" }}</title>
    <!-- Favicon -->
    <link rel="icon" href="{{ asset('images/jq-icon.ico') }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ asset('images/jq-icon.ico') }}" type="image/x-icon">
    <!-- Larger Icons for Different Devices -->
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('images/en/JQ.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('images/en/JQ.png') }}">
    <link rel="icon" type="image/png" sizes="48x48" href="{{ asset('images/en/JQ.png') }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('images/en/JQ.png') }}">
    <link rel="apple-touch-icon" sizes="152x152" href="{{ asset('images/en/JQ.png') }}">
    <link rel="apple-touch-icon" sizes="167x167" href="{{ asset('images/en/JQ.png') }}">
    <meta name="msapplication-TileImage" content="{{ asset('images/en/JQ.png') }}">
    <meta name="msapplication-TileColor" content="#e94560">
    <!-- Always use the compiled Tailwind CSS for consistency. -->
    @vite(['resources/css/app.css'])

    <!-- Fallback to CDN if compiled CSS fails to load (helpful during development) -->

{{--    <script defer src="https://cdn.jsdelivr.net/npm/alpinejs@3.x.x/dist/cdn.min.js"></script>--}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Press+Start+2P&family=Russo+One&display=swap" rel="stylesheet">

    <!-- Google Maps API for ZIP code autocomplete -->
    <script src="https://maps.googleapis.com/maps/api/js?key={{ config('services.google.places_api_key') }}&libraries=places&loading=async"></script>

    <!-- YouTube API - Preload -->
    <script>
        // Preload YouTube API as early as possible
        var tag = document.createElement('script');
        tag.src = "https://www.youtube.com/iframe_api";
        var firstScriptTag = document.getElementsByTagName('script')[0];
        firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);
    </script>

    @livewireStyles
    @livewireScripts
    <style>
        body {
            font-family: 'Russo One', sans-serif;
            background-color: #111;
            color: #fff;
        }

        .street-gradient {
            background: linear-gradient(135deg, #e8ebff 0%, #16213e 50%, #0f3460 100%);
        }

        .dark .street-gradient {
            background: linear-gradient(135deg, #0f0f1a 0%, #0d1226 50%, #081224 100%);
        }

        .fight-card {
            background: rgba(30, 30, 46, 0.8);
            border: 2px solid #e94560;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(233, 69, 96, 0.3);
            transition: all 0.3s ease;
        }

        .fight-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(233, 69, 96, 0.5);
        }

        .pixel-text {
            font-family: 'Press Start 2P', cursive;
            text-shadow: 3px 3px 0 #e94560;
        }

        .fight-btn {
            position: relative;
            overflow: hidden;
            background: linear-gradient(45deg, #e94560, #ff6b6b);
            border: none;
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s;
        }

        .fight-btn::after {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(transparent, rgba(255,255,255,0.2), transparent);
            transform: rotate(45deg);
            transition: all 0.6s;
        }

        .fight-btn:hover::after {
            left: 100%;
        }

        .health-bar {
            height: 6px;
            background: linear-gradient(90deg, #e94560, #ff6b6b);
            border-radius: 3px;
            box-shadow: 0 0 10px rgba(233, 69, 96, 0.7);
            position: relative;
            overflow: hidden;
        }

        .health-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: health-bar-shine 2s infinite linear;
        }

        @keyframes health-bar-shine {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        .combo-counter {
            position: absolute;
            top: -20px;
            right: -10px;
            background: #e94560;
            color: white;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 10px;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }

        .hit-effect {
            animation: hit 0.3s ease;
        }

        @keyframes hit {
            0% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            50% { transform: translateX(5px); }
            100% { transform: translateX(0); }
        }

        .vs-badge {
            position: relative;
            padding: 4px 12px;
            background: linear-gradient(45deg, #e94560, #ff6b6b);
            color: white;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
            box-shadow: 0 4px 15px rgba(233, 69, 96, 0.4);
        }

        .character-select {
            border: 3px solid #e94560;
            border-radius: 8px;
            transition: all 0.3s;
        }

        .character-select:hover {
            transform: scale(1.05);
            box-shadow: 0 0 20px rgba(233, 69, 96, 0.6);
        }

        .character-select.active {
            border-color: #00ff88;
            box-shadow: 0 0 20px rgba(0, 255, 136, 0.6);
        }

        .damage-text {
            position: absolute;
            color: #e94560;
            font-weight: bold;
            text-shadow: 2px 2px 0 #000;
            animation: floatUp 1s ease-out forwards;
        }

        @keyframes floatUp {
            0% { transform: translateY(0); opacity: 1; }
            100% { transform: translateY(-50px); opacity: 0; }
        }

        @keyframes pulse-subtle {
            0% { transform: scale(1); filter: brightness(1); }
            50% { transform: scale(1.03); filter: brightness(1.1); }
            100% { transform: scale(1); filter: brightness(1); }
        }

        .animate-pulse-subtle {
            animation: pulse-subtle 3s ease-in-out infinite;
        }
    </style>
</head>
<body class="street-gradient" wire:init>
    <!-- Intro Video Container -->
    <div id="intro-video-container" class="fixed inset-0 z-50 flex items-center justify-center overflow-hidden bg-black">
        <style>
            /* Mobile-specific video styles */
            @media (max-width: 768px) {
                #intro-video {
                    width: 100%;
                    height: 100%;
                    max-height: 100vh;
                    max-width: 100vw;
                }

                /* Ensure video maintains aspect ratio on mobile */
                #intro-video-container {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
            }

            /* Portrait orientation specific styles */
            @media (orientation: portrait) {
                #intro-video {
                    object-fit: contain !important;
                    /* Force contain in portrait mode */
                }
            }
        </style>
        <!-- CRT screen effect -->
        <div class="absolute inset-0 z-0 overflow-hidden pointer-events-none opacity-40" id="crt-effect">
            <div class="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-black opacity-20"></div>
            <div
                class="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_rgba(0,0,0,0)_0%,_rgba(0,0,0,0.8)_80%)] opacity-30">
            </div>
        </div>
        <!-- Video game-like overlay elements -->
        <div id="video-overlay" class="absolute inset-0 z-10 overflow-hidden pointer-events-none">
            <div class="absolute px-4 py-2 text-white transition-opacity duration-500 transform -translate-x-1/2 rounded-md opacity-0 bottom-4 left-1/2 bg-black/50 backdrop-blur-sm"
                id="loading-text">
                {{ __('welcome.media.loading') }}
            </div>
            <div class="absolute top-0 left-0 w-full h-1 transition-transform duration-300 ease-linear origin-left transform scale-x-0 bg-gradient-to-r from-yellow-500 to-red-500"
                id="progress-bar"></div>
        </div>

        <video id="intro-video" class="object-contain w-full h-full md:object-cover" playsinline muted>
            <source src="{{ asset('videos/jq_intro.mp4') }}" type="video/mp4">
            Your browser does not support the video tag.
        </video>

        <button id="skip-intro"
            class="absolute z-20 px-4 py-2 text-white transition-colors duration-300 transform border rounded-md bottom-4 left-4 bg-black/40 hover:bg-black/60 backdrop-blur-sm border-white/20 hover:scale-105">
            {{ __('welcome.media.skip') }} <span class="ml-1 text-xs opacity-70">{{ __('welcome.media.esc') }}</span>
        </button>
    </div>
    <!-- Fight Navigation -->
    <header class="fixed z-50 w-full shadow-lg bg-black/90 backdrop-blur-sm" x-data="{ mobileMenuOpen: false }">
        <nav class="container flex items-center justify-between px-6 py-3 mx-auto">
            <a href="#" class="flex items-center space-x-2">
                <img src="https://justicequest.us-mia-1.linodeobjects.com/public/images/en/JQ.png" alt="Justice Quest" class="h-10 animate-pulse-subtle">
            </a>

            <div class="items-center hidden space-x-6 md:flex">
                <a href="#features" class="font-bold text-gray-300 transition duration-300 hover:text-red-500 uppercase">{{ __('welcome.nav.features') }}</a>
                <a href="#how-it-works" class="font-bold text-gray-300 transition duration-300 hover:text-red-500 uppercase">{{ __('welcome.nav.how_it_works') }}</a>
                <a href="#pricing" class="font-bold text-gray-300 transition duration-300 hover:text-red-500 uppercase">{{ __('welcome.nav.pricing') }}</a>
                <button @click="darkMode = !darkMode" class="p-2 text-gray-300 rounded-full hover:text-yellow-400">
                    <i x-show="!darkMode" class="fas fa-moon"></i>
                    <i x-show="darkMode" class="fas fa-sun"></i>
                </button>
                <!-- Language Selector -->
                <div class="relative flex items-center ml-4">
                    <livewire:language-selector />
                </div>
                <button @click="showLoginModal = true" class="px-6 py-3 rounded-lg fight-btn">
                    <i class="mr-2 fas fa-sign-in-alt"></i> {{ __('welcome.nav.login') }}
                </button>
                <button @click="showRegisterModal = true" class="px-6 py-3 rounded-lg fight-btn">
                    <i class="mr-2 fas fa-user-plus"></i> {{ __('welcome.nav.register') }}
                </button>
            </div>

            <div class="md:hidden">
                <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-gray-300 focus:outline-none">
                    <i class="text-2xl fas fa-bars"></i>
                </button>
            </div>
        </nav>

        <!-- Mobile Menu -->
        <div x-show="mobileMenuOpen" x-cloak class="absolute z-50 w-full md:hidden bg-black/95">
            <div class="container px-6 py-4 mx-auto">
                <a href="#features" @click="mobileMenuOpen = false" class="block py-3 font-bold text-gray-300 border-b border-gray-800 hover:text-red-500">{{ __('welcome.nav.features') }}</a>
                <a href="#how-it-works" @click="mobileMenuOpen = false" class="block py-3 font-bold text-gray-300 border-b border-gray-800 hover:text-red-500">{{ __('welcome.nav.how_it_works') }}</a>
                <a href="#characters" @click="mobileMenuOpen = false" class="block py-3 font-bold text-gray-300 border-b border-gray-800 hover:text-red-500">{{ __('welcome.nav.pricing') }}</a>
                <button @click="darkMode = !darkMode" class="block w-full py-3 font-bold text-left text-gray-300 border-b border-gray-800 hover:text-yellow-400">
                    <i x-show="!darkMode" class="mr-2 fas fa-moon"></i>
                    <i x-show="darkMode" class="mr-2 fas fa-sun"></i>
                    {{ __('welcome.nav.toggle_mode') }}
                </button>
                <!-- Language Selector -->
                <div class="block w-full py-3 font-bold text-left text-gray-300 border-b border-gray-800">
                    <livewire:language-selector />
                </div>
                <button @click="showLoginModal = true; mobileMenuOpen = false" class="block w-full px-6 py-3 mt-4 text-center rounded-lg fight-btn">
                    <i class="mr-2 fas fa-sign-in-alt"></i> {{ __('welcome.nav.login') }}
                </button>
                <button @click="showRegisterModal = true; mobileMenuOpen = false" class="block w-full px-6 py-3 mt-4 text-center rounded-lg fight-btn">
                    <i class="mr-2 fas fa-user-plus"></i> {{ __('welcome.nav.register') }}
                </button>
            </div>
        </div>
    </header>

    <!-- Fight Intro -->
    <section class="relative pt-32 pb-20 overflow-hidden">
        <div class="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjMDAwIj48L3JlY3Q+CjxwYXRoIGQ9Ik0wIDVMNSAwWk02IDRMNCA2Wk0tMSAxTDEgLTFaIiBzdHJva2U9IiNlOTQ1NjAiIHN0cm9rZS13aWR0aD0iMSI+PC9wYXRoPgo8L3N2Zz4=')] opacity-10"></div>

        <div class="container relative z-10 px-6 mx-auto">
            <div class="flex flex-col items-center md:flex-row">
                <div class="mb-12 md:w-1/2 md:mb-0">
                    <div class="mb-6 vs-badge block md:hidden text-center">
                        <i class="mr-2 fas fa-bolt"></i> {{ __('welcome.intro.badge') }}
                    </div>
                    <div class="mb-6 vs-badge hidden md:inline-block">
                        <i class="mr-2 fas fa-bolt"></i> {{ __('welcome.intro.badge') }}
                    </div>
                    <!-- Mobile Logo (visible on small screens) -->
                    <div class="flex justify-center mb-6 md:hidden">
                        <div class="relative">
                            <img src="https://justicequest.us-mia-1.linodeobjects.com/public/images/en/JQ.png" alt="Justice Quest" class="w-full max-w-md mx-auto transform rounded-lg rotate-2 animate-pulse-subtle">
                        </div>
                    </div>

                    <!-- Desktop Heading (hidden on small screens) -->
                    <h1 class="hidden mb-6 text-5xl font-bold leading-tight md:block md:text-4xl lg:text-5xl" style="/*text-shadow: 2px 2px 5px black; */">
                        <span class="text-red-500">{{ __('welcome.intro.liberty') }}</span>
                        <span class="text-white">{{ __('welcome.intro.and_justice') }}</span><br>
                        <span class="text-blue-500">{{ __('welcome.intro.for_all') }}</span>
                    </h1>
                    <p class="mb-8 text-2xl sm:text-xl text-white" style="text-shadow: 2px -1px 10px black;">
                        {!! __('welcome.intro.description') !!}
                    </p>
                    <div class="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                        <a href="#get-started" class="px-8 py-4 text-lg rounded-lg fight-btn">
                            <i class="mr-2 fas fa-gamepad"></i> {{ __('welcome.intro.start_fighting') }}
                        </a>
                        <a href="#features" class="px-8 py-4 font-bold text-center text-white transition border-2 border-red-500 rounded-lg bg-black/50 hover:bg-red-500/20">
                            <i class="mr-2 fas fa-list-ol"></i> {{ __('welcome.intro.your_tool_belt') }}
                        </a>
                    </div>
                </div>
                <div class="relative md:w-1/2 hidden md:block">
                    <div class="relative">
                        <img src="https://justicequest.us-mia-1.linodeobjects.com/public/images/en/JQ.png" alt="Justice Quest" class="w-full max-w-md mx-auto transform rounded-lg rotate-2 animate-pulse-subtle">
{{--                        <div class="absolute flex items-center px-6 py-2 font-bold text-white bg-red-500 rounded-lg shadow-lg -bottom-6 -right-6">--}}
{{--                            <i class="mr-2 fas fa-robot"></i> +1000 aura--}}
{{--                        </div>--}}
{{--                        <div class="absolute px-4 py-1 font-bold text-black bg-green-600 rounded-lg shadow-lg -top-6 -left-6">--}}
{{--                            <i class="fa fa-gavel"></i> MOTION GRANTED--}}
{{--                        </div>--}}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Fight Stats -->
{{--    <section class="py-12 bg-black/30">--}}
{{--        <div class="container px-6 mx-auto">--}}
{{--            <div class="grid grid-cols-2 gap-6 text-center md:grid-cols-4">--}}
{{--                <div class="p-6 fight-card">--}}
{{--                    <div class="mb-2 text-4xl font-bold text-red-500">1000+</div>--}}
{{--                    <div class="text-sm tracking-wider text-gray-300 uppercase">DOCS CREATED</div>--}}
{{--                    <div class="w-full mt-4 health-bar"></div>--}}
{{--                </div>--}}
{{--                <div class="p-6 fight-card">--}}
{{--                    <div class="mb-2 text-4xl font-bold text-red-500">24/7</div>--}}
{{--                    <div class="text-sm tracking-wider text-gray-300 uppercase">AI ASSIST</div>--}}
{{--                    <div class="w-full mt-4 health-bar"></div>--}}
{{--                </div>--}}
{{--                <div class="p-6 fight-card">--}}
{{--                    <div class="mb-2 text-4xl font-bold text-red-500">50+</div>--}}
{{--                    <div class="text-sm tracking-wider text-gray-300 uppercase">TEMPLATES</div>--}}
{{--                    <div class="w-full mt-4 health-bar"></div>--}}
{{--                </div>--}}
{{--                <div class="p-6 fight-card">--}}
{{--                    <div class="mb-2 text-4xl font-bold text-red-500">2</div>--}}
{{--                    <div class="text-sm tracking-wider text-gray-300 uppercase">LANGUAGES</div>--}}
{{--                    <div class="w-full mt-4 health-bar"></div>--}}
{{--                </div>--}}
{{--            </div>--}}
{{--        </div>--}}
{{--    </section>--}}

    <!-- Fight Features -->
    <section id="features" class="py-20 bg-black/20">
        <div class="container px-6 mx-auto">
            <div class="mb-16 text-center">
                <h2 class="mb-6 text-4xl font-bold md:text-5xl pixel-text">
                    {!! __('welcome.features.title') !!}
                </h2>
                <p class="max-w-3xl mx-auto text-xl text-gray-300">
                    {{ __('welcome.features.subtitle') }}
                </p>
            </div>

            <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                <!-- Move 1 -->
                <div class="p-8 fight-card" x-data="{ combo: 0 }" @click="combo++" :class="{ 'hit-effect': combo > 0 }">
                    <div class="flex items-center justify-center w-16 h-16 mb-6 text-3xl text-red-500 border-2 border-red-500 rounded-lg bg-red-500/20">
                        <i class="fas fa-robot"></i>
                    </div>
                    <h3 class="mb-4 text-2xl font-bold text-white">{{ __('welcome.features.tools.ai_assistant.title') }}</h3>
                    <p class="mb-6 text-gray-300">
                        {{ __('welcome.features.tools.ai_assistant.description') }}
                    </p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-400">{{ __('welcome.features.tools.ai_assistant.type') }}</span>
                        <span class="font-bold text-red-500">{{ __('welcome.features.tools.ai_assistant.hp') }}</span>
                    </div>
                    <div x-show="combo > 0" class="combo-counter" x-text="'COMBO x' + combo"></div>
                </div>

                <!-- Move 2 -->
                <div class="p-8 fight-card" x-data="{ combo: 0 }" @click="combo++" :class="{ 'hit-effect': combo > 0 }">
                    <div class="flex items-center justify-center w-16 h-16 mb-6 text-3xl text-red-500 border-2 border-red-500 rounded-lg bg-red-500/20">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3 class="mb-4 text-2xl font-bold text-white">{{ __('welcome.features.tools.legal_research.title') }}</h3>
                    <p class="mb-6 text-gray-300">
                        {{ __('welcome.features.tools.legal_research.description') }}
                    </p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-400">{{ __('welcome.features.tools.legal_research.type') }}</span>
                        <span class="font-bold text-red-500">{{ __('welcome.features.tools.legal_research.hp') }}</span>
                    </div>
                    <div x-show="combo > 0" class="combo-counter" x-text="'COMBO x' + combo"></div>
                </div>



                <!-- Move 3 -->
                <div class="p-8 fight-card" x-data="{ combo: 0 }" @click="combo++" :class="{ 'hit-effect': combo > 0 }">
                    <div class="flex items-center justify-center w-16 h-16 mb-6 text-3xl text-red-500 border-2 border-red-500 rounded-lg bg-red-500/20">
                        <i class="fas fa-file-alt"></i>
                    </div>
                    <h3 class="mb-4 text-2xl font-bold text-white">{{ __('welcome.features.tools.doc_crafter.title') }}</h3>
                    <p class="mb-6 text-gray-300">
                        {{ __('welcome.features.tools.doc_crafter.description') }}
                    </p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-400">{{ __('welcome.features.tools.doc_crafter.type') }}</span>
                        <span class="font-bold text-red-500">{{ __('welcome.features.tools.doc_crafter.hp') }}</span>
                    </div>
                    <div x-show="combo > 0" class="combo-counter" x-text="'COMBO x' + combo"></div>
                </div>

                <!-- Move 4 -->
                <div class="p-8 fight-card" x-data="{ combo: 0 }" @click="combo++" :class="{ 'hit-effect': combo > 0 }">
                    <div class="flex items-center justify-center w-16 h-16 mb-6 text-3xl text-red-500 border-2 border-red-500 rounded-lg bg-red-500/20">
                        <i class="fas fa-folder"></i>
                    </div>
                    <h3 class="mb-4 text-2xl font-bold text-white">{{ __('welcome.features.tools.doc_management.title') }}</h3>
                    <p class="mb-6 text-gray-300">
                        {{ __('welcome.features.tools.doc_management.description') }}
                    </p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-400">{{ __('welcome.features.tools.doc_management.type') }}</span>
                        <span class="font-bold text-red-500">{{ __('welcome.features.tools.doc_management.hp') }}</span>
                    </div>
                    <div x-show="combo > 0" class="combo-counter" x-text="'COMBO x' + combo"></div>
                </div>

                <!-- Move 5 -->
                <div class="p-8 fight-card" x-data="{ combo: 0 }" @click="combo++" :class="{ 'hit-effect': combo > 0 }">
                    <div class="flex items-center justify-center w-16 h-16 mb-6 text-3xl text-red-500 border-2 border-red-500 rounded-lg bg-red-500/20">
                        <i class="fas fa-users"></i>
                    </div>
                    <h3 class="mb-4 text-2xl font-bold text-white">{{ __('welcome.features.tools.team_up.title') }}</h3>
                    <p class="mb-6 text-gray-300">
                        {{ __('welcome.features.tools.team_up.description') }}
                    </p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-400">{{ __('welcome.features.tools.team_up.type') }}</span>
                        <span class="font-bold text-red-500">{{ __('welcome.features.tools.team_up.hp') }}</span>
                    </div>
                    <div x-show="combo > 0" class="combo-counter" x-text="'COMBO x' + combo"></div>
                </div>

                <!-- Move 6 -->
                <div class="p-8 fight-card" x-data="{ combo: 0 }" @click="combo++" :class="{ 'hit-effect': combo > 0 }">
                    <div class="flex items-center justify-center w-16 h-16 mb-6 text-3xl text-red-500 border-2 border-red-500 rounded-lg bg-red-500/20">
                        <i class="fas fa-pencil"></i>
                    </div>
                    <h3 class="mb-4 text-2xl font-bold text-white">{{ __('welcome.features.tools.strategize.title') }}</h3>
                    <p class="mb-6 text-gray-300">
                        {{ __('welcome.features.tools.strategize.description') }}
                    </p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-400">{{ __('welcome.features.tools.strategize.type') }}</span>
                        <span class="font-bold text-red-500">{{ __('welcome.features.tools.strategize.hp') }}</span>
                    </div>
                    <div x-show="combo > 0" class="combo-counter" x-text="'COMBO x' + combo"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Fight Tutorial -->
    <section id="how-it-works" class="py-20 bg-black/30">
        <div class="container px-6 mx-auto">
            <div class="mb-16 text-center">
                <h2 class="mb-6 text-4xl font-bold md:text-5xl pixel-text">
                    {!! __('welcome.how_it_works.title') !!}
                </h2>
                <p class="max-w-3xl mx-auto text-xl text-gray-300">
                    {{ __('welcome.how_it_works.subtitle') }}
                </p>
            </div>

            <div class="grid gap-8 md:grid-cols-4">
                <!-- Round 1 -->
                <div class="flex flex-col items-center">
                    <div class="flex items-center justify-center w-24 h-24 mb-6 text-3xl font-bold text-white bg-red-500 border-4 border-yellow-400 rounded-full shadow-lg">
                        1
                    </div>
                    <div class="p-6 text-center fight-card">
                        <i class="mb-4 text-4xl text-red-500 fas fa-upload"></i>
                        <h3 class="mb-2 text-xl font-bold text-white">{{ __('welcome.how_it_works.steps.upload_case.title') }}</h3>
                        <p class="text-sm text-gray-300">
                            {{ __('welcome.how_it_works.steps.upload_case.description') }}
                        </p>
{{--                        <div class="mt-4 font-mono text-xs text-yellow-400">--}}
{{--                            ← → ↑ ↓ + PUNCH--}}
{{--                        </div>--}}
                    </div>
                </div>

                <!-- Round 2 -->
                <div class="flex flex-col items-center">
                    <div class="flex items-center justify-center w-24 h-24 mb-6 text-3xl font-bold text-white bg-red-500 border-4 border-yellow-400 rounded-full shadow-lg">
                        2
                    </div>
                    <div class="p-6 text-center fight-card">
                        <i class="mb-4 text-4xl text-red-500 fas fa-lightbulb"></i>
                        <h3 class="mb-2 text-xl font-bold text-white">{{ __('welcome.how_it_works.steps.ai_analysis.title') }}</h3>
                        <p class="text-sm text-gray-300">
                            {{ __('welcome.how_it_works.steps.ai_analysis.description') }}
                        </p>
{{--                        <div class="mt-4 font-mono text-xs text-yellow-400">--}}
{{--                            ↓ ↘ → + KICK--}}
{{--                        </div>--}}
                    </div>
                </div>

                <!-- Round 3 -->
                <div class="flex flex-col items-center">
                    <div class="flex items-center justify-center w-24 h-24 mb-6 text-3xl font-bold text-white bg-red-500 border-4 border-yellow-400 rounded-full shadow-lg">
                        3
                    </div>
                    <div class="p-6 text-center fight-card">
                        <i class="mb-4 text-4xl text-red-500 fas fa-search"></i>
                        <h3 class="mb-2 text-xl font-bold text-white">{{ __('welcome.how_it_works.steps.legal_research.title') }}</h3>
                        <p class="text-sm text-gray-300">
                            {{ __('welcome.how_it_works.steps.legal_research.description') }}
                        </p>
{{--                        <div class="mt-4 font-mono text-xs text-yellow-400">--}}
{{--                            → ↓ ↘ + PUNCH--}}
{{--                        </div>--}}
                    </div>
                </div>

                <!-- Round 4 -->
                <div class="flex flex-col items-center">
                    <div class="flex items-center justify-center w-24 h-24 mb-6 text-3xl font-bold text-white bg-red-500 border-4 border-yellow-400 rounded-full shadow-lg">
                        4
                    </div>
                    <div class="p-6 text-center fight-card">
                        <i class="mb-4 text-4xl text-red-500 fas fa-file-signature"></i>
                        <h3 class="mb-2 text-xl font-bold text-white">{{ __('welcome.how_it_works.steps.draft_docs.title') }}</h3>
                        <p class="text-sm text-gray-300">
                            {{ __('welcome.how_it_works.steps.draft_docs.description') }}
                        </p>
{{--                        <div class="mt-4 font-mono text-xs text-yellow-400">--}}
{{--                            ↓ ↙ ← → + KICK--}}
{{--                        </div>--}}
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Character Select / Pricing Tiers -->
    <section id="pricing" class="py-20 bg-black/20">
        <div class="container px-6 mx-auto">
            <div class="mb-16 text-center">
                <h2 class="mb-6 text-4xl font-bold md:text-5xl pixel-text">
                    {!! __('welcome.pricing.title') !!}
                </h2>
                <p class="max-w-3xl mx-auto text-xl text-gray-300">
                    {{ __('welcome.pricing.subtitle') }}
                </p>
            </div>

            <!-- Pro Se Plans Section -->
            <div class="mb-16">
                <h3 class="mb-6 text-2xl font-bold text-center text-white">Pro Se Plans</h3>
                <p class="mb-8 text-center text-gray-300">For individuals representing themselves in legal matters</p>

                <div class="grid gap-8 md:grid-cols-3">
                    <!-- Pro Se Basic -->
                    <div class="relative p-6 text-center transition-transform duration-300 transform character-select hover:scale-105"
                         @click="selected = 'pro_se_basic'"
                         :class="{ 'active': selected === 'pro_se_basic' }"
                         x-data="{ selected: '' }">
                        <div class="absolute top-0 right-0 px-3 py-1 text-xs font-bold text-white bg-red-500 rounded-bl-lg rounded-tr-lg">
                            Most Popular
                        </div>
                        <div class="flex items-center justify-center w-32 h-32 mx-auto mb-6 text-5xl bg-gray-800 rounded-full">
                            <i class="text-red-500 fas fa-user-shield"></i>
                        </div>
                        <h3 class="mb-2 text-2xl font-bold text-white">Pro Se Basic</h3>
                        <div class="mb-4 text-3xl font-bold text-red-500">
                            $19<span class="text-sm text-gray-400">/month</span>
                        </div>
                        <p class="mb-4 text-gray-300">
                            Perfect for individuals representing themselves
                        </p>
                        <div class="w-full mb-4 health-bar"></div>
                        <div class="mb-2 text-center text-yellow-400 font-bold">
                            2,000 credits monthly
                        </div>
                        <ul class="mb-6 space-y-2 text-left text-gray-300">
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> 1 active case
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Core document management
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> 1 collaborator
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Standard support
                            </li>
                        </ul>
                        <button @click="showRegisterModal = true" class="w-full px-4 py-2 font-bold text-white transition-colors duration-300 bg-red-600 rounded-lg hover:bg-red-700">
                            Select Plan
                        </button>
                    </div>

                    <!-- Pro Se Standard -->
                    <div class="relative p-6 text-center transition-transform duration-300 transform character-select hover:scale-105"
                         @click="selected = 'pro_se_standard'"
                         :class="{ 'active': selected === 'pro_se_standard' }"
                         x-data="{ selected: '' }">
                        <div class="flex items-center justify-center w-32 h-32 mx-auto mb-6 text-5xl bg-gray-800 rounded-full">
                            <i class="text-blue-400 fas fa-hands-helping"></i>
                        </div>
                        <h3 class="mb-2 text-2xl font-bold text-white">Pro Se Standard</h3>
                        <div class="mb-4 text-3xl font-bold text-blue-400">
                            $49<span class="text-sm text-gray-400">/month</span>
                        </div>
                        <p class="mb-4 text-gray-300">
                            Enhanced features for complex cases
                        </p>
                        <div class="w-full mb-4 health-bar"></div>
                        <div class="mb-2 text-center text-yellow-400 font-bold">
                            6,000 credits monthly
                        </div>
                        <ul class="mb-6 space-y-2 text-left text-gray-300">
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> 2-3 cases
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Enhanced AI capabilities
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> 2 collaborators
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Standard support
                            </li>
                        </ul>
                        <button @click="showRegisterModal = true" class="w-full px-4 py-2 font-bold text-white transition-colors duration-300 bg-blue-600 rounded-lg hover:bg-blue-700">
                            Select Plan
                        </button>
                    </div>

                    <!-- Pro Se Plus -->
                    <div class="relative p-6 text-center transition-transform duration-300 transform character-select hover:scale-105"
                         @click="selected = 'pro_se_plus'"
                         :class="{ 'active': selected === 'pro_se_plus' }"
                         x-data="{ selected: '' }">
                        <div class="flex items-center justify-center w-32 h-32 mx-auto mb-6 text-5xl bg-gray-800 rounded-full">
                            <i class="text-green-400 fas fa-user-tie"></i>
                        </div>
                        <h3 class="mb-2 text-2xl font-bold text-white">Pro Se Plus</h3>
                        <div class="mb-4 text-3xl font-bold text-green-400">
                            $99<span class="text-sm text-gray-400">/month</span>
                        </div>
                        <p class="mb-4 text-gray-300">
                            Complete legal toolkit for power users
                        </p>
                        <div class="w-full mb-4 health-bar"></div>
                        <div class="mb-2 text-center text-yellow-400 font-bold">
                            25,000 credits monthly
                        </div>
                        <ul class="mb-6 space-y-2 text-left text-gray-300">
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Unlimited cases
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> All features
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> 5 collaborators
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Priority support
                            </li>
                        </ul>
                        <button @click="showRegisterModal = true" class="w-full px-4 py-2 font-bold text-white transition-colors duration-300 bg-green-600 rounded-lg hover:bg-green-700">
                            Select Plan
                        </button>
                    </div>
                </div>
            </div>

            <!-- Attorney Plans Section -->
            <div class="mb-16">
                <h3 class="mb-6 text-2xl font-bold text-center text-white">Attorney Plans</h3>
                <p class="mb-8 text-center text-gray-300">Professional tools for licensed attorneys with client acquisition features</p>

                <div class="grid gap-8 md:grid-cols-3">
                    <!-- Attorney Starter -->
                    <div class="relative p-6 text-center transition-transform duration-300 transform character-select hover:scale-105"
                         @click="selected = 'attorney_starter'"
                         :class="{ 'active': selected === 'attorney_starter' }"
                         x-data="{ selected: '' }">
                        <div class="flex items-center justify-center w-32 h-32 mx-auto mb-6 text-5xl bg-gray-800 rounded-full">
                            <i class="text-purple-400 fas fa-balance-scale"></i>
                        </div>
                        <h3 class="mb-2 text-2xl font-bold text-white">Attorney Starter</h3>
                        <div class="mb-4 text-3xl font-bold text-purple-400">
                            $149<span class="text-sm text-gray-400">/month</span>
                        </div>
                        <p class="mb-4 text-gray-300">
                            Perfect for solo practitioners starting out
                        </p>
                        <div class="w-full mb-4 health-bar"></div>
                        <div class="mb-2 text-center text-yellow-400 font-bold">
                            8,000 credits monthly
                        </div>
                        <ul class="mb-6 space-y-2 text-left text-gray-300">
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Up to 10 active cases
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Client lead management (25/month)
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Basic invoicing (10/month)
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Client portal (5 accounts)
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> 3 collaborators
                            </li>
                        </ul>
                        <button @click="showRegisterModal = true" class="w-full px-4 py-2 font-bold text-white transition-colors duration-300 bg-purple-600 rounded-lg hover:bg-purple-700">
                            Select Plan
                        </button>
                    </div>

                    <!-- Attorney Professional -->
                    <div class="relative p-6 text-center transition-transform duration-300 transform character-select hover:scale-105"
                         @click="selected = 'attorney_professional'"
                         :class="{ 'active': selected === 'attorney_professional' }"
                         x-data="{ selected: '' }">
                        <div class="absolute top-0 right-0 px-3 py-1 text-xs font-bold text-white bg-purple-500 rounded-bl-lg rounded-tr-lg">
                            Best Value
                        </div>
                        <div class="flex items-center justify-center w-32 h-32 mx-auto mb-6 text-5xl bg-gray-800 rounded-full">
                            <i class="text-indigo-400 fas fa-briefcase"></i>
                        </div>
                        <h3 class="mb-2 text-2xl font-bold text-white">Attorney Professional</h3>
                        <div class="mb-4 text-3xl font-bold text-indigo-400">
                            $299<span class="text-sm text-gray-400">/month</span>
                        </div>
                        <p class="mb-4 text-gray-300">
                            Complete practice management solution
                        </p>
                        <div class="w-full mb-4 health-bar"></div>
                        <div class="mb-2 text-center text-yellow-400 font-bold">
                            15,000 credits monthly
                        </div>
                        <ul class="mb-6 space-y-2 text-left text-gray-300">
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Unlimited cases
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Enhanced leads (75/month)
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Advanced invoicing
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Lead analytics & tracking
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Custom branding
                            </li>
                        </ul>
                        <button @click="showRegisterModal = true" class="w-full px-4 py-2 font-bold text-white transition-colors duration-300 bg-indigo-600 rounded-lg hover:bg-indigo-700">
                            Select Plan
                        </button>
                    </div>

                    <!-- Attorney Enterprise -->
                    <div class="relative p-6 text-center transition-transform duration-300 transform character-select hover:scale-105"
                         @click="selected = 'attorney_enterprise'"
                         :class="{ 'active': selected === 'attorney_enterprise' }"
                         x-data="{ selected: '' }">
                        <div class="flex items-center justify-center w-32 h-32 mx-auto mb-6 text-5xl bg-gray-800 rounded-full">
                            <i class="text-yellow-400 fas fa-building"></i>
                        </div>
                        <h3 class="mb-2 text-2xl font-bold text-white">Attorney Enterprise</h3>
                        <div class="mb-4 text-3xl font-bold text-yellow-400">
                            $599<span class="text-sm text-gray-400">/month</span>
                        </div>
                        <p class="mb-4 text-gray-300">
                            Enterprise solution for large firms
                        </p>
                        <div class="w-full mb-4 health-bar"></div>
                        <div class="mb-2 text-center text-yellow-400 font-bold">
                            35,000 credits monthly
                        </div>
                        <ul class="mb-6 space-y-2 text-left text-gray-300">
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Everything in Professional
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Unlimited leads
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> White-label portal
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> API access
                            </li>
                            <li class="flex items-center">
                                <i class="mr-2 text-green-500 fas fa-check-circle"></i> Dedicated support
                            </li>
                        </ul>
                        <button @click="showRegisterModal = true" class="w-full px-4 py-2 font-bold text-white transition-colors duration-300 bg-yellow-600 rounded-lg hover:bg-yellow-700">
                            Select Plan
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Credit System Information -->
        <section class="py-20 bg-black/30">
            <div class="container px-6 mx-auto">
                <div class="mb-8">
                    <div class="card bg-base-200 shadow-md fight-card">
                        <div class="card-body">
                            <button type="button" onclick="toggleCreditInfoWelcome()" class="flex items-center justify-between w-full text-left text-lg font-medium hover:bg-base-300 p-2 rounded transition-colors">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                    <span class="text-white">How Our Credit System Works - Click to Learn More</span>
                                </div>
                                <svg id="credit-info-arrow-welcome" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transform transition-transform duration-200 text-white" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div id="credit-info-content-welcome" class="hidden mt-4">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- Case Management -->
                                    <div>
                                        <h4 class="font-semibold text-lg mb-3 text-red-500">Case Management</h4>
                                        <div class="space-y-2">
                                            <div class="flex justify-between items-center p-2 bg-black/40 rounded">
                                                <span class="text-sm text-white">Initializing a New Case</span>
                                                <span class="badge bg-red-500 text-white border-red-500">100 credits</span>
                                            </div>
                                            <div class="flex justify-between items-center p-2 bg-black/40 rounded">
                                                <span class="text-sm text-white">Legal Research Report</span>
                                                <span class="badge bg-red-500 text-white border-red-500">100 credits</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Document Processing -->
                                    <div>
                                        <h4 class="font-semibold text-lg mb-3 text-red-500">Document Processing</h4>
                                        <div class="space-y-2">
                                            <div class="flex justify-between items-center p-2 bg-black/40 rounded">
                                                <span class="text-sm text-white">Document Analysis & Summary</span>
                                                <span class="badge bg-red-500 text-white border-red-500">10 credits</span>
                                            </div>
                                            <div class="flex justify-between items-center p-2 bg-black/40 rounded">
                                                <span class="text-sm text-white">Audio Transcription (additional)</span>
                                                <span class="badge bg-red-500 text-white border-red-500">+5 credits</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Independent Research -->
                                    <div>
                                        <h4 class="font-semibold text-lg mb-3 text-red-500">Independent Research</h4>
                                        <div class="space-y-2">
                                            <div class="flex justify-between items-center p-2 bg-black/40 rounded">
                                                <span class="text-sm text-white">Short Web Search</span>
                                                <span class="badge bg-red-500 text-white border-red-500">50 credits</span>
                                            </div>
                                            <div class="flex justify-between items-center p-2 bg-black/40 rounded">
                                                <span class="text-sm text-white">Regular Research Report</span>
                                                <span class="badge bg-red-500 text-white border-red-500">100 credits</span>
                                            </div>
                                            <div class="flex justify-between items-center p-2 bg-black/40 rounded">
                                                <span class="text-sm text-white">Deep Research Report</span>
                                                <span class="badge bg-red-500 text-white border-red-500">300 credits</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- AI Assistant -->
                                    <div>
                                        <h4 class="font-semibold text-lg mb-3 text-red-500">AI Assistant</h4>
                                        <div class="space-y-2">
                                            <div class="flex justify-between items-center p-2 bg-black/40 rounded">
                                                <span class="text-sm text-white">Chat Interaction</span>
                                                <span class="badge bg-red-500 text-white border-red-500">5 credits</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-6 p-4 bg-blue-500/10 rounded-lg border border-blue-500/20">
                                    <div class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400 mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                        </svg>
                                        <div>
                                            <h5 class="font-semibold text-blue-400 mb-1">Why Credits?</h5>
                                            <p class="text-sm text-gray-300">
                                                Credits ensure fair usage of our AI-powered legal tools. Each feature requires different amounts of computational resources,
                                                so credits are priced accordingly. This system allows us to provide high-quality AI assistance while maintaining
                                                sustainable service costs. Every subscription plan includes monthly credits to get you started!
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </section>

    <!-- Fight Callout -->
    <section id="get-started" class="py-20 bg-gradient-to-r from-red-900 to-red-700">
        <div class="container px-6 mx-auto text-center">
            <h2 class="mb-6 text-4xl font-bold md:text-5xl pixel-text">
                {!! __('welcome.cta.title') !!}
            </h2>
            <p class="max-w-2xl mx-auto mb-8 text-xl text-red-100">
                {{ __('welcome.cta.subtitle') }}
            </p>
            <div class="flex flex-col justify-center space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                <button @click="showRegisterModal = true" class="px-8 py-4 text-lg rounded-lg fight-btn">
                    <i class="mr-2 fas fa-user-plus"></i> {{ __('welcome.cta.create_account') }}
                </button>
                <button @click="showLoginModal = true" class="px-8 py-4 font-bold text-white transition border-2 border-white rounded-lg bg-black/50 hover:bg-white/10">
                    <i class="mr-2 fas fa-sign-in-alt"></i> {{ __('welcome.cta.login') }}
                </button>
            </div>
        </div>
    </section>

    <!-- Fight Footer -->
    <footer class="py-12 border-t border-gray-800 bg-black/80">
        <div class="container px-6 mx-auto">
            <div class="flex flex-col items-center justify-between md:flex-row">
                <div class="flex items-center mb-6 md:mb-0">
{{--                    <img src="https://justicequest.us-mia-1.linodeobjects.com/public/images/en/JQ.png" alt="Justice Quest" class="h-8 mr-3 animate-pulse-subtle">--}}
                    <span class="text-xl font-bold text-white pixel-text">JUSTICE QUEST</span>
                </div>
                <div class="flex mb-6 space-x-6 md:mb-0">
                    <a href="#" class="text-gray-400 transition hover:text-red-500">
                        <i class="text-xl fab fa-twitter"></i>
                    </a>
                    <a href="#" class="text-gray-400 transition hover:text-red-500">
                        <i class="text-xl fab fa-facebook"></i>
                    </a>
                    <a href="#" class="text-gray-400 transition hover:text-red-500">
                        <i class="text-xl fab fa-linkedin"></i>
                    </a>
                </div>
                <div class="text-sm text-gray-500">
                    &copy; <span x-text="new Date().getFullYear()"></span> {{ __('welcome.footer.copyright') }}
                </div>
            </div>
            <div class="pt-8 mt-8 text-xs text-center text-gray-600 border-t border-gray-800">
                <p>{{ __('welcome.footer.disclaimer') }}</p>
                <p class="mt-2">{{ __('welcome.footer.mission') }}</p>
            </div>
        </div>
    </footer>

    <!-- Login Modal -->
    <div x-show="showLoginModal" class="fixed inset-0 z-50 flex items-center justify-center overflow-auto bg-black/80 backdrop-blur-sm" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div @click.away="showLoginModal = false" class="relative w-full max-w-md p-8 mx-4 bg-gradient-to-br from-indigo-900 to-indigo-700 border border-indigo-500/30 rounded-lg shadow-lg backdrop-blur-md" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100">
            <button @click="showLoginModal = false" class="absolute text-gray-400 top-4 right-4 hover:text-white">
                <i class="fas fa-times"></i>
            </button>

            <div wire:ignore.self>
                <livewire:auth.welcome-login-form />
            </div>
        </div>
    </div>

    <!-- Register Modal -->
    <div x-show="showRegisterModal" class="fixed inset-0 z-50 flex items-center justify-center overflow-auto bg-black/80 backdrop-blur-sm" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0">
        <div @click.away="showRegisterModal = false" class="relative w-full max-w-md p-8 mx-4 my-8 bg-gradient-to-br from-indigo-900 to-indigo-700 border border-indigo-500/30 rounded-lg shadow-lg backdrop-blur-md" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100">
            <button @click="showRegisterModal = false" class="absolute text-gray-400 top-4 right-4 hover:text-white">
                <i class="fas fa-times"></i>
            </button>

            <div wire:ignore.self>
                <livewire:auth.welcome-register-form />
            </div>
        </div>
    </div>
    <!-- Background Music Player (hidden) -->
    <div id="music-player-container" class="fixed z-40 flex items-center space-x-2 bottom-4 right-4">
        <div id="youtube-player" class="hidden"></div>
        <div id="play-music-prompt"
            class="hidden p-3 mr-2 bg-blue-500 border rounded-lg shadow-lg backdrop-blur-sm border-white">
            <button id="force-play-music" class="flex items-center text-xs text-white hover:text-yellow-400">
                <i class="mr-2 fas fa-play"></i> {{ __('welcome.media.play_music') }}
            </button>
        </div>
        <div class="relative group">
            <div
                class="flex items-center p-2 space-x-2 bg-blue-600 border rounded-full shadow-lg backdrop-blur-sm border-white">
                <button id="toggle-music"
                    class="flex items-center justify-center w-8 h-8 text-white transition-colors duration-300 rounded-full hover:text-yellow-400 bg-black/40 hover:bg-black/60">
                    <i id="music-icon" class="fas fa-volume-up"></i>
                </button>
                <div id="music-info"
                    class="text-white text-xs hidden sm:block pr-2 max-w-[120px] truncate cursor-pointer">{{ __('welcome.media.loading') }}
                </div>
                <!-- Volume Control -->
                <div class="hidden sm:flex items-center space-x-1">
                    <button id="volume-down" class="text-white hover:text-yellow-400 text-xs">
                        <i class="fas fa-volume-down"></i>
                    </button>
                    <input type="range" id="volume-slider" min="0" max="100" value="50"
                        class="w-16 h-1 bg-gray-700 rounded-lg appearance-none cursor-pointer"
                        style="accent-color: #f59e0b;">
                    <button id="volume-up" class="text-white hover:text-yellow-400 text-xs">
                        <i class="fas fa-volume-up"></i>
                    </button>
                </div>
                <button id="show-playlist"
                    class="items-center justify-center hidden w-6 h-6 text-white transition-colors duration-300 hover:text-yellow-400 sm:flex">
                    <i class="transition-transform duration-300 fas fa-chevron-up group-hover:rotate-180"></i>
                </button>
            </div>

            <!-- Playlist dropdown -->
            <div id="playlist-dropdown"
                class="absolute right-0 hidden w-48 p-2 mb-2 transition-opacity duration-300 border rounded-lg shadow-lg opacity-0 bottom-full bg-black/80 backdrop-blur-md border-white/10 group-hover:block group-hover:opacity-100">
                <div class="px-2 mb-2 text-xs font-semibold text-white">Select Track:</div>
                <div id="playlist-items" class="space-y-1 overflow-y-auto max-h-32">
                    <!-- Playlist items will be added here -->
                </div>
            </div>
        </div>
    </div>

    <script>
        // Toggle credit info section for welcome page
        function toggleCreditInfoWelcome() {
            const content = document.getElementById('credit-info-content-welcome');
            const arrow = document.getElementById('credit-info-arrow-welcome');

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                arrow.classList.add('rotate-180');
            } else {
                content.classList.add('hidden');
                arrow.classList.remove('rotate-180');
            }
        }

        // Create a global Alpine.js store for the music player
        document.addEventListener('alpine:init', () => {
            Alpine.store('musicPlayer', {
                player: null,
                playerReady: false,
                isMusicPlaying: true,
                currentVolume: 50,
                currentTrack: 'knx_0',
                playlists: {
                    'knx_0': 'https://www.youtube.com/watch?v=BsGj7QEQ4eo',
                    'devin': 'https://www.youtube.com/watch?v=KOb29U5UjSY&t=20',
                    'devin1': "https://www.youtube.com/watch?v=I4dPI18yaR4&list=RDMMI4dPI18yaR4&start_radio=1",
                    'devin2': "https://www.youtube.com/watch?v=eSVZRo9bc6I",
                    'knx': 'https://www.youtube.com/watch?v=DFfeE2OMkW8',
                    'mxxwll': 'https://www.youtube.com/watch?v=DFfeE2OMkW8',
                    'josh2800': 'https://www.youtube.com/watch?v=2zCVApZDmZc&list=OLAK5uy_kkEzojK7YZY-Nm45Xyr5BdO-TSSJeZn40&index=7'
                },

                init() {
                    // Load playlists from JSON file to update if needed
                    fetch('/playlists/jams.json')
                        .then(response => response.json())
                        .then(data => {
                            this.playlists = data;
                        })
                        .catch(error => console.error('Error loading playlists:', error));
                },

                initPlayer() {
                    if (this.player) return; // Player already initialized

                    try {
                        const url = this.playlists[this.currentTrack];
                        const videoId = this.extractVideoId(url);
                        const startSeconds = this.extractStartTime(url);

                        this.player = new YT.Player('youtube-player', {
                            height: '0',
                            width: '0',
                            videoId: videoId,
                            playerVars: {
                                'autoplay': 1,
                                'controls': 0,
                                'disablekb': 1,
                                'fs': 0,
                                'modestbranding': 1,
                                'rel': 0,
                                'start': startSeconds
                            },
                            events: {
                                'onReady': (event) => this.onPlayerReady(event),
                                'onStateChange': (event) => this.onPlayerStateChange(event),
                                'onError': (event) => this.onPlayerError(event)
                            }
                        });

                        // Update music info
                        const musicInfo = document.getElementById('music-info');
                        if (musicInfo) {
                            musicInfo.textContent = `{{ __('welcome.media.now_playing') }} ${this.currentTrack}`;
                        }
                    } catch (error) {
                        console.error('Error initializing YouTube player:', error);
                        // Show the play prompt as a fallback
                        const playPrompt = document.getElementById('play-music-prompt');
                        if (playPrompt) {
                            playPrompt.classList.remove('hidden');
                        }
                    }
                },

                onPlayerReady(event) {
                    this.playerReady = true;

                    // Immediately play with volume
                    event.target.unMute();
                    event.target.setVolume(this.currentVolume);
                    event.target.playVideo();

                    console.log('YouTube player ready, playing music immediately');
                },

                onPlayerStateChange(event) {
                    // If video ends, play it again (loop)
                    if (event.data === YT.PlayerState.ENDED) {
                        this.player.playVideo();
                    }
                },

                onPlayerError(event) {
                    console.error('YouTube Player Error:', event.data);
                    const musicInfo = document.getElementById('music-info');
                    if (musicInfo) {
                        musicInfo.textContent = 'Error playing track';
                    }
                },

                toggleMusic() {
                    if (!this.player || !this.playerReady) return;

                    const musicIcon = document.getElementById('music-icon');
                    if (!musicIcon) return;

                    if (this.isMusicPlaying) {
                        this.player.pauseVideo();
                        musicIcon.className = 'fas fa-volume-mute';
                    } else {
                        this.player.unMute();
                        this.player.setVolume(this.currentVolume);
                        this.player.playVideo();
                        musicIcon.className = 'fas fa-volume-up';

                        // Hide the play prompt if it's visible
                        const playPrompt = document.getElementById('play-music-prompt');
                        if (playPrompt) {
                            playPrompt.classList.add('hidden');
                        }
                    }

                    this.isMusicPlaying = !this.isMusicPlaying;
                },

                forcePlayMusic() {
                    if (!this.player || !this.playerReady) return;

                    this.player.unMute();
                    this.player.setVolume(this.currentVolume);
                    this.player.playVideo();
                    this.isMusicPlaying = true;

                    const musicIcon = document.getElementById('music-icon');
                    if (musicIcon) {
                        musicIcon.className = 'fas fa-volume-up';
                    }

                    const playPrompt = document.getElementById('play-music-prompt');
                    if (playPrompt) {
                        playPrompt.classList.add('hidden');
                    }

                    console.log('Music playback forced by user interaction');
                },

                changeTrack(track) {
                    if (track === this.currentTrack || !this.player) return;

                    this.currentTrack = track;
                    const url = this.playlists[this.currentTrack];
                    const videoId = this.extractVideoId(url);
                    const startSeconds = this.extractStartTime(url);

                    // Load and play the new video with timestamp
                    this.player.loadVideoById({
                        videoId: videoId,
                        startSeconds: startSeconds
                    });

                    // Update music info
                    const musicInfo = document.getElementById('music-info');
                    if (musicInfo) {
                        musicInfo.textContent = `{{ __('welcome.media.now_playing') }} ${this.currentTrack}`;
                    }
                },

                extractVideoId(url) {
                    const regExp =
                        /^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\?))\??v?=?([^#&?]*).*/;
                    const match = url.match(regExp);
                    return (match && match[7].length === 11) ? match[7] : false;
                },

                extractStartTime(url) {
                    let startSeconds = 0;

                    try {
                        // Parse the URL to get all query parameters
                        const urlObj = new URL(url);
                        const searchParams = urlObj.searchParams;

                        // Check for 't' parameter (can be in formats like t=5s or t=5)
                        if (searchParams.has('t')) {
                            const tParam = searchParams.get('t');
                            // Handle format like '35s'
                            if (tParam.endsWith('s')) {
                                startSeconds = parseInt(tParam.slice(0, -1), 10);
                            } else {
                                // Handle format like '35'
                                startSeconds = parseInt(tParam, 10);
                            }
                        }
                        // Also check for 'start' parameter as an alternative
                        else if (searchParams.has('start')) {
                            startSeconds = parseInt(searchParams.get('start'), 10);
                        }

                        // Handle time in minutes and seconds format (e.g., '1m30s')
                        if (searchParams.has('t') && searchParams.get('t').includes('m')) {
                            const tParam = searchParams.get('t');
                            const minutesMatch = tParam.match(/(\d+)m/);
                            const secondsMatch = tParam.match(/(\d+)s/);

                            if (minutesMatch) {
                                startSeconds = parseInt(minutesMatch[1], 10) * 60;
                            }
                            if (secondsMatch) {
                                startSeconds += parseInt(secondsMatch[1], 10);
                            }
                        }
                    } catch (e) {
                        // Fallback to regex approach if URL parsing fails
                        const timeRegExp1 = /[&?]t=([0-9]+)s?/;
                        const timeRegExp2 = /[&?]start=([0-9]+)/;

                        let timeMatch = url.match(timeRegExp1);
                        if (timeMatch && timeMatch[1]) {
                            startSeconds = parseInt(timeMatch[1], 10);
                        } else {
                            timeMatch = url.match(timeRegExp2);
                            if (timeMatch && timeMatch[1]) {
                                startSeconds = parseInt(timeMatch[1], 10);
                            }
                        }
                    }

                    return startSeconds;
                }
            });
        });

        // YouTube API callback
        function onYouTubeIframeAPIReady() {
            // Initialize the player when the API is ready
            if (Alpine && Alpine.store('musicPlayer')) {
                Alpine.store('musicPlayer').initPlayer();
            }
        }

        document.addEventListener('DOMContentLoaded', function() {
            const videoContainer = document.getElementById('intro-video-container');
            const video = document.getElementById('intro-video');
            const skipButton = document.getElementById('skip-intro');
            const loadingText = document.getElementById('loading-text');
            const progressBar = document.getElementById('progress-bar');

            // Function to end the intro
            function endIntro() {
                // Fade out the video container
                videoContainer.style.transition = 'opacity 1.5s ease-out';
                videoContainer.style.opacity = '0';

                // Remove from DOM after fade completes
                setTimeout(() => {
                    videoContainer.remove();

                    // Music should already be playing, but ensure it's playing
                    if (Alpine && Alpine.store('musicPlayer') && Alpine.store('musicPlayer').player) {
                    }
                }, 1500);
            }

            // Show loading text with typewriter effect
            setTimeout(() => {
                loadingText.style.opacity = '1';
            }, 500);

            // Animate progress bar
            setTimeout(() => {
                progressBar.style.transform = 'scaleX(1)';
                progressBar.style.transitionDuration = `${video.duration * 0.8}s`;
            }, 800);

            // Play the video automatically (with muted to ensure autoplay works)
            video.muted = true; // Ensure muted for autoplay
            video.play().catch(error => {
                console.log('Auto-play was prevented:', error);
                // If autoplay is blocked, just end the intro
                endIntro();
            });

            // Skip button click handler
            skipButton.addEventListener('click', function() {
                endIntro();
            });

            // Allow ESC key to skip intro
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && videoContainer.parentNode) {
                    endIntro();
                }
            });

            // When video ends naturally
            video.addEventListener('ended', function() {
                endIntro();
            });

            // Add video game console-like scan lines effect
            const overlay = document.getElementById('video-overlay');
            const scanLines = document.createElement('div');
            scanLines.classList.add('absolute', 'inset-0', 'pointer-events-none', 'opacity-10');
            scanLines.style.background = 'linear-gradient(to bottom, transparent 50%, rgba(0, 0, 0, 0.5) 50%)';
            scanLines.style.backgroundSize = '100% 4px';
            overlay.appendChild(scanLines);

            // Add CRT flicker effect
            const crtEffect = document.getElementById('crt-effect');

            function flickerEffect() {
                // Random brightness fluctuation
                const brightness = 0.95 + Math.random() * 0.1; // Between 0.95 and 1.05
                crtEffect.style.filter = `brightness(${brightness})`;

                // Occasional horizontal glitch
                if (Math.random() < 0.03) { // 3% chance of glitch
                    const glitchOffset = (Math.random() * 10 - 5) + 'px'; // Between -5px and 5px
                    videoContainer.style.transform = `translateX(${glitchOffset})`;
                    setTimeout(() => {
                        videoContainer.style.transform = 'translateX(0)';
                    }, 50);
                }

                requestAnimationFrame(flickerEffect);
            }
            flickerEffect();

            // Initialize music player if Alpine.js store is available
            if (typeof Alpine !== 'undefined' && Alpine.store('musicPlayer')) {
                // Force play music after a short delay to ensure user interaction
                setTimeout(() => {
                    if (Alpine.store('musicPlayer').player && Alpine.store('musicPlayer').playerReady) {
                        Alpine.store('musicPlayer').forcePlayMusic();
                    } else {
                        // Show the play prompt if music isn't playing
                        const playPrompt = document.getElementById('play-music-prompt');
                        if (playPrompt) {
                            playPrompt.classList.remove('hidden');
                        }
                    }
                }, 300);
            }
        });

        // Add event listeners for music player controls
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle music button
            const toggleMusicBtn = document.getElementById('toggle-music');
            if (toggleMusicBtn) {
                toggleMusicBtn.addEventListener('click', () => {
                    if (Alpine && Alpine.store('musicPlayer')) {
                        Alpine.store('musicPlayer').toggleMusic();
                    }
                });
            }

            // Force play music button
            const forcePlayMusicBtn = document.getElementById('force-play-music');
            if (forcePlayMusicBtn) {
                forcePlayMusicBtn.addEventListener('click', () => {
                    if (Alpine && Alpine.store('musicPlayer')) {
                        Alpine.store('musicPlayer').forcePlayMusic();
                    }
                });
            }

            // Volume slider
            const volumeSlider = document.getElementById('volume-slider');
            if (volumeSlider) {
                // Update volume when slider changes
                volumeSlider.addEventListener('input', () => {
                    if (Alpine && Alpine.store('musicPlayer')) {
                        const volume = parseInt(volumeSlider.value);
                        Alpine.store('musicPlayer').currentVolume = volume;
                        if (Alpine.store('musicPlayer').player && Alpine.store('musicPlayer').playerReady) {
                            Alpine.store('musicPlayer').player.setVolume(volume);
                        }

                        // Update volume icon based on level
                        const musicIcon = document.getElementById('music-icon');
                        if (musicIcon && Alpine.store('musicPlayer').isMusicPlaying) {
                            if (volume === 0) {
                                musicIcon.className = 'fas fa-volume-mute';
                            } else if (volume < 50) {
                                musicIcon.className = 'fas fa-volume-down';
                            } else {
                                musicIcon.className = 'fas fa-volume-up';
                            }
                        }
                    }
                });
            }

            // Volume up button
            const volumeUpBtn = document.getElementById('volume-up');
            if (volumeUpBtn) {
                volumeUpBtn.addEventListener('click', () => {
                    if (Alpine && Alpine.store('musicPlayer')) {
                        const volumeSlider = document.getElementById('volume-slider');
                        if (volumeSlider) {
                            let volume = parseInt(volumeSlider.value);
                            volume = Math.min(100, volume + 10); // Increase by 10%, max 100%
                            volumeSlider.value = volume;

                            // Trigger the input event to update the volume
                            volumeSlider.dispatchEvent(new Event('input'));
                        }
                    }
                });
            }

            // Volume down button
            const volumeDownBtn = document.getElementById('volume-down');
            if (volumeDownBtn) {
                volumeDownBtn.addEventListener('click', () => {
                    if (Alpine && Alpine.store('musicPlayer')) {
                        const volumeSlider = document.getElementById('volume-slider');
                        if (volumeSlider) {
                            let volume = parseInt(volumeSlider.value);
                            volume = Math.max(0, volume - 10); // Decrease by 10%, min 0%
                            volumeSlider.value = volume;

                            // Trigger the input event to update the volume
                            volumeSlider.dispatchEvent(new Event('input'));
                        }
                    }
                });
            }

            // Music info dropdown
            const musicInfo = document.getElementById('music-info');
            if (musicInfo) {
                musicInfo.addEventListener('click', () => {
                    const dropdown = document.getElementById('playlist-dropdown');
                    if (dropdown) {
                        dropdown.classList.toggle('hidden');
                    }
                });
            }

            // Populate playlist dropdown
            if (Alpine && Alpine.store('musicPlayer')) {
                const playlistItems = document.getElementById('playlist-items');
                if (playlistItems) {
                    const musicPlayer = Alpine.store('musicPlayer');
                    Object.keys(musicPlayer.playlists).forEach(track => {
                        const item = document.createElement('div');
                        item.className = `px-2 py-1 text-xs text-white hover:bg-white/20 rounded cursor-pointer transition-colors duration-200 ${track === musicPlayer.currentTrack ? 'bg-white/20' : ''}`;
                        item.textContent = track;
                        item.addEventListener('click', () => {
                            musicPlayer.changeTrack(track);
                        });
                        playlistItems.appendChild(item);
                    });
                }
            }
        });
    </script>
    @livewireScripts

    <script>
        document.addEventListener('livewire:initialized', () => {
            // Handle successful login
            Livewire.on('login-successful', (data) => {
                // Redirect to dashboard or specified URL
                window.location.href = data.redirect;
            });

            // Handle successful registration
            Livewire.on('registration-successful', (data) => {
                // Redirect to dashboard or specified URL
                window.location.href = data.redirect;
            });
        });
    </script>
</body>
</html>
