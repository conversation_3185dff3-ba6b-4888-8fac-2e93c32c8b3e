<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100/50 dark:bg-neutral-focus/50 backdrop-blur-xl shadow-xl sm:rounded-lg p-8">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold">{{ __('Manage Subscription') }}</h2>
                    <a href="{{ route('subscriptions.index') }}" class="btn btn-ghost">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                        View Plans
                    </a>
                </div>

                <!-- Current Subscription -->
                <div class="card bg-base-200 shadow-md mb-8">
                    <div class="card-body">
                        <h3 class="card-title text-xl">Current Plan</h3>

                        <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <p class="text-sm text-base-content/70">Plan</p>
                                <p class="text-lg font-semibold">
                                    @php
                                        $planNames = [
                                            'price_1RDSjpRwVr4O26ZHlYSBm1gB' => 'Pro Se Basic',
                                            'price_1RDSulRwVr4O26ZHalkMPhUz' => 'Pro Se Standard',
                                            'price_1RDSv0RwVr4O26ZHTKcXN457' => 'Pro Se Plus',
                                            'price_1RDT1zRwVr4O26ZHFvaptwJP' => 'Attorney Basic',
                                            'price_1RDT2iRwVr4O26ZHULovYU7a' => 'Attorney Pro',
                                            'price_1RDT3FRwVr4O26ZHpV8Qejaa' => 'Attorney Enterprise',
                                        ];
                                        $planName = $planNames[$subscription->stripe_price] ?? 'Unknown Plan';
                                    @endphp
                                    {{ $planName }}
                                </p>
                            </div>

                            <div>
                                <p class="text-sm text-base-content/70">Status</p>
                                <p class="text-lg">
                                    @if($subscription->canceled())
                                        <span class="badge badge-warning">Cancelled</span>
                                        <span class="text-sm ml-2">Ends on {{ $subscription->ends_at->format('M j, Y') }}</span>
                                    @else
                                        <span class="badge badge-success">Active</span>
                                    @endif
                                </p>
                            </div>

                            <div>
                                <p class="text-sm text-base-content/70">Started On</p>
                                <p class="text-lg">{{ $subscription->created_at->format('M j, Y') }}</p>
                            </div>

                            <div>
                                <p class="text-sm text-base-content/70">Next Billing Date</p>
                                <p class="text-lg">
                                    @if($subscription->canceled())
                                        N/A
                                    @else
                                        {{ \Carbon\Carbon::createFromTimestamp($subscription->asStripeSubscription()->current_period_end)->format('M j, Y') }}
                                    @endif
                                </p>
                            </div>
                        </div>

                        <div class="card-actions justify-end mt-6">
                            @if($subscription->canceled())
                                <form action="{{ route('subscriptions.resume') }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-primary">Resume Subscription</button>
                                </form>
                            @else
                                <form action="{{ route('subscriptions.destroy') }}" method="POST" onsubmit="return confirm('Are you sure you want to cancel your subscription?');">
                                    @csrf
                                    @method('DELETE')
                                    <button type="submit" class="btn btn-error">Cancel Subscription</button>
                                </form>
                            @endif
                        </div>
                    </div>
                </div>

                <!-- Payment Method -->
                <div class="card bg-base-200 shadow-md mb-8">
                    <div class="card-body">
                        <h3 class="card-title text-xl">Payment Method</h3>

                        @if($paymentMethod)
                            <div class="mt-4 flex items-center">
                                <div class="flex-1">
                                    <p class="text-sm text-base-content/70">Card</p>
                                    <p class="text-lg">
                                        {{ ucfirst($paymentMethod->card->brand) }} ending in {{ $paymentMethod->card->last4 }}
                                    </p>
                                    <p class="text-sm text-base-content/70">
                                        Expires {{ $paymentMethod->card->exp_month }}/{{ $paymentMethod->card->exp_year }}
                                    </p>
                                </div>

                                <button type="button" onclick="showUpdatePaymentForm()" class="btn btn-sm btn-ghost">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                        <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                                    </svg>
                                    Update
                                </button>
                            </div>
                        @else
                            <p class="mt-4 text-base-content/70">No payment method on file.</p>
                            <button type="button" onclick="showUpdatePaymentForm()" class="btn btn-sm btn-primary mt-2">
                                Add Payment Method
                            </button>
                        @endif

                        <!-- Update Payment Method Form (Hidden by default) -->
                        <div id="payment-update-form" class="hidden mt-6">
                            <div class="divider"></div>

                            <h4 class="text-lg font-semibold mb-4">Update Payment Method</h4>

                            <form id="update-payment-form" action="{{ route('subscriptions.payment-method.update') }}" method="POST">
                                @csrf
                                @method('PUT')

                                <div class="mb-4">
                                    <label for="card-element-update" class="block text-sm font-medium mb-2">
                                        Credit or debit card
                                    </label>
                                    <div id="card-element-update" class="p-3 border rounded-md bg-base-100">
                                        <!-- Stripe Card Element will be inserted here -->
                                    </div>
                                    <div id="card-errors-update" class="text-error text-sm mt-2" role="alert"></div>
                                </div>

                                <div class="flex justify-end mt-4">
                                    <button type="button" onclick="hideUpdatePaymentForm()" class="btn btn-ghost btn-sm mr-2">
                                        Cancel
                                    </button>
                                    <button type="submit" id="update-button" class="btn btn-primary btn-sm">
                                        Update Payment Method
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Billing History -->
                <div class="card bg-base-200 shadow-md">
                    <div class="card-body">
                        <h3 class="card-title text-xl">Billing History</h3>

                        <div class="overflow-x-auto mt-4">
                            <table class="table w-full">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Invoice</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($invoices as $invoice)
                                        <tr>
                                            <td>{{ $invoice->date()->format('M j, Y') }}</td>
                                            <td>{{ $invoice->total() }}</td>
                                            <td>
                                                @if($invoice->status == 'paid')
                                                    <span class="badge badge-success">Paid</span>
                                                @elseif($invoice->status == 'open')
                                                    <span class="badge badge-warning">Pending</span>
                                                @else
                                                    <span class="badge badge-error">{{ ucfirst($invoice->status) }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <a href="{{ $invoice->hosted_invoice_url }}" target="_blank" class="btn btn-xs btn-ghost">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm5 6a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V8z" clip-rule="evenodd" />
                                                    </svg>
                                                    View
                                                </a>
                                                <a href="{{ $invoice->invoice_pdf }}" target="_blank" class="btn btn-xs btn-ghost">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M6 2a2 2 0 00-2 2v12a2 2 0 002 2h8a2 2 0 002-2V7.414A2 2 0 0015.414 6L12 2.586A2 2 0 0010.586 2H6zm1 8a1 1 0 100 2h6a1 1 0 100-2H7z" clip-rule="evenodd" />
                                                    </svg>
                                                    PDF
                                                </a>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="4" class="text-center py-4">No billing history available.</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        // Initialize Stripe
        const stripe = Stripe('{{ env('STRIPE_KEY') }}');
        const elements = stripe.elements();

        // Create card element for payment update
        const cardElementUpdate = elements.create('card');

        // Function to show update payment form
        function showUpdatePaymentForm() {
            document.getElementById('payment-update-form').classList.remove('hidden');
            cardElementUpdate.mount('#card-element-update');
        }

        // Function to hide update payment form
        function hideUpdatePaymentForm() {
            document.getElementById('payment-update-form').classList.add('hidden');
        }

        // Handle payment update form submission
        const updateForm = document.getElementById('update-payment-form');
        const updateButton = document.getElementById('update-button');

        updateForm.addEventListener('submit', async (event) => {
            event.preventDefault();

            // Disable the submit button to prevent multiple submissions
            updateButton.disabled = true;
            updateButton.innerHTML = '<span class="loading loading-spinner loading-xs"></span> Processing...';

            // Create payment method
            const {paymentMethod, error} = await stripe.createPaymentMethod({
                type: 'card',
                card: cardElementUpdate,
            });

            if (error) {
                // Display error
                const errorElement = document.getElementById('card-errors-update');
                errorElement.textContent = error.message;

                // Re-enable the submit button
                updateButton.disabled = false;
                updateButton.innerHTML = 'Update Payment Method';
            } else {
                // Add payment method ID to the form
                const hiddenInput = document.createElement('input');
                hiddenInput.setAttribute('type', 'hidden');
                hiddenInput.setAttribute('name', 'payment_method');
                hiddenInput.setAttribute('value', paymentMethod.id);
                updateForm.appendChild(hiddenInput);

                // Submit the form
                updateForm.submit();
            }
        });
    </script>
    @endpush
</x-app-layout>
