<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100/50 dark:bg-neutral-focus/50 backdrop-blur-xl shadow-xl sm:rounded-lg p-8">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold">{{ __('Subscription Plans') }}</h2>
                    <p class="mt-2 text-base-content/70">
                        Choose the plan that best fits your needs. All plans include access to Justice Quest's core features.
                    </p>
                </div>

                <!-- Collapsible Credit Information -->
                <div class="mb-8">
                    <div class="card bg-base-200 shadow-md">
                        <div class="card-body">
                            <button type="button" onclick="toggleCreditInfoSubscriptions()" class="flex items-center justify-between w-full text-left text-lg font-medium hover:bg-base-300 p-2 rounded transition-colors">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                    </svg>
                                    How Credits Work - Click to Learn More
                                </div>
                                <svg id="credit-info-arrow-subscriptions" xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 transform transition-transform duration-200" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </button>

                            <div id="credit-info-content-subscriptions" class="hidden mt-4">
                                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                    <!-- Case Management -->
                                    <div>
                                        <h4 class="font-semibold text-lg mb-3 text-primary">Case Management</h4>
                                        <div class="space-y-2">
                                            <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                                <span class="text-sm">Initializing a New Case</span>
                                                <span class="badge badge-primary">100 credits</span>
                                            </div>
                                            <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                                <span class="text-sm">Legal Research Report</span>
                                                <span class="badge badge-primary">100 credits</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Document Processing -->
                                    <div>
                                        <h4 class="font-semibold text-lg mb-3 text-primary">Document Processing</h4>
                                        <div class="space-y-2">
                                            <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                                <span class="text-sm">Document Analysis & Summary</span>
                                                <span class="badge badge-primary">10 credits</span>
                                            </div>
                                            <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                                <span class="text-sm">Audio Transcription (additional)</span>
                                                <span class="badge badge-primary">+5 credits</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Independent Research -->
                                    <div>
                                        <h4 class="font-semibold text-lg mb-3 text-primary">Independent Research</h4>
                                        <div class="space-y-2">
                                            <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                                <span class="text-sm">Short Web Search</span>
                                                <span class="badge badge-primary">50 credits</span>
                                            </div>
                                            <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                                <span class="text-sm">Regular Research Report</span>
                                                <span class="badge badge-primary">100 credits</span>
                                            </div>
                                            <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                                <span class="text-sm">Deep Research Report</span>
                                                <span class="badge badge-primary">300 credits</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- AI Assistant -->
                                    <div>
                                        <h4 class="font-semibold text-lg mb-3 text-primary">AI Assistant</h4>
                                        <div class="space-y-2">
                                            <div class="flex justify-between items-center p-2 bg-base-100 rounded">
                                                <span class="text-sm">Chat Interaction</span>
                                                <span class="badge badge-primary">5 credits</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="mt-6 p-4 bg-info/10 rounded-lg border border-info/20">
                                    <div class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-info mr-2 mt-0.5 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                        </svg>
                                        <div>
                                            <h5 class="font-semibold text-info mb-1">Why Credits?</h5>
                                            <p class="text-sm text-base-content/80">
                                                Credits ensure fair usage of our AI-powered legal tools. Each feature requires different amounts of computational resources,
                                                so credits are priced accordingly. This system allows us to provide high-quality AI assistance while maintaining
                                                sustainable service costs.
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                @php $proSePlansShown = false; $attorneyPlansShown = false; @endphp

                @if(!auth()->user()->is_attorney)
                @php $proSePlansShown = true; @endphp
                <!-- Pro Se Plans -->
                <div class="mb-10">
                    <h3 class="text-xl font-semibold mb-4">Pro Se Plans</h3>
                    <p class="mb-6">For individuals representing themselves in legal matters.</p>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Basic Plan -->
                        <div class="card bg-base-200 shadow-md border-2 border-warning">
                            <div class="card-body">
                                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-warning text-warning-content px-4 py-1 rounded-full text-sm font-bold">
                                    3-Month Commitment
                                </div>
                                <h4 class="card-title">Pro Se Basic</h4>
                                <div class="text-center mb-4">
                                    <div class="text-3xl font-bold text-primary">$57.00<span class="text-base font-normal text-base-content/70"> upfront</span></div>
                                    <div class="text-sm text-base-content/70 mt-1">then $19.00/month</div>
                                    <div class="text-xs text-warning font-medium mt-2">3-month minimum commitment</div>
                                </div>

                                <div class="divider"></div>

                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>2,000 AI credits monthly</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>1 active case</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>Core document management</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>1 collaborator</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>Standard support</span>
                                    </li>
                                </ul>

                                <div class="alert alert-info text-xs mt-4">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-4 h-4"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                                    <span>Pay $57 today, then $19/month after 3 months</span>
                                </div>

                                <div class="card-actions justify-end mt-6">
                                    <button type="button" onclick="selectPlan('pro_se_basic_upfront')" class="btn btn-primary">
                                        Select Plan
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Standard Plan -->
                        <div class="card bg-base-200 shadow-md border-2 border-primary">
                            <div class="card-body">
                                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2 bg-primary text-primary-content px-4 py-1 rounded-full text-sm font-bold">
                                    Most Popular
                                </div>
                                <h4 class="card-title">Pro Se Standard</h4>
                                <div class="text-3xl font-bold text-primary mt-2">$49.00<span class="text-base font-normal text-base-content/70">/month</span></div>

                                <div class="divider"></div>

                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>6,000 AI credits monthly</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>2-3 cases</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>Enhanced AI capabilities</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>2 collaborators</span>
                                    </li>
                                </ul>

                                <div class="card-actions justify-end mt-6">
                                    <button type="button" onclick="selectPlan('pro_se_standard')" class="btn btn-primary">
                                        Select Plan
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Plus Plan -->
                        <div class="card bg-base-200 shadow-md">
                            <div class="card-body">
                                <h4 class="card-title">Pro Se Plus</h4>
                                <div class="text-3xl font-bold text-primary mt-2">$99.00<span class="text-base font-normal text-base-content/70">/month</span></div>

                                <div class="divider"></div>

                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>25,000 AI credits monthly</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>Unlimited case files</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>All features</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>5 collaborators</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>Priority support</span>
                                    </li>
                                </ul>

                                <div class="card-actions justify-end mt-6">
                                    <button type="button" onclick="selectPlan('pro_se_plus')" class="btn btn-primary">
                                        Select Plan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                @if(auth()->user()->is_attorney)
                @php $attorneyPlansShown = true; @endphp
                <!-- Attorney Plans -->
                <div>
                    <h3 class="text-xl font-semibold mb-4">Attorney Plans</h3>
                    <p class="mb-6">For licensed attorneys and legal professionals.</p>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <!-- Attorney Basic Plan -->
                        <div class="card bg-base-200 shadow-md">
                            <div class="card-body">
                                <h4 class="card-title">Attorney Basic</h4>
                                <div class="text-3xl font-bold text-primary mt-2">$79.00<span class="text-base font-normal text-base-content/70">/month</span></div>

                                <div class="divider"></div>

                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>6,000 AI credits monthly</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>5 active cases</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>15 lead contacts</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>2 collaborators</span>
                                    </li>
                                </ul>

                                <div class="card-actions justify-end mt-6">
                                    <button type="button" onclick="selectPlan('attorney_basic')" class="btn btn-primary">
                                        Select Plan
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Attorney Pro Plan -->
                        <div class="card bg-base-200 shadow-md">
                            <div class="card-body">
                                <h4 class="card-title">Attorney Pro</h4>
                                <div class="text-3xl font-bold text-primary mt-2">$199.00<span class="text-base font-normal text-base-content/70">/month</span></div>

                                <div class="divider"></div>

                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>10,000 AI credits monthly</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>Unlimited case files</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>35 lead contacts</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>5 collaborators</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>Priority support</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>All features</span>
                                    </li>
                                </ul>

                                <div class="card-actions justify-end mt-6">
                                    <button type="button" onclick="selectPlan('attorney_pro')" class="btn btn-primary">
                                        Select Plan
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Attorney Enterprise Plan -->
                        <div class="card bg-base-200 shadow-md">
                            <div class="card-body">
                                <h4 class="card-title">Attorney Enterprise</h4>
                                <div class="text-3xl font-bold text-primary mt-2">$399.00<span class="text-base font-normal text-base-content/70">/month</span></div>

                                <div class="divider"></div>

                                <ul class="space-y-2">
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>20,000 AI credits monthly</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>Unlimited case files</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>60+ lead contacts</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>Multi-user access</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>Custom branding</span>
                                    </li>
                                    <li class="flex items-start">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-success mr-2 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                        <span>Priority support</span>
                                    </li>
                                </ul>

                                <div class="card-actions justify-end mt-6">
                                    <button type="button" onclick="selectPlan('attorney_enterprise')" class="btn btn-primary">
                                        Select Plan
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                @endif

                @if(!$proSePlansShown && !$attorneyPlansShown)
                <div class="alert alert-info shadow-lg my-8">
                    <div>
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current flex-shrink-0 w-6 h-6"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>
                        <span>No subscription plans are currently available for your account type. Please contact support for assistance.</span>
                    </div>
                </div>
                @endif

                <!-- Payment Form (Hidden by default) -->
                <div id="payment-form-container" class="hidden mt-10">
                    <div class="divider"></div>

                    <h3 class="text-xl font-semibold mb-4">Payment Details</h3>

                    <div class="card bg-base-200 shadow-md p-6">
                        <div class="mb-4">
                            <p class="text-lg font-medium">Selected Plan: <span id="selected-plan-name"></span></p>
                            <p class="text-lg font-medium">Price: <span id="selected-plan-price"></span></p>
                        </div>

                        <form id="payment-form" action="{{ route('subscriptions.store') }}" method="POST">
                            @csrf
                            <input type="hidden" name="plan" id="plan-id">

                            <div class="mb-4">
                                <label for="card-element" class="block text-sm font-medium mb-2">
                                    Credit or debit card
                                </label>
                                <div id="card-element" class="p-3 border rounded-md bg-base-100">
                                    <!-- Stripe Card Element will be inserted here -->
                                </div>
                                <div id="card-errors" class="text-error text-sm mt-2" role="alert"></div>
                            </div>

                            <div class="flex justify-end mt-6">
                                <button type="button" onclick="cancelPayment()" class="btn btn-ghost mr-2">
                                    Cancel
                                </button>
                                <button type="submit" id="submit-button" class="btn btn-primary">
                                    Subscribe
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script src="https://js.stripe.com/v3/"></script>
    <script>
        // Toggle credit info section for subscriptions page
        function toggleCreditInfoSubscriptions() {
            const content = document.getElementById('credit-info-content-subscriptions');
            const arrow = document.getElementById('credit-info-arrow-subscriptions');

            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                arrow.classList.add('rotate-180');
            } else {
                content.classList.add('hidden');
                arrow.classList.remove('rotate-180');
            }
        }

        // Initialize Stripe
        const stripe = Stripe('{{ env('STRIPE_KEY') }}');
        const elements = stripe.elements();

        // Create card element
        const cardElement = elements.create('card');
        cardElement.mount('#card-element');

        // Handle form submission
        const form = document.getElementById('payment-form');
        const submitButton = document.getElementById('submit-button');

        form.addEventListener('submit', async (event) => {
            event.preventDefault();

            // Disable the submit button to prevent multiple submissions
            submitButton.disabled = true;
            submitButton.innerHTML = '<span class="loading loading-spinner loading-sm"></span> Processing...';

            // Create payment method
            const {paymentMethod, error} = await stripe.createPaymentMethod({
                type: 'card',
                card: cardElement,
            });

            if (error) {
                // Display error
                const errorElement = document.getElementById('card-errors');
                errorElement.textContent = error.message;

                // Re-enable the submit button
                submitButton.disabled = false;
                submitButton.innerHTML = 'Subscribe';
            } else {
                // Add payment method ID to the form
                const hiddenInput = document.createElement('input');
                hiddenInput.setAttribute('type', 'hidden');
                hiddenInput.setAttribute('name', 'payment_method');
                hiddenInput.setAttribute('value', paymentMethod.id);
                form.appendChild(hiddenInput);

                // Submit the form
                form.submit();
            }
        });

        // Plan data
        const plans = {
            'pro_se_basic_upfront': {
                name: 'Pro Se Basic (3-Month Commitment)',
                price: '$57.00 upfront, then $19.00/month',
                credits: 2000
            },
            'pro_se_basic': {
                name: 'Pro Se Basic',
                price: '$19.00/month',
                credits: 2000
            },
            'pro_se_standard': {
                name: 'Pro Se Standard',
                price: '$49.00/month',
                credits: 6000
            },
            'pro_se_plus': {
                name: 'Pro Se Plus',
                price: '$99.00/month',
                credits: 25000
            },
            'attorney_basic': {
                name: 'Attorney Basic',
                price: '$79.00/month',
                credits: 5000
            },
            'attorney_pro': {
                name: 'Attorney Pro',
                price: '$199.00/month',
                credits: 10000
            },
            'attorney_enterprise': {
                name: 'Attorney Enterprise',
                price: '$399.00/month',
                credits: 20000
            }
        };

        // Function to select a plan
        function selectPlan(planId) {
            document.getElementById('plan-id').value = planId;
            document.getElementById('selected-plan-name').textContent = plans[planId].name;
            document.getElementById('selected-plan-price').textContent = plans[planId].price;

            document.getElementById('payment-form-container').classList.remove('hidden');

            // Scroll to payment form
            document.getElementById('payment-form-container').scrollIntoView({
                behavior: 'smooth'
            });
        }

        // Function to cancel payment
        function cancelPayment() {
            document.getElementById('payment-form-container').classList.add('hidden');
        }
    </script>
    @endpush
</x-app-layout>
