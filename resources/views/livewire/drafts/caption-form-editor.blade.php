<div class="p-4 space-y-6 border rounded-lg bg-base-100 border-base-300">
    <div class="flex items-center justify-between mb-4">
        <div class="text-lg font-medium">{{ __('app.caption_information') }}</div>
        @if ($isSaving)
            <div class="flex items-center text-sm text-success">
                <svg class="w-4 h-4 mr-1 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none"
                    viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                        stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                </svg>
                {{ __('app.saving') }}...
            </div>
        @endif
    </div>

    <!-- Court Information -->
    <div class="space-y-4">
        <h3 class="font-medium text-md">{{ __('app.court_information') }}</h3>

        <div class="w-full form-control">
            <label class="label">
                <span class="label-text">{{ __('app.court_name') }}</span>
            </label>
            <input type="text" wire:model.live.debounce.500ms="courtName" class="w-full input input-bordered"
                placeholder="DISTRICT COURT FOR THE NORTHERN DISTRICT OF GEORGIA" />
        </div>

        <div class="w-full form-control">
            <label class="label">
                <span class="label-text">{{ __('app.division') }}</span>
            </label>
            <input type="text" wire:model.live.debounce.500ms="division" class="w-full input input-bordered"
                placeholder="Atlanta Division" />
        </div>

        <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div class="w-full form-control">
                <label class="label">
                    <span class="label-text">{{ __('app.case_number') }}</span>
                </label>
                <input type="text" wire:model.live.debounce.500ms="caseNumber" class="w-full input input-bordered"
                    placeholder="1:24-cv-05040-MLB" />
            </div>

            <div class="w-full form-control">
                <label class="label">
                    <span class="label-text">{{ __('app.judge_name') }}</span>
                </label>
                <input type="text" wire:model.live.debounce.500ms="judgeName" class="w-full input input-bordered"
                    placeholder="HON. JUDGE MICHAEL L BROWN" />
            </div>
        </div>
    </div>

    <!-- Plaintiffs -->
    <div class="space-y-4">
        <div class="flex items-center justify-between">
            <h3 class="font-medium text-md">{{ __('app.plaintiffs') }}</h3>
            <button type="button" wire:click="addPlaintiff" class="btn btn-sm btn-outline">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                {{ __('app.add_plaintiff') }}
            </button>
        </div>

        @foreach ($plaintiffs as $index => $plaintiff)
            <div class="flex items-end gap-2">
                <div class="flex-1 form-control">
                    <label class="label">
                        <span class="label-text">{{ __('app.name') }}</span>
                    </label>
                    <input type="text" wire:model.live.debounce.500ms="plaintiffs.{{ $index }}.name"
                        class="w-full input input-bordered" placeholder="John Doe" />
                </div>

                <div class="w-1/3 form-control">
                    <label class="label">
                        <span class="label-text">{{ __('app.role') }}</span>
                    </label>
                    <select wire:model.live.debounce.300ms="plaintiffs.{{ $index }}.role"
                        class="w-full select select-bordered">
                        <option value="Plaintiff">{{ __('app.plaintiff') }}</option>
                        <option value="Petitioner">{{ __('app.petitioner') }}</option>
                    </select>
                </div>

                @if (count($plaintiffs) > 1)
                    <button type="button" wire:click="removePlaintiff({{ $index }})"
                        class="mb-2 btn btn-sm btn-error">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                @endif
            </div>
        @endforeach
    </div>

    <!-- Defendants -->
    <div class="space-y-4">
        <div class="flex items-center justify-between">
            <h3 class="font-medium text-md">{{ __('app.defendants') }}</h3>
            <button type="button" wire:click="addDefendant" class="btn btn-sm btn-outline">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                {{ __('app.add_defendant') }}
            </button>
        </div>

        @foreach ($defendants as $index => $defendant)
            <div class="flex items-end gap-2">
                <div class="flex-1 form-control">
                    <label class="label">
                        <span class="label-text">Name</span>
                    </label>
                    <input type="text" wire:model.live.debounce.500ms="defendants.{{ $index }}.name"
                        class="w-full input input-bordered" placeholder="XYZ Corporation" />
                </div>

                <div class="w-1/3 form-control">
                    <label class="label">
                        <span class="label-text">Role</span>
                    </label>
                    <select wire:model.live.debounce.300ms="defendants.{{ $index }}.role"
                        class="w-full select select-bordered">
                        <option value="Defendant">{{ __('app.defendant') }}</option>
                        <option value="Respondent">{{ __('app.respondent') }}</option>
                    </select>
                </div>

                @if (count($defendants) > 1)
                    <button type="button" wire:click="removeDefendant({{ $index }})"
                        class="mb-2 btn btn-sm btn-error">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                @endif
            </div>
        @endforeach
    </div>

    <!-- Document Title -->
    <div class="w-full form-control">
        <label class="label">
            <span class="label-text">{{ __('app.document_title') }}</span>
        </label>
        <input type="text" wire:model.live.debounce.500ms="documentTitle" class="w-full input input-bordered"
            placeholder="MOTION TO COMPEL ARBITRATION AND STAY PROCEEDINGS" />
    </div>
</div>

<!-- We no longer need the setTimeout script since we're handling the saving state directly in PHP -->
