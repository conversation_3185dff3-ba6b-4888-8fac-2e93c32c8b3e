<div class="overflow-hidden shadow-xl bg-base-100 sm:rounded-lg">
    <div class="flex items-center justify-between p-4 border-b border-base-300">
        <div class="flex items-center space-x-2">
            <h3 class="text-lg font-medium">{{ __('app.document_editor') }}</h3>
            @if ($lastSaved)
                <span class="text-xs text-base-content/60">Last saved at {{ $lastSaved }}</span>
            @endif
        </div>
        <div class="flex items-center space-x-2">
            <div class="form-control">
                <label class="flex space-x-2 cursor-pointer label">
                    <span class="label-text">{{ __('app.auto_save') }}</span>
                    <input type="checkbox" class="toggle toggle-primary toggle-sm" wire:model.live="autoSave"
                        wire:click="toggleAutoSave" />
                </label>
            </div>
            <button class="btn btn-primary btn-sm" wire:click="saveContent">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5 mr-1" fill="none" viewBox="0 0 24 24"
                    stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                </svg>
                {{ __('app.save') }}
            </button>
        </div>
    </div>

    <!-- Simple Editor Content -->
    <div class="p-4 bg-white">
        <textarea wire:model.debounce.1000ms="content"
            class="w-full min-h-[600px] p-4 border border-base-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary"
            placeholder="{{ __('app.start_typing') }}"></textarea>
    </div>
</div>
