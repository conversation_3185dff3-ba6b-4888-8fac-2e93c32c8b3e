<div class="bg-base-200 p-4 rounded-lg">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-lg font-medium">{{ __('app.exhibits') }}</h3>
        <button wire:click="$set('showAddExhibitModal', true)" class="btn btn-sm btn-primary">
            {{ __('app.add_exhibit') }}
        </button>
    </div>

    @if ($exhibits->isEmpty())
        <div class="text-center py-4 text-base-content/70">
            <p>{{ __('app.no_exhibits_yet') }}</p>
        </div>
    @else
        <div class="space-y-2 max-h-64 overflow-y-auto">
            @foreach ($exhibits as $exhibit)
                <div class="bg-base-100 p-3 rounded-lg">
                    <div class="flex justify-between items-start">
                        <div>
                            <span class="font-medium">{{ __('app.exhibit') }} {{ $exhibit->label }}</span>
                            <p class="text-sm text-base-content/70">{{ $exhibit->description }}</p>
                        </div>
                        <div class="flex space-x-1">
                            <button wire:click="insertExhibitReference({{ $exhibit->id }}, 'standard')"
                                class="btn btn-xs btn-ghost" title="Insert reference">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                                </svg>
                            </button>
                            <div class="dropdown dropdown-end">
                                <label tabindex="0" class="btn btn-xs btn-ghost">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 9l-7 7-7-7" />
                                    </svg>
                                </label>
                                <ul tabindex="0"
                                    class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
                                    <li><a
                                            wire:click="insertExhibitReference({{ $exhibit->id }}, 'standard')">{{ __('app.standard') }}</a>
                                    </li>
                                    <li><a
                                            wire:click="insertExhibitReference({{ $exhibit->id }}, 'detailed')">{{ __('app.detailed') }}</a>
                                    </li>
                                    <li><a
                                            wire:click="insertExhibitReference({{ $exhibit->id }}, 'legal')">{{ __('app.legal') }}</a>
                                    </li>
                                    <li><a
                                            wire:click="insertExhibitReference({{ $exhibit->id }}, 'parenthetical')">{{ __('app.parenthetical') }}</a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    @endif

    <!-- Add Exhibit Modal -->
    <x-modal wire:model="showAddExhibitModal">
        <x-slot name="title">{{ __('app.add_new_exhibit') }}</x-slot>
        <x-slot name="content">
            @livewire('exhibits.create-exhibit-form', ['caseFileId' => $caseFileId, 'draftId' => $draftId])
        </x-slot>
    </x-modal>
</div>
