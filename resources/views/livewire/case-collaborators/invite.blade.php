<div>
    <button
        class="btn btn-primary btn-sm"
        wire:click="$toggle('isOpen')"
    >
        {{ __('collaboration.buttons.invite') }}
    </button>

    @if($isOpen)
        <div class="modal modal-open">
            <div class="modal-box">
                <h3 class="font-bold text-lg">{{ __('collaboration.headers.invite') }}</h3>

                <form wire:submit="invite" class="space-y-4 mt-4">
                    <div>
                        <label class="label">
                            <span class="label-text">{{ __('collaboration.labels.email') }}</span>
                        </label>
                        <input
                            type="email"
                            wire:model="email"
                            class="input input-bordered w-full @error('email') input-error @enderror"
                            placeholder="<EMAIL>"
                        >
                        @error('email')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>

                    <div>
                        <label class="label">
                            <span class="label-text">{{ __('collaboration.labels.role') }}</span>
                        </label>
                        <select
                            wire:model="role"
                            class="select select-bordered w-full @error('role') select-error @enderror"
                        >
                            <option value="viewer">{{ __('collaboration.roles.viewer') }}</option>
                            <option value="editor">{{ __('collaboration.roles.editor') }}</option>
                            <option value="manager">{{ __('collaboration.roles.manager') }}</option>
                        </select>
                        @error('role')
                            <label class="label">
                                <span class="label-text-alt text-error">{{ $message }}</span>
                            </label>
                        @enderror
                    </div>

                    <div class="modal-action">
                        <button type="button" class="btn" wire:click="$set('isOpen', false)">{{ __('common.cancel') }}</button>
                        <button type="submit" class="btn btn-primary">{{ __('collaboration.buttons.invite') }}</button>
                    </div>
                </form>
            </div>
            <div class="modal-backdrop" wire:click="$set('isOpen', false)"></div>
        </div>
    @endif
</div>
