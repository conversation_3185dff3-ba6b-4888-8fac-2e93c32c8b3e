<div class="bg-base-100 shadow-xl sm:rounded-lg overflow-hidden">
    <div class="p-6 border-b border-base-content/10">
        <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
            <div class="space-y-1">
                <h3 class="text-2xl font-medium text-base-content">
                    {{ __('strategy.title') }}
                </h3>
                <p class="text-base text-base-content/60">
                    {{ __('strategy.subtitle') }}
                </p>
            </div>
            <div class="flex items-center">
                <div class="grid grid-cols-1 sm:flex sm:flex-wrap gap-2 w-full">
                    <button class="btn btn-sm btn-primary" wire:click="saveStrategy" wire:loading.attr="disabled">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                        </svg>
                        {{ __('strategy.save_strategy') }}
                    </button>

                    <button class="btn btn-sm btn-secondary" wire:click="togglePromptMode">
                        @if ($promptMode === 'guided')
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                            </svg>
                            {{ __('strategy.switch_to_conversation') }}
                        @else
                            <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            {{ __('strategy.switch_to_guided') }}
                        @endif
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Strategy Builder Content -->
    <div class="p-6">
        <!-- Include the chat interface component -->
        <livewire:chat.chat-interface />

        @if ($promptMode === 'guided')
            <!-- Guided Mode -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="text-lg font-medium text-base-content">{{ __('strategy.strategy_development') }}</h4>
                    <button class="btn btn-primary btn-sm" wire:click="generateStrategy" wire:loading.attr="disabled">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z" />
                        </svg>
                        {{ __('strategy.generate_strategy') }}
                    </button>
                </div>

                @if ($isGenerating)
                    <div class="alert alert-info">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                            class="stroke-current shrink-0 w-6 h-6">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>{{ __('strategy.generating') }}</span>
                    </div>
                @endif

                @if ($generationError)
                    <div class="alert alert-error">
                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <span>{{ __('strategy.generation_error') }} {{ $generationError }}</span>
                    </div>
                @endif
            </div>

            <!-- Tabs for different sections -->
            <div class="tabs tabs-boxed mb-6">
                <a class="tab {{ $activeTab === 'overview' ? 'tab-active' : '' }}"
                    wire:click="setActiveTab('overview')">{{ __('strategy.tabs.overview') }}</a>
                <a class="tab {{ $activeTab === 'strategy' ? 'tab-active' : '' }}"
                    wire:click="setActiveTab('strategy')">{{ __('strategy.tabs.strategy') }}</a>
                <a class="tab {{ $activeTab === 'legal' ? 'tab-active' : '' }}"
                    wire:click="setActiveTab('legal')">{{ __('strategy.tabs.legal') }}</a>
                <a class="tab {{ $activeTab === 'actions' ? 'tab-active' : '' }}"
                    wire:click="setActiveTab('actions')">{{ __('strategy.tabs.actions') }}</a>
                <a class="tab {{ $activeTab === 'notes' ? 'tab-active' : '' }}"
                    wire:click="setActiveTab('notes')">{{ __('strategy.tabs.notes') }}</a>
            </div>

            <!-- Tab Content -->
            <div class="tab-content">
                @if ($activeTab === 'overview')
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">{{ __('strategy.fields.executive_summary') }}</span>
                        </label>
                        <textarea class="textarea textarea-bordered h-64" wire:model="executiveSummary"
                            placeholder="{{ __('strategy.fields.executive_summary_placeholder') }}"></textarea>
                    </div>
                @elseif($activeTab === 'strategy')
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">{{ __('strategy.fields.strategy_recommendations') }}</span>
                        </label>
                        <textarea class="textarea textarea-bordered h-64" wire:model="strategyData.content"
                            placeholder="{{ __('strategy.fields.strategy_recommendations_placeholder') }}"></textarea>
                    </div>
                @elseif($activeTab === 'legal')
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">{{ __('strategy.fields.legal_analysis') }}</span>
                        </label>
                        <textarea class="textarea textarea-bordered h-64" wire:model="legalAnalysis.content"
                            placeholder="{{ __('strategy.fields.legal_analysis_placeholder') }}"></textarea>
                    </div>
                @elseif($activeTab === 'actions')
                    <div class="space-y-4">
                        <label class="label">
                            <span class="label-text">{{ __('strategy.fields.action_items') }}</span>
                        </label>

                        @if (is_array($actionItems) && count($actionItems) > 0)
                            @foreach ($actionItems as $index => $item)
                                <div class="flex items-start gap-2">
                                    <input type="text" class="input input-bordered w-full"
                                        wire:model="actionItems.{{ $index }}"
                                        placeholder="{{ __('strategy.fields.action_item_placeholder') }}" />
                                    <button class="btn btn-square btn-sm btn-error"
                                        wire:click="() => { array_splice($this->actionItems, {{ $index }}, 1); }">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none"
                                            viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                            @endforeach
                        @else
                            <div class="alert alert-info">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                    class="stroke-current shrink-0 w-6 h-6">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>{{ __('strategy.fields.no_action_items') }}</span>
                            </div>
                        @endif

                        <button class="btn btn-sm btn-secondary"
                            wire:click="() => { $this->actionItems = is_array($this->actionItems) ? $this->actionItems : []; $this->actionItems[] = ''; }">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M12 4v16m8-8H4" />
                            </svg>
                            {{ __('strategy.fields.add_action_item') }}
                        </button>
                    </div>
                @elseif($activeTab === 'notes')
                    <div class="form-control">
                        <label class="label">
                            <span class="label-text">{{ __('strategy.fields.notes') }}</span>
                        </label>
                        <textarea class="textarea textarea-bordered h-64" wire:model="notes"
                            placeholder="{{ __('strategy.fields.notes_placeholder') }}"></textarea>
                    </div>
                @endif
            </div>
        @else
            <!-- Conversation Mode is handled by the ChatInterface component -->
            <div class="hidden"><!-- This section is intentionally empty --></div>
        @endif
    </div>
</div>

<script>
    document.addEventListener('livewire:initialized', function() {
        // Function to scroll to bottom of conversation
        function scrollToBottom() {
            const container = document.getElementById('conversation-container');
            if (container) {
                container.scrollTop = container.scrollHeight;
            }
        }

        // Scroll to bottom on initial load
        scrollToBottom();

        // Scroll to bottom when messages are updated
        @this.on('messagesUpdated', function() {
            // Use setTimeout to ensure DOM is updated before scrolling
            setTimeout(scrollToBottom, 100);
        });

        // Scroll to bottom when conversation mode is activated
        @this.on('conversationModeActivated', function() {
            setTimeout(scrollToBottom, 100);
        });
    });
</script>
