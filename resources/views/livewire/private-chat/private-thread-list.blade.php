<div class="bg-base-100 rounded-lg shadow-md p-4 h-full">
    <h3 class="text-lg font-semibold mb-4">{{ __('private-chat.conversations') }}</h3>

    <!-- Search Input -->
    <div class="relative mb-4">
        <input
            type="text"
            wire:model.live="search"
            placeholder="{{ __('private-chat.search_conversations') }}"
            class="input input-bordered w-full pl-10 input-sm"
        >
        <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg class="w-4 h-4 text-base-content/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
        </div>
        @if($search)
            <button
                wire:click="$set('search', '')"
                class="absolute inset-y-0 right-0 flex items-center pr-3 text-base-content/50 hover:text-base-content"
            >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        @endif
    </div>

    <!-- Thread List -->
    <div class="space-y-2 overflow-y-auto max-h-[calc(100vh-12rem)]">
        @forelse($threads as $thread)
            @php
                $isUnread = $thread->pivot->last_read_at === null ||
                            $thread->last_activity_at > $thread->pivot->last_read_at;
                $isActive = $thread->id == $activeThreadId;
                $otherParticipants = $thread->participants;
            @endphp

            <a
                href="{{ route('private-chat.show', $thread->id) }}"
                class="block p-3 rounded-lg transition-colors {{ $isActive ? 'bg-primary/20' : ($isUnread ? 'bg-base-200 hover:bg-base-300' : 'hover:bg-base-200') }}"
            >
                <div class="flex items-center">
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium truncate {{ $isUnread ? 'font-bold' : '' }}">
                            {{ $thread->getDisplayTitle(auth()->user()) }}
                        </p>
                        <p class="text-xs text-base-content/70 truncate">
                            @foreach($otherParticipants as $participant)
                                <span class="inline-flex items-center">
                                    {{ $participant->username }}
                                    <span class="ml-1 px-1.5 py-0.5 bg-base-300 text-xs rounded">
                                        {{ strtoupper($participant->language ?? 'en') }}
                                    </span>
                                </span>
                                @if(!$loop->last), @endif
                            @endforeach
                        </p>
                    </div>
                    <div class="ml-2 flex-shrink-0">
                        @if($isUnread)
                            <span class="inline-block w-2 h-2 bg-primary rounded-full"></span>
                        @endif
                    </div>
                </div>
            </a>
        @empty
            <div class="text-center py-8 text-base-content/70">
                <p>{{ __('private-chat.no_conversations') }}</p>
                <a href="{{ route('users.search') }}" class="btn btn-sm btn-primary mt-2">
                    {{ __('private-chat.find_users') }}
                </a>
            </div>
        @endforelse
    </div>
</div>

@push('scripts')
<script>
    // Function to initialize Echo listeners for thread list
    const initializeThreadListEchoListeners = () => {
        if (!window.Echo) {
            console.error('Echo is not available for thread list!');
            return;
        }

        console.log('Echo is available for thread list');

        // Subscribe to the private channel with wildcard
        try {
            window.Echo.private('private-chat.*')
                .listen('.new-message', (event) => {
                    alert('Thread list received new message!');
                    console.log('Thread list received new-message event:', event);
                    // Livewire will handle this via the echo-private listener
                });

            console.log('Subscribed to private-chat.* wildcard channel');
        } catch (error) {
            console.error('Error subscribing to wildcard channel:', error);
        }
    };

    // Initialize Echo listeners when Echo is ready
    if (window.Echo) {
        initializeThreadListEchoListeners();
    } else {
        // Listen for the echo:ready event
        document.addEventListener('echo:ready', initializeThreadListEchoListeners);

        // Fallback: check periodically if Echo becomes available
        const checkEcho = setInterval(() => {
            if (window.Echo) {
                clearInterval(checkEcho);
                initializeThreadListEchoListeners();
            }
        }, 100);
    }
</script>
@endpush
