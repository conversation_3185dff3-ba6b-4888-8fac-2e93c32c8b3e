
<div class="flex flex-col h-full overflow-hidden chat-container" wire:poll.5s="getNewMessages">
    <!-- Cha<PERSON>er -->
    <div class="bg-base-200 p-4 rounded-t-lg shadow-sm">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-lg font-semibold">{{ $thread->getDisplayTitle(auth()->user()) }}</h2>
                <div class="text-sm text-base-content/70">
                    @foreach($otherParticipants as $participant)
                        <span class="inline-flex items-center mr-2">
                            {{ $participant->username }}
                            <span class="ml-1 px-1.5 py-0.5 bg-base-300 text-xs rounded">
                                {{ strtoupper($participant->language ?? 'en') }}
                            </span>
                        </span>
                    @endforeach
                </div>
            </div>
            <div class="flex items-center space-x-2">
                <!-- Video Call Button -->
                <button
                    type="button"
                    class="btn btn-sm btn-primary"
                    onclick="startVideoCall('{{ $thread->id }}')"
                    title="{{ __('private-chat.start_video_call') }}"
                >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                </button>

                <!-- Close Button -->
                <a href="{{ route('private-chat.index') }}" class="btn btn-sm btn-ghost">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </a>
            </div>
        </div>

        <!-- Language Notice -->
        <div class="mt-2 p-2 bg-info/20 text-info-content rounded text-sm">
            <p>
                <span class="font-semibold">{{ __('private-chat.language_notice') }}</span>
                {{ __('private-chat.translation_notice', ['language' => strtoupper($currentUserLanguage)]) }}
            </p>
        </div>
    </div>

    <!-- Messages Container -->
    <div class="flex-1 overflow-y-auto p-4 space-y-4 min-h-0 bg-base-300 max-h-screen" id="messages-container">
        <!-- Load More Button -->
        @if($hasMoreMessages)
            <div class="flex justify-center mb-4">
                <button wire:click="loadMore" class="btn btn-sm btn-outline" wire:loading.attr="disabled">
                    <span wire:loading.remove wire:target="loadMore">{{ __('private-chat.load_more') }}</span>
                    <span wire:loading wire:target="loadMore" class="loading loading-spinner loading-sm"></span>
                </button>
            </div>
        @endif

        @php
            $currentDate = null;
        @endphp

        @foreach($messages as $index => $message)
            @php
                // Check if we need to show a divider for newly loaded messages
                $showNewMessagesDivider = $lastLoadedCount > 0 && $index === $lastLoadedCount - 1;

                // Check if we're dealing with an array or object
                $userId = is_array($message) ? $message['user_id'] : $message->user_id;
                $userName = is_array($message) ? $message['user']['name'] : $message->user->name;
                $userPhoto = is_array($message) ? $message['user']['profile_photo_url'] : $message->user->profile_photo_url;
                $createdAt = is_array($message) ? \Carbon\Carbon::parse($message['created_at']) : $message->created_at;
                $originalLanguage = is_array($message) ? $message['original_language'] : $message->original_language;

                // Check if we need to show a date divider
                $messageDate = $createdAt->format('Y-m-d');
                $showDateDivider = $currentDate !== $messageDate;
                $currentDate = $messageDate;
            @endphp

            @if($showNewMessagesDivider)
                <div class="divider my-4">
                    <div class="badge badge-neutral">{{ __('private-chat.new_messages_above') }}</div>
                </div>
            @endif

            @if($showDateDivider)
                <div class="divider my-2">
                    <div class="badge badge-ghost">{{ $createdAt->format('F j, Y') }}</div>
                </div>
            @endif

            @php
                // Get the content to display
                $displayContent = '';
                if (is_array($message)) {
                    // If the message is in the user's language, show the original
                    if ($originalLanguage === $currentUserLanguage) {
                        $displayContent = $message['original_content'];
                    }
                    // If we have a translation, show it
                    elseif (!empty($message['translated_content'])) {
                        $displayContent = $message['translated_content'];
                    }
                    // Fallback to original content
                    else {
                        $displayContent = $message['original_content'];
                    }
                } else {
                    $displayContent = $message->getDisplayContent($currentUserLanguage);
                }
            @endphp

            <div class="chat {{ $userId === auth()->id() ? 'chat-end' : 'chat-start' }} break-words">
                <div class="chat-image avatar">
                    <div class="w-10 rounded-full">
                        <img src="{{ $userPhoto }}" alt="{{ $userName }}" />
                    </div>
                </div>
                <div class="chat-header">
                    {{ $userName }}
                    <time class="text-xs opacity-50">{{ $createdAt->format('M j, g:i a') }}</time>
                </div>
                <div class="chat-bubble {{ $userId === auth()->id() ? 'chat-bubble-primary' : 'chat-bubble-secondary' }} break-words max-w-xs sm:max-w-sm md:max-w-md">
                    {{ $displayContent }}
                </div>
                <div class="chat-footer opacity-50 text-xs">
                    {{ $originalLanguage !== $currentUserLanguage ? __('private-chat.translated_from', ['language' => strtoupper($originalLanguage)]) : '' }}
                </div>
            </div>
        @endforeach

        @if($messages->isEmpty())
            <div class="text-center py-8 text-base-content/70">
                <p>{{ __('private-chat.no_messages') }}</p>
            </div>
        @endif
    </div>

{{--    <!-- Polling Indicator -->--}}
{{--    <div wire:loading wire:target="getNewMessages" class="text-center py-2 bg-info/20 text-info-content text-xs">--}}
{{--        <span class="loading loading-spinner loading-xs"></span> {{ __('private-chat.checking_messages') }}--}}
{{--    </div>--}}

    <!-- Message Input -->
    <div class="p-4 bg-base-200 rounded-b-lg shadow-sm">
        <livewire:voice-message-input
            name="private_chat_message"
            wire:model="message"
            height="80px"
            :placeholder="__('private-chat.type_message')"
            :showSendButton="true"
            :sendButtonText="__('private-chat.send')"
            :disabled="$isLoading"
        />
    </div>
</div>

@push('scripts')
<script>
    // Variables to track scroll position
    let lastScrollHeight = 0;
    let lastScrollTop = 0;
    let isLoadingMore = false;
    let isNearBottom = true; // Track if user is near the bottom of the chat

    // Auto-scroll to bottom of messages on load and when messages update
    const scrollToBottom = () => {
        const container = document.getElementById('messages-container');
        if (container) {
            // Use a small timeout to ensure the DOM has updated
            setTimeout(() => {
                container.scrollTop = container.scrollHeight;
                console.log('Scrolled to bottom, height:', container.scrollHeight);
            }, 50);
        }
    };

    // Check if user is near the bottom of the chat
    const checkIfNearBottom = () => {
        const container = document.getElementById('messages-container');
        if (container) {
            // Consider "near bottom" if within 100px of the bottom
            const scrollBottom = container.scrollTop + container.clientHeight;
            const isNear = (container.scrollHeight - scrollBottom) < 100;
            isNearBottom = isNear;
            return isNear;
        }
        return false;
    };

    // Function to preserve scroll position when loading more messages
    const preserveScrollPosition = () => {
        const container = document.getElementById('messages-container');
        if (container) {
            // Calculate how much new content was added
            const newScrollHeight = container.scrollHeight;
            const scrollHeightDiff = newScrollHeight - lastScrollHeight;

            // Set the scroll position to maintain the same relative position
            if (scrollHeightDiff > 0) {
                // When loading older messages (which appear at the top), we need to adjust the scroll position
                // to keep the same messages in view
                container.scrollTop = lastScrollTop + scrollHeightDiff;
                console.log('Preserved scroll position after loading more. Diff:', scrollHeightDiff);
            }

            // Update the last known values
            lastScrollHeight = newScrollHeight;
            lastScrollTop = container.scrollTop;
        }
    };

    // Initial scroll on page load
    scrollToBottom();

    // Save scroll position before updates
    const saveScrollPosition = () => {
        const container = document.getElementById('messages-container');
        if (container) {
            lastScrollHeight = container.scrollHeight;
            lastScrollTop = container.scrollTop;
            isLoadingMore = true;
            checkIfNearBottom(); // Update the near bottom state
            console.log('Saved scroll position:', lastScrollTop, 'height:', lastScrollHeight, 'near bottom:', isNearBottom);
        }
    };

    // Add scroll event listener to track position
    document.getElementById('messages-container').addEventListener('scroll', function() {
        checkIfNearBottom();
    });

    // Livewire 3 initialization hook
    Livewire.hook('component.init', ({ component, el }) => {
        if (component.name === 'private-chat.private-chat-interface') {
            scrollToBottom();
            console.log('Private chat interface initialized');

            // Save initial scroll values
            const container = document.getElementById('messages-container');
            if (container) {
                lastScrollHeight = container.scrollHeight;
                lastScrollTop = container.scrollTop;
                isNearBottom = true; // Initially at the bottom
            }
        }
    });

    // Before any updates, save the current scroll position
    Livewire.hook('message.sent', ({ component, message }) => {
        if (component.name === 'private-chat.private-chat-interface') {
            saveScrollPosition();
        }
    });

    // The loadMore method will directly set the data-last-method attribute
    // and call saveScrollPosition() via Livewire's js() method

    // Listen for Livewire updates
    Livewire.hook('morph.updated', ({ component, el }) => {
        if (component.name === 'private-chat.private-chat-interface') {
            // Get the messages container
            const container = document.getElementById('messages-container');
            if (!container) return;

            // Check if we're loading more messages by looking at the data attribute
            const lastMethod = container.getAttribute('data-last-method') || '';

            if (lastMethod === 'loadMore' || isLoadingMore) {
                // Use a small timeout to ensure the DOM has fully updated
                setTimeout(() => {
                    // Preserve scroll position when loading more messages
                    preserveScrollPosition();
                    // Reset the flags
                    container.setAttribute('data-last-method', '');
                    isLoadingMore = false;
                    console.log('Preserved scroll position after loading more messages');
                }, 50);
            } else if (isNearBottom) {
                // Only scroll to bottom for new messages if user was already near the bottom
                scrollToBottom();
                console.log('User was near bottom, scrolled to bottom for new messages');
            } else {
                console.log('User was scrolled up, not scrolling to bottom for new messages');
            }
        }
    });

    // Listen for the checkScrollForNewMessages event
    document.addEventListener('checkScrollForNewMessages', function(event) {
        // Only scroll if user is near the bottom
        if (checkIfNearBottom()) {
            scrollToBottom();
            console.log('New messages received and user was near bottom, scrolling down');
        } else {
            // Show a notification that new messages were received
            const count = event.detail.count || 1;
            const toast = document.createElement('div');
            toast.className = 'fixed top-4 right-4 bg-info text-info-content px-4 py-2 rounded shadow-lg z-50 animate-fade-in-up cursor-pointer';
            toast.innerHTML = `{{ __('private-chat.new_messages', ['count' => '${count}']) }}`;
            document.body.appendChild(toast);

            // Scroll to bottom when the toast is clicked
            toast.addEventListener('click', function() {
                scrollToBottom();
                document.body.removeChild(toast);
            });

            // Remove the toast after 5 seconds
            setTimeout(() => {
                if (document.body.contains(toast)) {
                    toast.classList.add('animate-fade-out');
                    setTimeout(() => {
                        if (document.body.contains(toast)) {
                            document.body.removeChild(toast);
                        }
                    }, 500);
                }
            }, 5000);

            console.log('New messages received but user was scrolled up, showing notification');
        }
    });

    // Comment out Echo listeners since we're using polling instead
    /*
    // Function to initialize Echo listeners
    const initializeEchoListeners = () => {
        if (!window.Echo) {
            console.error('Echo is not available!');
            return;
        }

        console.log('Echo is available for private chat');

        // Subscribe to the private channel for this thread
        try {
            window.Echo.private(`private-chat.${@js($thread->id)}`)
                .listen('.new-message', (event) => {
                    console.log('Received new-message event:', event);
                    // Livewire will handle this via the echo-private listener
                })
                .listenForWhisper('typing', (e) => {
                    console.log('User is typing:', e);
                });

            console.log(`Subscribed to private-chat.${@js($thread->id)}`);
        } catch (error) {
            console.error('Error subscribing to channel:', error);
        }
    };

    // Initialize Echo listeners when Echo is ready
    if (window.Echo) {
        initializeEchoListeners();
    } else {
        // Listen for the echo:ready event
        document.addEventListener('echo:ready', initializeEchoListeners);

        // Fallback: check periodically if Echo becomes available
        const checkEcho = setInterval(() => {
            if (window.Echo) {
                clearInterval(checkEcho);
                initializeEchoListeners();
            }
        }, 100);
    }
    */

    console.log('Using polling for chat updates instead of WebSockets');

    // Create a fallback beep sound using the Web Audio API
    const createBeepSound = () => {
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.type = 'sine';
            oscillator.frequency.value = 880; // A5 note
            gainNode.gain.value = 0.1; // Lower volume

            oscillator.start();

            // Short beep
            setTimeout(() => {
                oscillator.stop();
            }, 200);

            return true;
        } catch (e) {
            console.log('Error creating beep sound:', e);
            return false;
        }
    };

    // Listen for new message event
    document.addEventListener('newMessageReceived', function(event) {
        scrollToBottom();

        // Play notification sound with better error handling
        try {
            // Preload the audio to check if it's valid
            const audio = new Audio();
            audio.preload = 'auto';

            // Use the full URL to ensure proper path resolution
            const baseUrl = window.location.origin;
            audio.src = `${baseUrl}/sounds/sci-notification.wav`;

            let played = false;

            // Add event listeners for debugging
            audio.addEventListener('canplaythrough', () => {
                console.log('Audio can play through');
                audio.play()
                    .then(() => { played = true; })
                    .catch(e => {
                        console.log('Error playing sound after load:', e);
                        // Try the fallback beep if MP3 fails
                        if (!played) createBeepSound();
                    });
            });

            audio.addEventListener('error', (e) => {
                console.log('Audio loading error:', e);
                // Try the fallback beep if MP3 fails to load
                if (!played) createBeepSound();
            });

            // Fallback if the audio doesn't trigger events
            setTimeout(() => {
                if (!played) {
                    if (audio.readyState >= 2) { // HAVE_CURRENT_DATA or better
                        audio.play()
                            .then(() => { played = true; })
                            .catch(e => {
                                console.log('Error playing sound after timeout:', e);
                                // Try the fallback beep
                                createBeepSound();
                            });
                    } else {
                        // If audio isn't ready, use the fallback beep
                        createBeepSound();
                    }
                }
            }, 1000);
        } catch (e) {
            console.log('Error setting up audio:', e);
            // Try the fallback beep if there's an exception
            createBeepSound();
        }

        // Show browser notification if page is not visible
        if (document.hidden && 'Notification' in window) {
            if (Notification.permission === 'granted') {
                new Notification('{{ __('private-chat.new_message') }}', {
                    body: '{{ __('private-chat.new_message') }}',
                    icon: '/favicon.ico'
                });
            } else if (Notification.permission !== 'denied') {
                Notification.requestPermission();
            }
        }

        // Show a toast notification for new messages
        const toast = document.createElement('div');
        toast.className = 'fixed bottom-4 right-4 bg-success text-success-content px-4 py-2 rounded shadow-lg z-50 animate-fade-in-up';
        toast.innerHTML = '{{ __('private-chat.new_message') }}';
        document.body.appendChild(toast);

        // Remove the toast after 3 seconds
        setTimeout(() => {
            toast.classList.add('animate-fade-out');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 500);
        }, 3000);
    });

    // Add CSS animations
    const style = document.createElement('style');
    style.textContent = `
        @keyframes fadeInUp {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        .animate-fade-in-up {
            animation: fadeInUp 0.3s ease-out forwards;
        }
        .animate-fade-out {
            animation: fadeOut 0.5s ease-out forwards;
        }
    `;
    document.head.appendChild(style);
</script>
@endpush
