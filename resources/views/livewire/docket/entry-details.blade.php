<div class="space-y-4 sm:space-y-6">
    @if($entry->description)
        <div>
            <h4 class="font-medium text-sm sm:text-base mb-1 sm:mb-2">{{ __('docket.entry.fields.description') }}</h4>
            <p class="text-sm sm:text-base text-base-content/70">{{ $entry->description }}</p>
        </div>
    @endif

    <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
        @if($entry->filing_party)
            <div>
                <h4 class="font-medium text-sm sm:text-base mb-1 sm:mb-2">{{ __('docket.entry.fields.filing_party') }}</h4>
                <p class="text-sm sm:text-base text-base-content/70">{{ $entry->filing_party }}</p>
            </div>
        @endif

        @if($entry->judge)
            <div>
                <h4 class="font-medium text-sm sm:text-base mb-1 sm:mb-2">{{ __('docket.entry.fields.judge') }}</h4>
                <p class="text-sm sm:text-base text-base-content/70">{{ $entry->judge }}</p>
            </div>
        @endif
    </div>

    @if($relatedDocuments->isNotEmpty())
        <div>
            <h4 class="font-medium text-sm sm:text-base mb-1 sm:mb-2">{{ __('docket.entry.related_documents') }}</h4>
            <ul class="space-y-1 sm:space-y-2">
                @foreach($relatedDocuments as $document)
                    <li class="flex items-center gap-1 sm:gap-2 text-sm sm:text-base">
                        <x-icon name="document" class="w-4 h-4 sm:w-5 sm:h-5 text-base-content/50" />
                        <a
                            href="{{ route('documents.show', $document) }}"
                            class="link link-hover truncate"
                        >
                            {{ $document->title ?: $document->original_filename }}
                        </a>
                        @if($document->exhibits_count)
                            <span class="badge badge-xs sm:badge-sm badge-outline">
                                {{ trans_choice('docket.entry.exhibits_count', $document->exhibits_count) }}
                            </span>
                        @endif
                    </li>
                @endforeach
            </ul>
        </div>
    @endif

    @if($relatedCommunications->isNotEmpty())
        <div>
            <h4 class="font-medium text-sm sm:text-base mb-1 sm:mb-2">{{ __('docket.entry.related_communications') }}</h4>
            <ul class="space-y-1 sm:space-y-2">
                @foreach($relatedCommunications as $communication)
                    <li class="flex flex-wrap items-center gap-1 sm:gap-2 text-sm sm:text-base">
                        <x-icon
                            name="{{ $communication->type }}"
                            class="w-4 h-4 sm:w-5 sm:h-5 text-base-content/50"
                        />
                        <a
                            href="{{ route('communications.show', $communication) }}"
                            class="link link-hover truncate"
                        >
                            {{ $communication->subject ?: __('docket.entry.communication_without_subject') }}
                        </a>
                        <span class="text-xs sm:text-sm text-base-content/50">
                            {{ $communication->sent_at->format('M d, Y') }}
                        </span>
                    </li>
                @endforeach
            </ul>
        </div>
    @endif
</div>