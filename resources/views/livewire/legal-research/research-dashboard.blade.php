<div class="space-y-6">
    <!-- Flash Messages -->
    @if (session()->has('message'))
        <div class="alert alert-success shadow-lg">
            <div>
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                <span>{{ session('message') }}</span>
            </div>
        </div>
    @endif

    @if (session()->has('error'))
        <div class="alert alert-error shadow-lg">
            <div>
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current flex-shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                <span>{{ session('error') }}</span>
            </div>
        </div>
    @endif

    <!-- Tabs -->
    <div class="tabs tabs-boxed">
        <a wire:click="setActiveTab('quick')" class="tab {{ $activeTab === 'quick' ? 'tab-active' : '' }}">
            Quick Search
        </a>
        <a wire:click="setActiveTab('regular')" class="tab {{ $activeTab === 'regular' ? 'tab-active' : '' }}">
            Regular Research
        </a>
        <a wire:click="setActiveTab('deep')" class="tab {{ $activeTab === 'deep' ? 'tab-active' : '' }}">
            Deep Research
        </a>
    </div>

    <!-- Quick Search Tab Content -->
    <div class="{{ $activeTab === 'quick' ? 'block' : 'hidden' }}">
        <div class="bg-white dark:bg-neutral-800 shadow-md rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Quick Web Search</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Search the web for legal information. Get real-time information beyond standard legal databases.
                <span class="font-semibold">Cost: {{ $quickSearchCredits }} credits</span>
            </p>

            @php
                $insufficientQuickCredits = $currentBalance < $quickSearchCredits;
            @endphp

            @if($insufficientQuickCredits)
            <div class="alert alert-warning mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                <div>
                    <h3 class="font-bold">Insufficient Credits</h3>
                    <div class="text-sm">You need at least {{ $quickSearchCredits }} credits to perform a quick search. Your current balance is {{ $currentBalance }} credits.</div>
                    <a href="{{ route('credits.purchase.form') }}" class="btn btn-sm btn-outline mt-2">Purchase Credits</a>
                </div>
            </div>
            @endif

            <form wire:submit.prevent="submitQuickSearch" class="space-y-4">
                <div>
                    <label for="quickSearchQuery" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search Query</label>
                    <textarea id="quickSearchQuery" wire:model="quickSearchQuery" rows="2"
                        class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                        placeholder="e.g., Recent Supreme Court decisions on copyright law"></textarea>
                    @error('quickSearchQuery') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div class="flex justify-end space-x-2">
                    @if($quickSearchResults)
                    <button type="button" class="btn btn-outline" wire:click="clearQuickSearch">
                        Clear
                    </button>
                    @endif
                    <button type="submit" class="btn btn-primary" wire:loading.attr="disabled" wire:target="submitQuickSearch"
                        {{ $insufficientQuickCredits ? 'disabled' : '' }}
                        title="{{ $insufficientQuickCredits ? 'You need ' . $quickSearchCredits . ' credits to perform a quick search' : 'Search the web for legal information' }}">
                        @if($insufficientQuickCredits)
                            Need {{ $quickSearchCredits }} Credits
                        @elseif($isSearching)
                            Searching...
                        @else
                            Search Web
                        @endif
                    </button>
                </div>
            </form>
        </div>

        @if($quickSearchResults)
        <div class="mt-6 bg-white dark:bg-neutral-800 shadow-md rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Search Results</h3>
            <div class="prose dark:prose-invert max-w-none bg-white p-4">
                {!! Str::markdown($quickSearchResults) !!}
            </div>
        </div>
        @endif

        <div class="mt-6 bg-white dark:bg-neutral-800 shadow-md rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Your Quick Search History</h3>

            @if($quickQuestions->isEmpty())
                <div class="text-center py-8 text-gray-600 dark:text-gray-300">
                    No quick searches yet. Submit a search to get started.
                </div>
            @else
                <div class="overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr class="text-gray-700 dark:text-gray-200">
                                <th>Search Query</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($quickQuestions as $question)
                                <tr class="text-gray-700 dark:text-gray-200">
                                    <td class="max-w-md truncate">{{ $question->question }}</td>
                                    <td>{{ $question->created_at->format('M d, Y') }}</td>
                                    <td class="flex space-x-2">
                                        <button wire:click="viewDetails({{ $question->id }})" class="btn btn-sm btn-primary">
                                            <i class="fa fa-eye"></i>
                                        </button>
                                        <button wire:click="confirmDelete({{ $question->id }})" type="button" class="btn btn-sm btn-error">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-4">
                        {{ $quickQuestions->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Regular Research Tab Content -->
    <div class="{{ $activeTab === 'regular' ? 'block' : 'hidden' }}">
        <div class="bg-white dark:bg-neutral-800 shadow-md rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Regular Legal Research</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Submit a legal research question for quick analysis. This is ideal for straightforward legal questions that require standard research.
                <span class="font-semibold">Cost: {{ $regularResearchCredits }} credits</span>
            </p>

            @if($insufficientCredits)
            <div class="alert alert-warning mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                <div>
                    <h3 class="font-bold">Insufficient Credits</h3>
                    <div class="text-sm">You need at least {{ $regularResearchCredits }} credits to submit a regular research question. Your current balance is {{ $currentBalance }} credits.</div>
                    <a href="{{ route('credits.purchase.form') }}" class="btn btn-sm btn-outline mt-2">Purchase Credits</a>
                </div>
            </div>
            @endif

            <form wire:submit.prevent="submitRegularResearch" class="space-y-4">
                <div>
                    <label for="regularResearchQuestion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Research Question</label>
                    <textarea id="regularResearchQuestion" wire:model="regularResearchQuestion" rows="4"
                        class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                        placeholder="e.g., What are the legal precedents for self-defense in California?"></textarea>
                    @error('regularResearchQuestion') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="btn btn-primary" wire:loading.attr="disabled" wire:target="submitRegularResearch" wire:loading.class="loading"
                        {{ $insufficientCredits ? 'disabled' : '' }}
                        title="{{ $insufficientCredits ? 'You need ' . $regularResearchCredits . ' credits to submit a regular research question' : 'Submit your question for research' }}">
                        @if($insufficientCredits)
                            Need {{ $regularResearchCredits }} Credits
                        @else
                            Submit Question
                        @endif
                    </button>
                </div>
            </form>
        </div>

        <div class="mt-6 bg-white dark:bg-neutral-800 shadow-md rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Your Regular Research Questions</h3>

            @if($regularQuestions->isEmpty())
                <div class="text-center py-8 text-gray-600 dark:text-gray-300">
                    No research questions yet. Submit a question to get started.
                </div>
            @else
                <div class="overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr class="text-gray-700 dark:text-gray-200">
                                <th>Question</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($regularQuestions as $question)
                                <tr class="text-gray-700 dark:text-gray-200">
                                    <td class="max-w-md truncate">{{ $question->question }}</td>
                                    <td>
                                        @if($question->status === 'pending')
                                            <span class="badge badge-warning">Pending</span>
                                        @elseif($question->status === 'in_progress')
                                            <span class="badge badge-info">In Progress</span>
                                        @elseif($question->status === 'completed')
                                            <span class="badge badge-success">Completed</span>
                                        @elseif($question->status === 'failed')
                                            <span class="badge badge-error">Failed</span>
                                        @endif
                                    </td>
                                    <td>{{ $question->created_at->format('M d, Y') }}</td>
                                    <td class="flex space-x-2">
                                        <button wire:click="viewDetails({{ $question->id }})" class="btn btn-sm btn-primary">
                                            <i class="fa fa-eye"></i>
                                        </button>
                                        <button wire:click="confirmDelete({{ $question->id }})" type="button" class="btn btn-sm btn-error">
                                            <i class="fa fa-trash"></i>
                                        </button>
                                        <button wire:click="refreshStatus({{ $question->id }})" class="btn btn-sm btn-outline">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                            </svg>
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-4">
                        {{ $regularQuestions->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Deep Research Tab Content -->
    <div class="{{ $activeTab === 'deep' ? 'block' : 'hidden' }}">
        <div class="bg-white dark:bg-neutral-800 shadow-md rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">Deep Legal Research</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-4">
                Submit a complex legal research question for in-depth multi-agent analysis. This is ideal for complex legal questions that require comprehensive research.
                <span class="font-semibold">Cost: {{ $deepResearchCredits }} credits</span>
            </p>

            @php
                $insufficientDeepCredits = $currentBalance < $deepResearchCredits;
            @endphp

            @if($insufficientDeepCredits)
            <div class="alert alert-warning mb-4">
                <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                <div>
                    <h3 class="font-bold">Insufficient Credits</h3>
                    <div class="text-sm">You need at least {{ $deepResearchCredits }} credits to submit a deep research question. Your current balance is {{ $currentBalance }} credits.</div>
                    <a href="{{ route('credits.purchase.form') }}" class="btn btn-sm btn-outline mt-2">Purchase Credits</a>
                </div>
            </div>
            @endif

            <form wire:submit.prevent="submitDeepResearch" class="space-y-4">
                <div>
                    <label for="deepResearchQuestion" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Research Question</label>
                    <textarea id="deepResearchQuestion" wire:model="deepResearchQuestion" rows="4"
                        class="w-full rounded-md border-gray-300 dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"
                        placeholder="e.g., What are the legal implications of autonomous vehicle accidents across different jurisdictions?"></textarea>
                    @error('deepResearchQuestion') <span class="text-red-500 text-sm">{{ $message }}</span> @enderror
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="btn btn-primary" wire:loading.attr="disabled" wire:target="submitDeepResearch" wire:loading.class="loading"
                        {{ $insufficientDeepCredits ? 'disabled' : '' }}
                        title="{{ $insufficientDeepCredits ? 'You need ' . $deepResearchCredits . ' credits to submit a deep research question' : 'Submit your question for in-depth research' }}">
                        @if($insufficientDeepCredits)
                            Need {{ $deepResearchCredits }} Credits
                        @else
                            Submit Question
                        @endif
                    </button>
                </div>
            </form>
        </div>

        <div class="mt-6 bg-white dark:bg-neutral-800 shadow-md rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">Your Deep Research Questions</h3>

            @if($deepQuestions->isEmpty())
                <div class="text-center py-8 text-gray-600 dark:text-gray-300">
                    No research questions yet. Submit a question to get started.
                </div>
            @else
                <div class="overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr class="text-gray-700 dark:text-gray-200">
                                <th>Question</th>
                                <th>Status</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($deepQuestions as $question)
                                <tr class="text-gray-700 dark:text-gray-200">
                                    <td class="max-w-md truncate">{{ $question->question }}</td>
                                    <td>
                                        @if($question->status === 'pending')
                                            <span class="badge badge-warning">Pending</span>
                                        @elseif($question->status === 'in_progress')
                                            <span class="badge badge-info">In Progress</span>
                                        @elseif($question->status === 'completed')
                                            <span class="badge badge-success">Completed</span>
                                        @elseif($question->status === 'failed')
                                            <span class="badge badge-error">Failed</span>
                                        @endif
                                    </td>
                                    <td>{{ $question->created_at->format('M d, Y') }}</td>
                                    <td class="flex space-x-2">
                                        <button wire:click="viewDetails({{ $question->id }})" class="btn btn-sm btn-primary">
                                            <i class="fa fa-eye"></i>
                                        </button>
                                        <button wire:click="refreshStatus({{ $question->id }})" class="btn btn-sm btn-outline">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                            </svg>
                                        </button>
                                        <button wire:click="confirmDelete({{ $question->id }})" class="btn btn-sm btn-outline btn-error">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                            </svg>
                                        </button>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                    <div class="mt-4">
                        {{ $deepQuestions->links() }}
                    </div>
                </div>
            @endif
        </div>
    </div>
    <!-- Delete Confirmation Modal -->
    @if($showDeleteConfirmation)
        <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

                <!-- Modal panel -->
                <div class="inline-block align-bottom bg-white dark:bg-neutral-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                    <div class="bg-white dark:bg-neutral-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                                <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            </div>
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left z-[999999]">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                                    Delete Research Question
                                </h3>
                                <div class="mt-2">
                                    <p class="text-sm text-gray-500 dark:text-gray-400">
                                        Are you sure you want to delete this research question? This action cannot be undone.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-neutral-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        <button wire:click="deleteQuestion" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Delete
                        </button>
                        <button wire:click="cancelDelete" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-neutral-800 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-neutral-700">
                            Cancel
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Research Detail Modal -->
    @if($showDetailModal && $selectedQuestion)
        <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
            <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
                <!-- Background overlay -->
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

                <!-- Modal panel -->
                <div class="inline-block align-bottom bg-white dark:bg-neutral-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                    <div class="bg-white dark:bg-neutral-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                        <div class="sm:flex sm:items-start">
                            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                                    Research Details
                                </h3>
                                <div class="mt-4 space-y-4">
                                    <!-- Question Info -->
                                    <div>
                                        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">Question</h4>
                                        <p class="mt-1 text-gray-600 dark:text-gray-400">{{ $selectedQuestion->question }}</p>
                                    </div>

                                    <!-- Status Info -->
                                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">Status</h4>
                                            <p class="mt-1">
                                                @if($selectedQuestion->status === 'pending')
                                                    <span class="badge badge-warning">Pending</span>
                                                @elseif($selectedQuestion->status === 'in_progress')
                                                    <span class="badge badge-info">In Progress</span>
                                                @elseif($selectedQuestion->status === 'completed')
                                                    <span class="badge badge-success">Completed</span>
                                                @elseif($selectedQuestion->status === 'failed')
                                                    <span class="badge badge-error">Failed</span>
                                                @endif
                                            </p>
                                        </div>
                                        <div>
                                            <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">Type</h4>
                                            <p class="mt-1 capitalize">{{ $selectedQuestion->research_type }}</p>
                                        </div>
                                        <div>
                                            <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">Date Submitted</h4>
                                            <p class="mt-1">{{ $selectedQuestion->created_at->format('M d, Y g:i A') }}</p>
                                        </div>
                                    </div>

                                    <!-- Research Results -->
                                    @if($selectedQuestion->status === 'completed')
                                        <div>
                                            <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">Research Results</h4>
                                            @if($selectedQuestion->research_report_url)
                                                <div class="mt-2">
                                                    <a href="{{ $selectedQuestion->research_report_url }}" target="_blank" class="btn btn-primary">
                                                        View Full Report
                                                    </a>
                                                </div>
                                            @endif

                                            @if($selectedQuestion->research_markdown_content)
                                                <div class="mt-4 p-4 bg-gray-50 dark:bg-neutral-700 rounded-lg overflow-auto max-h-96 bg-white">
                                                    <div class="prose dark:prose-invert max-w-none bg-white">
                                                        {!! Str::markdown($selectedQuestion->research_markdown_content) !!}
                                                    </div>
                                                </div>
                                            @else
                                                <p class="mt-1 text-gray-600 dark:text-gray-400">No markdown content available.</p>
                                            @endif
                                        </div>
                                    @elseif($selectedQuestion->status === 'failed')
                                        <div>
                                            <h4 class="text-md font-medium text-red-600">Error</h4>
                                            <p class="mt-1 text-red-500">{{ $selectedQuestion->research_error }}</p>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 dark:bg-neutral-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                        @if($selectedQuestion->status === 'pending' || $selectedQuestion->status === 'in_progress')
                            <button wire:click="refreshStatus({{ $selectedQuestion->id }})" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
                                Refresh Status
                            </button>
                        @endif
                        <button wire:click="confirmDelete({{ $selectedQuestion->id }})" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                            Delete
                        </button>
                        <button wire:click="closeDetailModal" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm dark:bg-neutral-800 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-neutral-700">
                            Close
                        </button>
                    </div>
                </div>
            </div>
        </div>
    @endif
</div>
