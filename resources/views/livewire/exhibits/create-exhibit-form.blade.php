<div>
    <form wire:submit.prevent="save">
        <div class="space-y-4">
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Exhibit Label</span>
                </label>
                <input 
                    type="text" 
                    wire:model="label" 
                    class="input input-bordered w-full" 
                    placeholder="A, B, C, etc."
                />
                @error('label') <span class="text-error text-sm mt-1">{{ $message }}</span> @enderror
            </div>
            
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Description</span>
                </label>
                <input 
                    type="text" 
                    wire:model="description" 
                    class="input input-bordered w-full" 
                    placeholder="Brief description of the exhibit"
                />
                @error('description') <span class="text-error text-sm mt-1">{{ $message }}</span> @enderror
            </div>
            
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Document (Optional)</span>
                </label>
                <select wire:model="documentId" class="select select-bordered w-full">
                    <option value="">Select a document</option>
                    @foreach($availableDocuments as $document)
                        <option value="{{ $document->id }}">{{ $document->title }}</option>
                    @endforeach
                </select>
                @error('documentId') <span class="text-error text-sm mt-1">{{ $message }}</span> @enderror
            </div>
            
            <div class="form-control">
                <label class="label">
                    <span class="label-text">Date (Optional)</span>
                </label>
                <input 
                    type="date" 
                    wire:model="dated" 
                    class="input input-bordered w-full"
                />
                @error('dated') <span class="text-error text-sm mt-1">{{ $message }}</span> @enderror
            </div>
            
            <div class="flex justify-end space-x-2 mt-6">
                <button type="button" wire:click="$dispatch('closeModal')" class="btn btn-ghost">Cancel</button>
                <button type="submit" class="btn btn-primary">Save Exhibit</button>
            </div>
        </div>
    </form>
</div>
