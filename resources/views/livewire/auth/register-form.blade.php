<div>
    @if(session('debug'))
        <div class="alert alert-info mb-4">
            {{ session('debug') }}
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-error mb-4">
            {{ session('error') }}
        </div>
    @endif

    @if(session('dev_coordinates'))
        <div class="alert alert-success mb-4 p-4 border-2 border-green-500 bg-green-100 text-green-800 font-bold">
            🌍 COORDINATES CAPTURED: {{ session('dev_coordinates') }}
        </div>
    @endif

    <form wire:submit="register" class="space-y-6">
        <div class="space-y-4">
            <div>
                <x-label for="name" value="{{ __('Name') }}" />
                <x-input id="name" class="block mt-1 w-full" type="text" wire:model="name" required autofocus autocomplete="name" />
                <x-input-error for="name" class="mt-2" />
            </div>

            <div>
                <x-label for="username" value="{{ __('Username') }}" />
                <x-input
                    id="username"
                    class="block mt-1 w-full"
                    wire:model.live="username"
                    required
                    autocomplete="new-password"
                />
                <x-input-error for="username" class="mt-2" />
                @error('username') <div class="text-error mt-1">{{ $message }}</div> @enderror
                <p class="mt-1 text-sm text-gray-500">
                    {{ __('Username must be unique, 3-25 characters, and can only contain letters, numbers, dashes, and underscores.') }}
                </p>
            </div>

            <div>
                <x-label for="email" value="{{ __('Email') }}" />
                <x-input id="email" class="block mt-1 w-full" type="email" wire:model="email" required autocomplete="email" />
                <x-input-error for="email" class="mt-2" />
            </div>

            <div>
                <x-label for="password" value="{{ __('Password') }}" />
                <x-input id="password" class="block mt-1 w-full" type="password" wire:model="password" required autocomplete="new-password" />
                <x-input-error for="password" class="mt-2" />
            </div>

            <div>
                <x-label for="password_confirmation" value="{{ __('Confirm Password') }}" />
                <x-input id="password_confirmation" class="block mt-1 w-full" type="password" wire:model="password_confirmation" required autocomplete="new-password" />
                <x-input-error for="password_confirmation" class="mt-2" />
            </div>

            <div class="mt-4">
                <label class="flex items-center">
                    <x-checkbox wire:model.live="is_attorney" />
                    <span class="ml-2 text-sm text-gray-600">{{ __('I am an attorney') }}</span>
                </label>
            </div>

            @if($is_attorney)
            <div class="mt-4">
                <x-label for="bar_card_number" value="{{ __('Bar Card Number') }}" />
                <x-input id="bar_card_number" class="block mt-1 w-full" type="text" wire:model="bar_card_number" required />
                <x-input-error for="bar_card_number" class="mt-2" />
                <p class="mt-1 text-sm text-gray-500">
                    {{ __('Please enter your bar card number or attorney license number.') }}
                </p>
            </div>
            @endif

            <div class="mt-4">
                <x-label value="{{ __('ZIP Code') }}" />
                <div x-data="{
                    retryCount: 0,
                    maxRetries: 20,
                    retryInterval: 200,
                    initGooglePlaces() {
                        if (typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.places === 'undefined') {
                            this.retryCount++;
                            if (this.retryCount <= this.maxRetries) {
                                console.log(`Waiting for Google Maps API to load (attempt ${this.retryCount}/${this.maxRetries})`);
                                setTimeout(() => this.initGooglePlaces(), this.retryInterval);
                            } else {
                                console.error('Google Maps API failed to load after maximum retries');
                            }
                            return;
                        }

                        console.log('Google Maps Places API loaded successfully');
                        const input = document.getElementById('zip_code');
                        const options = {
                            componentRestrictions: { country: 'us' },
                            fields: ['address_components', 'geometry'],
                            types: ['postal_code']
                        };

                        const autocomplete = new google.maps.places.Autocomplete(input, options);

                    autocomplete.addListener('place_changed', () => {
                        const place = autocomplete.getPlace();

                        if (!place.geometry || !place.geometry.location) {
                            return;
                        }

                        // Extract ZIP code from place
                        let zipCode = '';
                        for (const component of place.address_components) {
                            if (component.types.includes('postal_code')) {
                                zipCode = component.short_name;
                                break;
                            }
                        }

                        // Set ZIP code and coordinates
                        if (zipCode) {
                            @this.set('zip_code', zipCode);
                            @this.call('setCoordinates',
                                place.geometry.location.lat(),
                                place.geometry.location.lng()
                            );
                        }
                    });
                } }" x-init="initGooglePlaces()">
                    <x-input id="zip_code" type="text" class="block mt-1 w-full" wire:model="zip_code" required autocomplete="new-password" />
                </div>
                <x-input-error for="zip_code" class="mt-2" />


                @if(app()->environment('local', 'development', 'testing'))
                    <!-- Debug information -->
                    <div class="mt-2 text-sm">
                        <p class="font-semibold">Current values:</p>
                        <p>ZIP: <span class="text-blue-600">{{ $zip_code }}</span></p>
                        <p>Lat: <span class="text-green-600">{{ $latitude ?? 'Not set' }}</span></p>
                        <p>Lng: <span class="text-green-600">{{ $longitude ?? 'Not set' }}</span></p>
                    </div>
                    <!-- End debug information -->
                @endif
            </div>

            @if (Laravel\Jetstream\Jetstream::hasTermsAndPrivacyPolicyFeature())
                <div>
                    <x-label for="terms">
                        <div class="flex items-start">
                            <x-checkbox wire:model="terms" id="terms" required class="mt-1" />

                            <div class="ms-2 text-sm">
                                {!! __('I agree to the :terms_of_service and :privacy_policy', [
                                        'terms_of_service' => '<a target="_blank" href="'.route('terms.show').'" class="underline text-base-content/60 dark:text-base-content/60 hover:text-base-content dark:hover:text-base-content/90 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800">'.__('Terms of Service').'</a>',
                                        'privacy_policy' => '<a target="_blank" href="'.route('policy.show').'" class="underline text-base-content/60 dark:text-base-content/60 hover:text-base-content dark:hover:text-base-content/90 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800">'.__('Privacy Policy').'</a>',
                                ]) !!}
                            </div>
                        </div>
                        <x-input-error for="terms" class="mt-2" />
                    </x-label>
                </div>
            @endif
        </div>

        <div class="space-y-4 mt-4">
            <a class="block w-full text-center underline text-sm text-base-content/60 dark:text-base-content/60 hover:text-base-content dark:hover:text-base-content/90 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800" href="{{ route('login') }}">
                {{ __('Already registered?') }}
            </a>

            <x-button class="w-full lg:w-auto" wire:loading.attr="disabled">
                <span wire:loading.remove wire:target="register">{{ __('Register') }}</span>
                <span wire:loading wire:target="register">{{ __('Processing...') }}</span>
            </x-button>
        </div>
    </form>
</div>
