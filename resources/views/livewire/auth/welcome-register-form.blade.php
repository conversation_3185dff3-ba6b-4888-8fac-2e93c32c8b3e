<div x-data="{
    isAttorney: @entangle('is_attorney'),
    termsAccepted: @entangle('terms')
}">
    <div class="mb-8 text-center">
        <img src="https://justicequest.us-mia-1.linodeobjects.com/public/images/en/JQ.png" class="h-20 mx-auto mb-4 animate-pulse-subtle" alt="Justice Quest Logo">
        <h2 class="text-2xl font-bold text-white">Create Your Account</h2>
    </div>

    <form wire:submit="register" class="space-y-4">
        <div>
            <label for="name" class="block text-sm font-medium text-white">Name</label>
            <input id="name" wire:model="name" type="text" required class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-red-500/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
            @error('name')
                <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
            @enderror
        </div>

        <div>
            <label for="username" class="block text-sm font-medium text-white">Username</label>
            <input id="username" wire:model="username" type="text" required class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-red-500/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
            @error('username')
                <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
            @enderror
            <p class="mt-1 text-xs text-gray-400">
                Username must be unique, 3-25 characters, and can only contain letters, numbers, dashes, and underscores.
            </p>
        </div>

        <div>
            <label for="register-email" class="block text-sm font-medium text-white">Email</label>
            <input id="register-email" wire:model="email" type="email" required class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-red-500/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
            @error('email')
                <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
            @enderror
        </div>

        <div>
            <label for="register-password" class="block text-sm font-medium text-white">Password</label>
            <input id="register-password" wire:model="password" type="password" required class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-red-500/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
            @error('password')
                <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
            @enderror
        </div>

        <div>
            <label for="password_confirmation" class="block text-sm font-medium text-white">Confirm Password</label>
            <input id="password_confirmation" wire:model="password_confirmation" type="password" required class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-red-500/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
        </div>

        <div class="mt-4">
            <label class="flex items-center">
                <input wire:model="is_attorney" type="checkbox" class="w-4 h-4 text-red-600 bg-white border-gray-400 rounded focus:ring-red-500">
                <span class="ml-2 text-sm text-gray-300">I am an attorney</span>
            </label>
        </div>

        <div x-show="isAttorney">
            <label for="bar_card_number" class="block text-sm font-medium text-white">Bar Card Number</label>
            <input id="bar_card_number" wire:model="bar_card_number" type="text" :required="isAttorney" class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-red-500/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
            @error('bar_card_number')
                <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
            @enderror
            <p class="mt-1 text-xs text-gray-400">
                Please enter your bar card number or attorney license number.
            </p>
        </div>

        <div>
            <label for="zip_code" class="block text-sm font-medium text-white">ZIP Code</label>
            <div x-data="{
                retryCount: 0,
                maxRetries: 20,
                retryInterval: 200,
                initGooglePlaces() {
                    if (typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.places === 'undefined') {
                        this.retryCount++;
                        if (this.retryCount <= this.maxRetries) {
                            console.log(`Waiting for Google Maps API to load (attempt ${this.retryCount}/${this.maxRetries})`);
                            setTimeout(() => this.initGooglePlaces(), this.retryInterval);
                        } else {
                            console.error('Google Maps API failed to load after maximum retries');
                        }
                        return;
                    }

                    console.log('Google Maps Places API loaded successfully');
                    const input = document.getElementById('zip_code');
                    const options = {
                        componentRestrictions: { country: 'us' },
                        fields: ['address_components', 'geometry'],
                        types: ['postal_code']
                    };

                    const autocomplete = new google.maps.places.Autocomplete(input, options);

                    autocomplete.addListener('place_changed', () => {
                        const place = autocomplete.getPlace();

                        if (!place.geometry || !place.geometry.location) {
                            return;
                        }

                        // Extract ZIP code from place
                        let zipCode = '';
                        for (const component of place.address_components) {
                            if (component.types.includes('postal_code')) {
                                zipCode = component.short_name;
                                break;
                            }
                        }

                        // Set coordinates via Livewire
                        if (zipCode) {
                            $wire.setCoordinates(
                                place.geometry.location.lat(),
                                place.geometry.location.lng()
                            );
                        }
                    });
                }
            }" x-init="$nextTick(() => initGooglePlaces())">
                <input id="zip_code" wire:model="zip_code" type="text" required class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-red-500/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent">
            </div>
            @error('zip_code')
                <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
            @enderror
        </div>

        <div class="flex items-center">
            <input wire:model="terms" id="terms" type="checkbox" required class="w-4 h-4 text-red-600 bg-white border-gray-400 rounded focus:ring-red-500">
            <label for="terms" class="block ml-2 text-sm text-gray-300">
                I agree to the <a href="/terms" class="text-red-400 hover:text-red-300" target="_blank">Terms of Service and Privacy Policy</a>
            </label>
            @error('terms')
                <span class="block mt-1 text-xs text-red-500">{{ $message }}</span>
            @enderror
        </div>

        <div>
            <button type="submit" :disabled="!termsAccepted" class="flex justify-center w-full px-4 py-2 text-sm font-medium text-white transition-colors duration-300 bg-red-600 border border-transparent rounded-md shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed" wire:loading.attr="disabled">
                <span wire:loading.remove>Register</span>
                <span wire:loading>Processing...</span>
            </button>
        </div>
    </form>

    <div class="mt-6 text-center">
        <p class="text-sm text-gray-400">Already have an account?</p>
        <button @click="showRegisterModal = false; showLoginModal = true" class="mt-2 text-sm text-red-400 hover:text-red-300">
            Sign in
        </button>
    </div>
</div>
