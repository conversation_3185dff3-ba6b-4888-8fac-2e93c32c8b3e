<div>
    <!-- Fullscreen Chat Interface -->
    <div x-data="{
        showThreadSelector: false,
        showScrollButton: false,
        scrollToBottom() {
            const chatMessages = document.getElementById('chat-messages');
            if (chatMessages) {
                // Find the last message element
                const lastMessage = chatMessages.querySelector('.chat:last-child, .mb-4:last-child');

                if (lastMessage) {
                    // Scroll to the last message with smooth behavior
                    lastMessage.scrollIntoView({ behavior: 'smooth', block: 'start' });
                } else {
                    // Fallback to scrolling to bottom if no message is found
                    chatMessages.scrollTo({ top: chatMessages.scrollHeight, behavior: 'smooth' });
                }

                // Hide the scroll button after scrolling
                this.showScrollButton = false;
            }
        },
        scrollToVeryBottom() {
            setTimeout(function() {
                const chatMessages = document.getElementById('chat-messages');
                if (chatMessages) {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                    this.showScrollButton = false;
                }
            }, 1000)
        }
    }" x-show="$wire.isChatOpen"
        x-on:open-chat-interface.window="$wire.open(); $nextTick(() => scrollToBottom())"
        x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0"
        x-transition:enter-end="opacity-100" x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0"
        class="fixed inset-0 z-[99] flex flex-col overflow-hidden bg-base-100 dark:bg-neutral-focus h-screen w-full"
        x-cloak>
        <!-- Chat Header -->
        <div
            class="flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 border-b border-base-content/10 dark:border-base-content/70 gap-3">
            <div class="flex items-center w-full sm:w-auto">
                <div class="flex items-center justify-center w-10 h-10 mr-3 rounded-full bg-primary/20 flex-shrink-0">
                    <span class="text-lg">🤖</span>
                </div>
                <div class="overflow-hidden">
                    <div class="flex flex-wrap items-center gap-2">
                        @if ($interviewMode)
                            <h2 class="text-lg font-semibold">{{ __('chat.interview_assistant') }}</h2>
                            @if ($caseFile)
                                <span
                                    class="px-2 py-1 text-xs rounded bg-primary/20 text-primary whitespace-nowrap">{{ __('chat.interview') }}</span>
                                <span
                                    class="text-sm font-medium truncate max-w-[150px] sm:max-w-[200px]">{{ $caseFile->title }}</span>
                            @endif
                        @elseif ($strategyMode)
                            <h2 class="text-lg font-semibold">{{ __('strategy.title') }}</h2>
                            @if ($caseFile)
                                <span
                                    class="px-2 py-1 text-xs rounded bg-primary/20 text-primary whitespace-nowrap">{{ __('chat.case') }}</span>
                                <span
                                    class="text-sm font-medium truncate max-w-[150px] sm:max-w-[200px]">{{ $caseFile->title }}</span>
                            @endif
                        @else
                            <h2 class="text-lg font-semibold">{{ __('chat.ai_assistant') }}</h2>
                            @if ($caseFile)
                                <span
                                    class="px-2 py-1 text-xs rounded bg-primary/20 text-primary whitespace-nowrap">{{ __('chat.case') }}</span>
                                <span
                                    class="text-sm font-medium truncate max-w-[150px] sm:max-w-[200px]">{{ $caseFile->title }}</span>
                            @endif
                        @endif
                        @if (!$interviewMode && !$strategyMode)
                            <button @click="showThreadSelector = !showThreadSelector" class="btn btn-xs btn-ghost">
                                <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 9l-7 7-7-7" />
                                </svg>
                            </button>
                        @endif
                    </div>
                    <p class="text-sm text-base-content/60 truncate max-w-full">
                        @if ($interviewMode)
                            {{ __('chat.fact_gathering_interview') }}
                        @elseif ($strategyMode)
                            {{ __('strategy.conversation.subtitle') }}
                        @else
                            {{ $currentThread ? $currentThread->title : __('chat.select_conversation_thread') }}
                        @endif
                    </p>
                </div>
            </div>
            <div class="flex items-center gap-2 self-end sm:self-auto w-full sm:w-auto justify-end">
                @if (!$interviewMode && !$strategyMode)
                    <button wire:click="createNewThread" class="btn btn-sm btn-primary"
                        title="{{ __('chat.new_conversation') }}">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        <span class="hidden sm:inline-block ml-1">New Chat</span>
                    </button>
                @endif
                <a wire:click="close" class="btn btn-ghost btn-circle">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" fill="none" viewBox="0 0 24 24"
                        stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </a>
            </div>
        </div>

        <!-- Thread Selector Dropdown -->
        <div x-show="showThreadSelector && !$wire.interviewMode && !$wire.strategyMode" x-transition
            class="border-b border-base-content/10 dark:border-base-content/70 bg-base-200">
            <div class="p-2 overflow-y-auto max-h-64">
                @if (count($threads) > 0)
                    <div class="flex flex-col gap-1">
                        @foreach ($threads as $thread)
                            <div class="flex items-center justify-between">
                                <button wire:click="selectThread({{ $thread->id }})"
                                    @click="showThreadSelector = false"
                                    class="btn btn-ghost justify-start text-left flex-grow {{ $currentThread && $currentThread->id === $thread->id ? 'btn-active' : '' }}">
                                    <div>
                                        <div class="font-medium">{{ $thread->title }}</div>
                                        <div class="flex items-center gap-2">
                                            @if ($thread->type)
                                                <span
                                                    class="badge badge-sm">{{ \App\Models\AssistantThread::getTypes()[$thread->type] ?? $thread->type }}</span>
                                            @endif
                                            <div class="text-xs opacity-70">
                                                {{ $thread->last_activity_at->diffForHumans() }}</div>
                                        </div>
                                    </div>
                                </button>
                                <button wire:click="confirmDeleteThread({{ $thread->id }})"
                                    class="btn btn-ghost btn-sm text-error" title="{{ __('chat.delete_thread') }}">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none"
                                        viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                    </svg>
                                </button>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="p-4 text-center text-base-content/60">
                        {{ __('chat.no_conversation_threads') }}
                    </div>
                @endif
            </div>
        </div>

        <div class="flex flex-col flex-1 overflow-hidden w-full max-w-4xl mx-auto">
            <!-- Chat Messages Area -->
            <div class="flex-1 p-4 space-y-4 overflow-y-auto h-full w-full relative" id="chat-messages"
                x-init="scrollToBottom();
                $el.addEventListener('scroll', () => {
                    // Show button if not at bottom (with a small threshold)
                    const isAtBottom = $el.scrollHeight - $el.scrollTop - $el.clientHeight < 100;
                    if (!isAtBottom && $wire.chatMessages && $wire.chatMessages.length > 0) {
                        showScrollButton = true;
                    }
                });" x-intersect:enter="scrollToBottom()" @messages-updated.window="scrollToBottom()"
                @new-assistant-message.window="showScrollButton = true; scrollToBottom()"
                x-effect="if($wire.chatMessages && $wire.chatMessages.length) { $nextTick(() => scrollToBottom()); }">
                @if ($currentThread)
                    @foreach ($chatMessages as $message)
                        @if ($message->role === 'user')
                            <div class="chat chat-end">
                                <div class="chat-header">
                                    {{ __('chat.you') }}
                                    <time class="text-xs opacity-50">{{ $message->created_at->format('H:i') }}</time>
                                </div>
                                <div class="chat-bubble chat-bubble-secondary">
                                    {{ $message->content }}
                                </div>
                            </div>
                        @else
                            <div class="mb-4"
                                @if ($loop->last) style="background: lightgrey !important;padding: 20px;border-radius: 10px;" @endif>
                                <div class="flex items-center mb-1">
                                    <span class="font-medium">{{ __('chat.ai_assistant') }}</span>
                                    <time
                                        class="ml-2 text-xs opacity-50">{{ $message->created_at->format('H:i') }}</time>
                                </div>
                                <div class="prose prose-base max-w-none dark:prose-invert">
                                    {!! Str::markdown($message->content) !!}
                                </div>
                            </div>
                        @endif
                    @endforeach

                    <!-- Loading Animation for Assistant Response -->
                    <div x-show="$wire.isWaitingForResponse" x-init="$watch('$wire.isWaitingForResponse', value => { if (value) scrollToBottom() })" class="mb-4 animate-pulse">
                        <div class="flex items-center mb-1">
                            <span class="font-medium">{{ __('chat.ai_assistant') }}</span>
                            <span class="ml-2 text-xs opacity-50">{{ __('chat.thinking') }}</span>
                        </div>
                        <div class="p-4 rounded-lg bg-base-200">
                            <div class="flex space-x-2">
                                <div class="w-3 h-3 rounded-full bg-primary"></div>
                                <div class="w-3 h-3 rounded-full bg-primary animation-delay-200"></div>
                                <div class="w-3 h-3 rounded-full bg-primary animation-delay-400"></div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="flex items-center justify-center h-full">
                        <div class="max-w-md p-6 text-center rounded-lg bg-base-200">
                            <h3 class="mb-2 text-lg font-semibold">{{ __('chat.start_new_conversation') }}</h3>
                            <p class="mb-4">{{ __('chat.create_new_thread_prompt') }}</p>
                            <button wire:click="createNewThread"
                                class="btn btn-primary">{{ __('chat.new_conversation') }}</button>
                        </div>
                    </div>
                @endif

                <!-- Floating Scroll to Bottom Button -->
                <div x-show="showScrollButton" x-transition class="tooltip tooltip-left"
                    data-tip="Scroll to latest message">
                    <button @click="scrollToBottom()"
                        class="scroll-to-bottom-btn fixed bottom-44 right-6 bg-primary hover:bg-primary-focus text-white rounded-full p-3 shadow-lg transition-all duration-300 transform hover:scale-110 focus:outline-none">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M19 14l-7 7m0 0l-7-7m7 7V3" />
                        </svg>
                    </button>
                </div>
            </div>
            <div class="mb-4">
                <div class="flex justify-end">
                    @if ($interviewMode)
                        <button wire:click="restartInterview()" class="btn btn-sm btn-error"
                            title="Restart Interview">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                            </svg>
                            Restart Interview
                        </button>
                    @endif

                    @if ($strategyMode)
                        <button wire:click="saveAsStrategy()" wire:loading.attr="disabled" wire:target="saveAsStrategy" class="btn btn-sm btn-primary relative"
                            title="{{ __('strategy.conversation.save_strategy') }}">
                            <div wire:loading.remove wire:target="saveAsStrategy" class="flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none"
                                    viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                                </svg>
                                {{ __('strategy.conversation.save_strategy') }}
                            </div>
                            <div wire:loading wire:target="saveAsStrategy" class="flex items-center">
                                <svg class="animate-spin h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                {{ __('strategy.conversation.processing') }}
                            </div>
                        </button>
                    @endif
                </div>
            </div>
            <!-- Chat Input Area -->
            @include('partials.chat-interface-input')
        </div>

        <style>
            /* Fixed height for textarea */
            textarea.textarea {
                height: 80px !important;
                max-height: 80px !important;
                resize: none !important;
            }

            /* Ensure proper layout */
            .fixed.inset-0.z-\[9999\].flex.flex-col {
                height: 100vh;
                max-height: 100vh;
                width: 100% !important;
                max-width: 100% !important;
                overflow: hidden;
                z-index: 9999 !important;
            }

            #chat-messages {
                flex: 1;
                overflow-y: auto;
                padding-bottom: 20px;
                width: 100% !important;
                max-width: 100% !important;
                position: relative;
            }

            /* Floating scroll to bottom button styles */
            .scroll-to-bottom-btn {
                z-index: 90000;
                box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
                animation: pulse-light 2s infinite;
            }

            @keyframes pulse-light {
                0% {
                    box-shadow: 0 0 0 0 rgba(var(--p), 0.4);
                }

                70% {
                    box-shadow: 0 0 0 10px rgba(var(--p), 0);
                }

                100% {
                    box-shadow: 0 0 0 0 rgba(var(--p), 0);
                }
            }

            /* Mobile responsiveness fixes */
            @media (max-width: 640px) {

                /* Ensure the chat interface has proper overflow handling */
                .fixed.inset-0.z-\[9999\].flex.flex-col {
                    flex-direction: column;
                    height: 100vh !important;
                    width: 100% !important;
                    max-width: 100% !important;
                    /* Remove the display: flex !important that was causing it to show by default */
                }

                /* Make sure the chat messages area takes appropriate space */
                #chat-messages {
                    flex: 1;
                    overflow-y: auto;
                }

                /* Adjust scroll to bottom button position on mobile */
                .scroll-to-bottom-btn {
                    bottom: 80px;
                    right: 16px;
                }

                /* Ensure the input area doesn't overflow */
                .p-4.border-t {
                    padding-bottom: env(safe-area-inset-bottom, 1rem);
                    flex-shrink: 0;
                }
            }
        </style>
    </div>

    <!-- New Thread Modal -->
    <div x-data="{ open: @entangle('showNewThreadModal') }">
        <div x-show="open" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            class="fixed inset-0 z-[10000] flex items-center justify-center bg-black bg-opacity-50"
            style="display: none;">
            <div class="w-full max-w-md mx-4 rounded-lg shadow-xl bg-base-100">
                <div class="p-4 border-b border-base-content/10">
                    <h3 class="text-lg font-semibold">{{ __('chat.new_conversation') }}</h3>
                </div>
                <div class="p-4">
                    <div class="mb-4">
                        <label class="block mb-1 text-sm font-medium">{{ __('chat.title') }}</label>
                        <input type="text" wire:model.defer="newThreadTitle" class="w-full input input-bordered"
                            placeholder="{{ __('chat.title_placeholder') }}">
                        @error('newThreadTitle')
                            <span class="text-sm text-error">{{ $message }}</span>
                        @enderror
                    </div>
                    <div class="mb-4">
                        <label class="block mb-1 text-sm font-medium">{{ __('chat.description_optional') }}</label>
                        <textarea wire:model.defer="newThreadDescription" class="w-full textarea textarea-bordered" rows="2"
                            placeholder="{{ __('chat.description_placeholder') }}"></textarea>
                    </div>
                    <div class="flex justify-end gap-2">
                        <button wire:click="cancelNewThread" class="btn btn-ghost">{{ __('chat.cancel') }}</button>
                        <button wire:click="saveNewThread" class="btn btn-primary">{{ __('chat.create') }}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Thread Confirmation Modal -->
    <div x-data="{ open: @entangle('showDeleteThreadModal') }">
        <div x-show="open" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100"
            x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100"
            x-transition:leave-end="opacity-0"
            class="fixed inset-0 z-[10000] flex items-center justify-center bg-black bg-opacity-50"
            style="display: none;">
            <div class="w-full max-w-md mx-4 rounded-lg shadow-xl bg-base-100">
                <div class="p-4 border-b border-base-content/10">
                    <h3 class="text-lg font-semibold">{{ __('chat.delete_conversation') }}</h3>
                </div>
                <div class="p-4">
                    <p class="mb-4">{{ __('chat.delete_confirmation') }}</p>
                    <div class="flex justify-end gap-2">
                        <button @click="open = false" class="btn btn-ghost">{{ __('chat.cancel') }}</button>
                        <button wire:click="deleteThread" class="btn btn-error">{{ __('chat.delete') }}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
