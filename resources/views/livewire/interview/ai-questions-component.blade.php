<div>
    @if(count($questions) === 0)
        <div class="p-6 bg-base-200 rounded-lg">
            <p class="mb-4">{{ __('interview.ai_questions.no_questions_yet') }}</p>

            <button
                wire:click="generateQuestions"
                class="btn btn-primary"
                wire:loading.attr="disabled"
                wire:target="generateQuestions"
            >
                <span wire:loading.remove wire:target="generateQuestions">
                    {{ __('interview.ai_questions.generate_questions') }}
                </span>
                <span wire:loading wire:target="generateQuestions">
                    {{ __('interview.ai_questions.generating') }}
                    <span class="ml-2 loading loading-spinner loading-sm"></span>
                </span>
            </button>
        </div>
    @else
        <div class="space-y-6">
            <!-- Question navigation -->
            <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-base-content/70">
                    {{ __('interview.ai_questions.question_count', ['current' => $currentQuestionIndex + 1, 'total' => count($questions)]) }}
                </span>

                <div class="flex space-x-1">
                    @foreach($questions as $index => $q)
                        <button
                            wire:click="$set('currentQuestionIndex', {{ $index }})"
                            class="w-3 h-3 rounded-full {{ $index === $currentQuestionIndex ? 'bg-primary' : ($q['is_answered'] ? 'bg-success' : 'bg-base-300') }}"
                            title="{{ $q['is_answered'] ? 'Answered' : 'Not answered' }}"
                        ></button>
                    @endforeach
                </div>
            </div>

            <!-- Current question -->
            @if(isset($questions[$currentQuestionIndex]))
                @php $question = $questions[$currentQuestionIndex]; @endphp

                <div class="p-6 bg-base-200 rounded-lg">
                    <h4 class="mb-4 text-lg font-medium">{{ $question['question_text'] }}</h4>

                    <!-- Input field based on expected_response_type -->
                    <div class="mt-4">
                        @switch($question['expected_response_type'])
                            @case('text')
                                <textarea
                                    wire:model="answers.{{ $question['id'] }}.text"
                                    class="w-full textarea textarea-bordered"
                                    rows="4"
                                    placeholder="{{ __('interview.ai_questions.text_placeholder') }}"
                                ></textarea>
                                @break

                            @case('voice')
                                <livewire:voice-message-input
                                    name="question_{{ $question['id'] }}"
                                    :value="$answers[$question['id']]['text']"
                                    wire:model="answers.{{ $question['id'] }}.text"
                                    height="150px"
                                    :placeholder="__('interview.ai_questions.voice_placeholder')"
                                />
                                @break

                            @case('document')
                                <div>
                                    @if($answers[$question['id']]['document_id'])
                                        @php $document = \App\Models\Document::find($answers[$question['id']]['document_id']); @endphp
                                        @if($document)
                                            <div class="p-3 mb-2 border rounded-lg border-base-300">
                                                <div class="flex items-center justify-between">
                                                    <div>
                                                        <p class="font-medium">{{ $document->original_filename }}</p>
                                                        <p class="text-sm text-base-content/70">{{ $document->file_size_formatted }}</p>
                                                    </div>
                                                    <button
                                                        wire:click="$set('answers.{{ $question['id'] }}.document_id', null)"
                                                        class="btn btn-sm btn-ghost"
                                                    >
                                                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>
                                        @endif
                                    @else
                                        <div x-data="{ showUploader: false }">
                                            <button
                                                @click="showUploader = true"
                                                class="btn btn-outline"
                                            >
                                                {{ __('interview.ai_questions.upload_document') }}
                                            </button>

                                            <div x-show="showUploader" class="mt-4">
                                                <livewire:exhibit-uploader
                                                    :case-file="$caseFile"
                                                    :show-document-list="false"
                                                />

                                                <button
                                                    @click="showUploader = false"
                                                    class="mt-2 btn btn-sm btn-ghost"
                                                >
                                                    {{ __('interview.ai_questions.cancel_upload') }}
                                                </button>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                                @break

                            @case('date')
                                <input
                                    type="date"
                                    wire:model="answers.{{ $question['id'] }}.text"
                                    class="w-full input input-bordered"
                                />
                                @break

                            @case('multiple_choice')
                                @if(is_array($question['multiple_choice_options']))
                                    <div class="space-y-2">
                                        @foreach($question['multiple_choice_options'] as $option)
                                            <label class="flex items-center space-x-2">
                                                <input
                                                    type="radio"
                                                    wire:model="answers.{{ $question['id'] }}.text"
                                                    value="{{ $option }}"
                                                    class="radio"
                                                />
                                                <span>{{ $option }}</span>
                                            </label>
                                        @endforeach
                                    </div>
                                @endif
                                @break

                            @default
                                <textarea
                                    wire:model="answers.{{ $question['id'] }}.text"
                                    class="w-full textarea textarea-bordered"
                                    rows="4"
                                    placeholder="{{ __('interview.ai_questions.default_placeholder') }}"
                                ></textarea>
                        @endswitch
                    </div>

                    <!-- Save button -->
                    <div class="flex justify-between mt-6">
                        <button
                            wire:click="previousQuestion"
                            class="btn btn-ghost"
                            {{ $currentQuestionIndex === 0 ? 'disabled' : '' }}
                        >
                            {{ __('interview.navigation.previous') }}
                        </button>

                        <div class="space-x-2">
                            <button
                                wire:click="saveAnswer({{ $question['id'] }})"
                                class="btn btn-primary"
                                wire:loading.attr="disabled"
                                wire:target="saveAnswer"
                            >
                                <span wire:loading.remove wire:target="saveAnswer">
                                    {{ __('interview.ai_questions.save_answer') }}
                                </span>
                                <span wire:loading wire:target="saveAnswer">
                                    {{ __('interview.ai_questions.saving') }}
                                    <span class="ml-2 loading loading-spinner loading-sm"></span>
                                </span>
                            </button>

                            <button
                                wire:click="nextQuestion"
                                class="btn btn-ghost"
                                {{ $currentQuestionIndex === count($questions) - 1 ? 'disabled' : '' }}
                            >
                                {{ __('interview.navigation.next') }}
                            </button>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Complete interview button -->
            <div class="pt-4 mt-8 border-t border-base-300">
                <button
                    wire:click="completeInterview"
                    class="w-full btn btn-success"
                    wire:loading.attr="disabled"
                    wire:target="completeInterview"
                >
                    <span wire:loading.remove wire:target="completeInterview">
                        {{ __('interview.ai_questions.complete_interview') }}
                    </span>
                    <span wire:loading wire:target="completeInterview">
                        {{ __('interview.ai_questions.completing') }}
                        <span class="ml-2 loading loading-spinner loading-sm"></span>
                    </span>
                </button>

                <div class="mt-2 text-sm text-center text-base-content/70">
                    {{ __('interview.ai_questions.complete_help') }}
                </div>
            </div>
        </div>
    @endif
</div>
