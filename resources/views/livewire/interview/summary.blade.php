<div
    x-data="{ show: false }"
    x-init="setTimeout(() => { show = true }, 100)"
    x-show="show"
    x-transition:enter="transition ease-out duration-700"
    x-transition:enter-start="opacity-0 transform -translate-y-4"
    x-transition:enter-end="opacity-100 transform translate-y-0"
    class="space-y-6"
>
    <h3 class="text-xl font-semibold text-gray-900">{{ __('interview.summary.title') }}</h3>

    <div class="p-6 bg-green-50 border border-green-200 rounded-lg">
        <div class="flex flex-col items-center text-center mb-6">
            <svg class="w-16 h-16 text-green-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>

            <h4 class="text-lg font-medium text-gray-900 mb-2">{{ __('interview.summary.subtitle') }}</h4>

            <p class="text-gray-700 mb-4">
                {{ __('interview.summary.description') }}
            </p>
        </div>

        <div class="bg-white p-6 rounded-lg border border-green-100 w-full mb-6">
            <h5 class="font-medium text-gray-800 mb-4 text-xl">{{ __('interview.summary.analysis_title') }}</h5>

            @if($caseFile->summaries->count() > 0)
                <div class="prose max-w-none">
                    {!! Str::markdown($caseFile->summaries->first()->content) !!}
                </div>
            @else
                <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4">
                    <p class="text-yellow-700">
                        {{ __('interview.summary.generating') }}
                    </p>
                </div>
            @endif
        </div>

        <div class="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-6">
            <h5 class="font-medium text-gray-800 mb-2">{{ __('interview.summary.next_steps_title') }}</h5>
            <ul class="list-disc pl-5 space-y-2 text-gray-700 text-left">
                <li>{{ __('interview.summary.next_steps.review') }}</li>
                <li>{{ __('interview.summary.next_steps.upload') }}</li>
                <li>{{ __('interview.summary.next_steps.consult') }}</li>
                <li>{{ __('interview.summary.next_steps.consider') }}</li>
            </ul>
        </div>

        <div class="flex justify-between">
            <x-button wire:click="previousStep" class="bg-gray-200 text-gray-800 hover:bg-gray-300">
                {{ __('interview.summary.back') }}
            </x-button>

            <a href="{{ route('case-files.show', $caseFile) }}" class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 active:bg-blue-900 focus:outline-none focus:border-blue-900 focus:ring ring-blue-300 disabled:opacity-25 transition ease-in-out duration-150">
                {{ __('interview.summary.return') }}
            </a>
        </div>
    </div>
</div>
