
    <div
        x-data="{ show: false }"
        x-init="setTimeout(() => { show = true }, 100)"
        x-show="show"
        x-transition:enter="transition ease-out duration-700"
        x-transition:enter-start="opacity-0 transform -translate-y-4"
        x-transition:enter-end="opacity-100 transform translate-y-0"
        class="space-y-6"
    >
        <h3 class="text-xl font-semibold text-gray-900">{{ __('interview.overview.ready_for_fact_gathering') }}</h3>

        <div class="p-6 bg-blue-50 border border-blue-200 rounded-lg">
            <div class="flex flex-col items-center text-center mb-6">
                <svg class="w-16 h-16 text-blue-500 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>

                <h4 class="text-lg font-medium text-gray-900 mb-2">{{ __('interview.overview.prepare_title') }}</h4>

                <p class="text-gray-700 mb-4">
                    {{ __('interview.overview.prepare_description') }}
                </p>

                <div class="bg-white p-4 rounded-lg border border-blue-100 w-full mb-4">
                    <h5 class="font-medium text-gray-800 mb-2">{{ __('interview.overview.important_info_title') }}</h5>
                    <ul class="list-disc pl-5 space-y-2 text-gray-700 text-left">
                        <li>{{ __('interview.overview.important_info_save_progress') }}</li>
                        <li><strong>{{ __('interview.overview.important_info_upload_documents') }}</strong></li>
                        <li>{{ __('interview.overview.important_info_specific_questions') }}</li>
                        <li>{{ __('interview.overview.important_info_comprehensive') }}</li>
                    </ul>
                </div>

                <p class="text-gray-700 mb-6">
                    {{ __('interview.overview.ready_prompt') }}
                </p>
            </div>

            <!-- Temporary button to show the generated prompt -->
{{--            <div class="bg-yellow-100 border-l-4 border-yellow-500 p-4 mb-4">--}}
{{--                <p class="text-yellow-700 font-bold">Developer Tool</p>--}}
{{--                <p class="text-yellow-700 mb-2">Click the button below to see the generated prompt:</p>--}}
{{--                <button--}}
{{--                    wire:click="showGeneratedPrompt"--}}
{{--                    class="bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded"--}}
{{--                >--}}
{{--                    Show Generated Prompt--}}
{{--                </button>--}}
{{--            </div>--}}

            <div class="flex justify-between">
                <x-button wire:click="previousStep" class="bg-gray-200 text-gray-800 hover:bg-gray-300">
                    {{ __('interview.navigation.back') }}
                </x-button>

                <x-button
                    wire:click="initFactGatheringInterview"
                    wire:loading.attr="disabled"
                    wire:target="initFactGatheringInterview"
                    class="justify-center bg-blue-600 hover:bg-blue-700"
                >
                    <span wire:loading.remove wire:target="initFactGatheringInterview">
                        {{ __('interview.overview.begin_fact_gathering') }}
                    </span>
                    <span wire:loading wire:target="initFactGatheringInterview" class="flex items-center">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        {{ __('interview.overview.preparing_interview') }}
                    </span>
                </x-button>
            </div>
        </div>
    </div>
