<div>
    <!-- Modal Dialog -->
    <dialog id="create-case-modal" class="modal" x-data x-bind:open="$wire.showModal" x-init="window.addEventListener('open-create-case-modal', () => {
        $wire.openModal();
    });
    $el.addEventListener('close', () => {
        $wire.closeModal();
    });">
        <div class="modal-box">
            <h3 class="mb-4 text-lg font-bold">{{ __('app.create_new_case') }}</h3>

            @if($insufficientCredits)
                <div class="alert alert-error mb-4">
                    <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                    <div>
                        <h3 class="font-bold">Insufficient Credits</h3>
                        <div class="text-sm">You need {{ $requiredCredits }} credits to create a new case. Your current balance is {{ $currentBalance }}.</div>
                        <a href="{{ route('credits.purchase.form') }}" class="btn btn-sm btn-outline mt-2">Purchase Credits</a>
                    </div>
                </div>
            @endif

            <form wire:submit="createCase">
                <div class="space-y-4">
                    <!-- Case Title -->
                    <div>
                        <label for="title"
                            class="block text-sm font-medium text-base-content/70">{{ __('forms.case_title') }} <span
                                class="text-error">*</span></label>
                        <input type="text" id="title" wire:model="title" class="w-full mt-1 input input-bordered"
                            placeholder="{{ __('forms.enter_case_title') }}" />
                        @error('title')
                            <span class="mt-1 text-sm text-error">{{ $message }}</span>
                        @enderror
                    </div>

                    <!-- Case Number (Optional) -->
                    <div>
                        <label for="case_number"
                            class="block text-sm font-medium text-base-content/70">{{ __('forms.case_reference_number') }}
                            <span class="text-xs text-base-content/50">({{ __('forms.optional') }})</span></label>
                        <input type="text" id="case_number" wire:model="case_number"
                            class="w-full mt-1 input input-bordered"
                            placeholder="{{ __('app.enter_case_number_if_available') }}" />
                        @error('case_number')
                            <span class="mt-1 text-sm text-error">{{ $message }}</span>
                        @enderror
                    </div>
                </div>

                <div class="mt-6 modal-action">
                    <button type="button" class="btn btn-ghost" wire:click="closeModal">{{ __('app.cancel') }}</button>
                    <button type="submit" class="btn btn-primary" wire:loading.attr="disabled" @if($insufficientCredits) disabled @endif>
                        <span wire:loading.remove>
                            @if($insufficientCredits)
                                Need {{ $requiredCredits }} Credits
                            @else
                                {{ __('app.create_and_continue') }}
                            @endif
                        </span>
                        <span wire:loading>{{ __('app.creating') }}</span>
                    </button>
                </div>
            </form>
        </div>
        <form method="dialog" class="modal-backdrop">
            <button @click="$wire.closeModal()">close</button>
        </form>
    </dialog>
</div>
