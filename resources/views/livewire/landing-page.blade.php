<div class="flex flex-col min-h-screen street-gradient" x-data="{
        mobileMenuOpen: false,
        darkMode: false,
        currentView: @entangle('currentView'),
        testAlpine: true,
    }" :class="{ 'dark': darkMode }">
    <!-- Font imports for the deepseek design -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bebas+Neue&family=Press+Start+2P&family=Russo+One&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css">

    <style>
        body {
            font-family: 'Russo One', sans-serif;
            background-color: #111;
            color: #fff;
        }

        .street-gradient {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
        }

        .dark .street-gradient {
            background: linear-gradient(135deg, #0f0f1a 0%, #0d1226 50%, #081224 100%);
        }

        .fight-card {
            background: rgba(30, 30, 46, 0.8);
            border: 2px solid #e94560;
            border-radius: 8px;
            box-shadow: 0 8px 32px rgba(233, 69, 96, 0.3);
            transition: all 0.3s ease;
        }

        .fight-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(233, 69, 96, 0.5);
        }

        .fight-btn {
            background: linear-gradient(135deg, #e94560 0%, #c81d45 100%);
            color: white;
            font-weight: bold;
            text-transform: uppercase;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(233, 69, 96, 0.4);
        }

        .fight-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 25px rgba(233, 69, 96, 0.6);
        }

        .pixel-text {
            font-family: 'Press Start 2P', cursive;
            text-shadow: 3px 3px 0 rgba(0, 0, 0, 0.8);
            letter-spacing: 1px;
        }

        .vs-badge {
            display: inline-block;
            padding: 0.5rem 1rem;
            background: rgba(233, 69, 96, 0.2);
            border: 2px solid #e94560;
            border-radius: 4px;
            color: #e94560;
            font-weight: bold;
            text-transform: uppercase;
            font-size: 0.875rem;
        }

        .health-bar {
            height: 6px;
            background: linear-gradient(to right, #e94560, #ff6b81);
            border-radius: 3px;
            position: relative;
            overflow: hidden;
        }

        .health-bar::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,0.5) 50%, rgba(255,255,255,0) 100%);
            animation: health-pulse 1.5s infinite;
        }

        @keyframes health-pulse {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        .character-select {
            border: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .character-select:hover, .character-select.active {
            border-color: #e94560;
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(233, 69, 96, 0.4);
        }

        .hit-effect {
            animation: hit 0.3s ease-out;
        }

        @keyframes hit {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); box-shadow: 0 0 30px rgba(233, 69, 96, 0.8); }
            100% { transform: scale(1); }
        }

        .combo-counter {
            position: absolute;
            top: -15px;
            right: -10px;
            background: #e94560;
            color: white;
            font-weight: bold;
            padding: 2px 8px;
            border-radius: 4px;
            font-size: 0.75rem;
            animation: pop-in 0.3s ease-out;
        }

        @keyframes pop-in {
            0% { transform: scale(0); }
            70% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
    </style>

    <!-- Header from deepseek design -->
    <header class="fixed top-0 left-0 z-30 w-full py-4 bg-black/50 backdrop-blur-md">
        <div class="container flex items-center justify-between px-6 mx-auto">
            <a href="#" class="flex items-center space-x-2">
                <img src="{{ asset('images/' . app()->getLocale() . '/JQ.png') }}" class="h-10" alt="Justice Quest Logo">
                <span class="text-xl font-bold text-white">JUSTICE QUEST</span>
            </a>

            <div class="items-center hidden space-x-6 md:flex">
                <a href="#features" class="font-bold text-gray-300 transition duration-300 hover:text-red-500">FEATURES</a>
                <a href="#how-it-works" class="font-bold text-gray-300 transition duration-300 hover:text-red-500">HOW TO PLAY</a>
                <button @click="darkMode = !darkMode" class="p-2 text-gray-300 rounded-full hover:text-yellow-400">
                    <i x-show="!darkMode" class="fas fa-moon"></i>
                    <i x-show="darkMode" class="fas fa-sun"></i>
                </button>
                <button @click="$wire.showLogin()" class="px-6 py-3 rounded-lg fight-btn">
                    <i class="mr-2 fas fa-sign-in-alt"></i> LOGIN
                </button>
                <button @click="$wire.showRegister()" class="px-6 py-3 rounded-lg fight-btn">
                    <i class="mr-2 fas fa-user-plus"></i> REGISTER
                </button>
            </div>

            <button @click="mobileMenuOpen = !mobileMenuOpen" class="p-2 text-white md:hidden">
                <i x-show="!mobileMenuOpen" class="fas fa-bars"></i>
                <i x-show="mobileMenuOpen" class="fas fa-times"></i>
            </button>
        </div>

        <!-- Mobile menu -->
        <div x-show="mobileMenuOpen" x-transition class="fixed inset-0 z-40 flex items-center justify-center w-full h-full bg-black/90 md:hidden">
            <div class="flex flex-col items-center space-y-6 text-center">
                <a href="#features" @click="mobileMenuOpen = false" class="text-xl font-bold text-gray-300 transition duration-300 hover:text-red-500">FEATURES</a>
                <a href="#how-it-works" @click="mobileMenuOpen = false" class="text-xl font-bold text-gray-300 transition duration-300 hover:text-red-500">HOW TO PLAY</a>
                <button @click="darkMode = !darkMode" class="flex items-center px-4 py-2 text-gray-300 rounded-lg hover:text-yellow-400 bg-black/50">
                    <i x-show="!darkMode" class="mr-2 fas fa-moon"></i>
                    <i x-show="darkMode" class="mr-2 fas fa-sun"></i>
                    TOGGLE MODE
                </button>
                <button @click="$wire.showLogin(); mobileMenuOpen = false" class="block px-6 py-3 mt-4 text-center rounded-lg fight-btn">
                    <i class="mr-2 fas fa-sign-in-alt"></i> LOGIN
                </button>
                <button @click="$wire.showRegister(); mobileMenuOpen = false" class="block px-6 py-3 mt-4 text-center rounded-lg fight-btn">
                    <i class="mr-2 fas fa-user-plus"></i> REGISTER
                </button>
            </div>
        </div>
    </header>

    <div x-data="{
        init() {
            // Add event listeners for music player controls
            this.$nextTick(() => {
                // Toggle music button
                const toggleMusicBtn = document.getElementById('toggle-music');
                if (toggleMusicBtn) {
                    toggleMusicBtn.addEventListener('click', () => {
                        Alpine.store('musicPlayer').toggleMusic();
                    });
                }

                // Force play music button
                const forcePlayMusicBtn = document.getElementById('force-play-music');
                if (forcePlayMusicBtn) {
                    forcePlayMusicBtn.addEventListener('click', () => {
                        Alpine.store('musicPlayer').forcePlayMusic();
                    });
                }

                // Music info dropdown
                const musicInfo = document.getElementById('music-info');
                if (musicInfo) {
                    musicInfo.addEventListener('click', () => {
                        const dropdown = document.getElementById('playlist-dropdown');
                        if (dropdown) {
                            dropdown.classList.toggle('hidden');
                        }
                    });
                }

                // Populate playlist dropdown
                this.populatePlaylistDropdown();
            });
        },

        // Populate playlist dropdown with items
        populatePlaylistDropdown() {
            const playlistItems = document.getElementById('playlist-items');
            if (!playlistItems) return;

            const musicPlayer = Alpine.store('musicPlayer');
            if (!musicPlayer) return;

            playlistItems.innerHTML = '';

            Object.keys(musicPlayer.playlists).forEach(track => {
                const item = document.createElement('div');
                item.className = `px-2 py-1 text-xs text-white hover:bg-white/20 rounded cursor-pointer transition-colors duration-200 ${track === musicPlayer.currentTrack ? 'bg-white/20' : ''}`;
                item.textContent = track;
                item.addEventListener('click', () => {
                    musicPlayer.changeTrack(track);
                    // Update the dropdown UI
                    this.populatePlaylistDropdown();
                });
                playlistItems.appendChild(item);
            });
        }
    }">
    <div x-show="testAlpine" class="fixed top-0 left-0 z-50 p-2 m-4 text-white bg-green-500 rounded-md">Alpine.js is
        working</div>
    <!-- Background Music Player (hidden) - Preserve from original welcome page -->
    <div id="music-player-container" class="fixed z-40 flex items-center space-x-2 bottom-4 right-4">
        <div id="youtube-player" class="hidden"></div>
        <div id="play-music-prompt"
            class="hidden p-3 mr-2 bg-black border rounded-lg shadow-lg backdrop-blur-sm border-white/10">
            <button id="force-play-music" class="flex items-center text-xs text-white hover:text-yellow-400">
                <i class="mr-2 fas fa-play"></i> Click to play music
            </button>
        </div>
        <div class="relative group">
            <div
                class="flex items-center p-2 space-x-2 bg-black border rounded-full shadow-lg backdrop-blur-sm border-white/10">
                <button id="toggle-music"
                    class="flex items-center justify-center w-8 h-8 text-white transition-colors duration-300 rounded-full hover:text-yellow-400 bg-black/40 hover:bg-black/60">
                    <i id="music-icon" class="fas fa-volume-up"></i>
                </button>
                <div id="music-info"
                    class="text-white text-xs hidden sm:block pr-2 max-w-[120px] truncate cursor-pointer">Loading...
                </div>
                <button id="show-playlist"
                    class="items-center justify-center hidden w-6 h-6 text-white transition-colors duration-300 hover:text-yellow-400 sm:flex">
                    <i class="transition-transform duration-300 fas fa-chevron-up group-hover:rotate-180"></i>
                </button>
            </div>

            <!-- Playlist dropdown -->
            <div id="playlist-dropdown"
                class="absolute right-0 hidden w-48 p-2 mb-2 transition-opacity duration-300 border rounded-lg shadow-lg opacity-0 bottom-full bg-black/80 backdrop-blur-md border-white/10 group-hover:block group-hover:opacity-100">
                <div class="px-2 mb-2 text-xs font-semibold text-white">Select Track:</div>
                <div id="playlist-items" class="space-y-1 overflow-y-auto max-h-32">
                    <!-- Playlist items will be added here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="flex flex-col items-center justify-center flex-1 pt-32 pb-6">
        <!-- Main View -->
        <div x-show="currentView === 'main'" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100">

            <!-- Fight Intro Section from deepseek -->
            <section class="relative pt-32 pb-20 overflow-hidden">
                <div class="absolute inset-0 bg-[url('data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHdpZHRoPSI1IiBoZWlnaHQ9IjUiPgo8cmVjdCB3aWR0aD0iNSIgaGVpZ2h0PSI1IiBmaWxsPSIjMDAwIj48L3JlY3Q+CjxwYXRoIGQ9Ik0wIDVMNSAwWk02IDRMNCA2Wk0tMSAxTDEgLTFaIiBzdHJva2U9IiNlOTQ1NjAiIHN0cm9rZS13aWR0aD0iMSI+PC9wYXRoPgo8L3N2Zz4=')] opacity-10"></div>

                <div class="container relative z-10 px-6 mx-auto">
                    <div class="flex flex-col items-center md:flex-row">
                        <div class="mb-12 md:w-1/2 md:mb-0">
                            <div class="mb-6 vs-badge">
                                <i class="mr-2 fas fa-bolt"></i> LEGAL AI ASSISTANT
                            </div>
                            <h1 class="mb-6 text-5xl font-bold leading-tight md:text-6xl lg:text-7xl pixel-text">
                                FIGHT FOR <span class="text-red-500">YOUR RIGHTS</span>
                            </h1>
                            <p class="mb-8 text-xl text-gray-300">
                                Enter the legal arena with AI-powered weapons. Draft documents, manage cases, and defeat injustice with Justice Quest - your ultimate legal fighting game.
                            </p>
                            <div class="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                                <button @click="$wire.showLogin()" class="px-8 py-4 text-lg rounded-lg fight-btn">
                                    <i class="mr-2 fas fa-sign-in-alt"></i> LOGIN
                                </button>
                                <button @click="$wire.showRegister()" class="px-8 py-4 font-bold text-center text-white transition border-2 border-red-500 rounded-lg bg-black/50 hover:bg-red-500/20">
                                    <i class="mr-2 fas fa-user-plus"></i> REGISTER
                                </button>
                            </div>
                        </div>
                        <div class="relative md:w-1/2">
                            <div class="relative">
                                <img src="{{ asset('images/' . app()->getLocale() . '/JQ.png') }}" alt="Justice Quest" class="w-full max-w-md mx-auto transform border-4 border-red-500 rounded-lg rotate-2">
                                <div class="absolute flex items-center px-6 py-2 font-bold text-white bg-red-500 rounded-lg shadow-lg -bottom-6 -right-6">
                                    <i class="mr-2 fas fa-robot"></i> AI ASSISTANT
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Fight Stats Section -->
            <section class="py-12 bg-black/30">
                <div class="container px-6 mx-auto">
                    <div class="grid grid-cols-2 gap-6 text-center md:grid-cols-4">
                        <div class="p-6 fight-card">
                            <div class="mb-2 text-4xl font-bold text-red-500">1000+</div>
                            <div class="text-sm tracking-wider text-gray-300 uppercase">DOCS CREATED</div>
                            <div class="w-full mt-4 health-bar"></div>
                        </div>
                        <div class="p-6 fight-card">
                            <div class="mb-2 text-4xl font-bold text-red-500">24/7</div>
                            <div class="text-sm tracking-wider text-gray-300 uppercase">AI ASSIST</div>
                            <div class="w-full mt-4 health-bar"></div>
                        </div>
                        <div class="p-6 fight-card">
                            <div class="mb-2 text-4xl font-bold text-red-500">50+</div>
                            <div class="text-sm tracking-wider text-gray-300 uppercase">TEMPLATES</div>
                            <div class="w-full mt-4 health-bar"></div>
                        </div>
                        <div class="p-6 fight-card">
                            <div class="mb-2 text-4xl font-bold text-red-500">2</div>
                            <div class="text-sm tracking-wider text-gray-300 uppercase">LANGUAGES</div>
                            <div class="w-full mt-4 health-bar"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Fight Features Section -->
            <section id="features" class="py-20 bg-black/20">
                <div class="container px-6 mx-auto">
                    <div class="mb-16 text-center">
                        <h2 class="mb-6 text-4xl font-bold md:text-5xl pixel-text">
                            YOUR <span class="text-red-500">FIGHTING MOVES</span>
                        </h2>
                        <p class="max-w-3xl mx-auto text-xl text-gray-300">
                            Master these powerful legal techniques to dominate the courtroom
                        </p>
                    </div>

                    <div class="grid gap-8 md:grid-cols-2 lg:grid-cols-3">
                        <!-- Move 1 -->
                        <div class="p-8 fight-card" x-data="{ combo: 0 }" @click="combo++" :class="{ 'hit-effect': combo > 0 }">
                            <div class="flex items-center justify-center w-16 h-16 mb-6 text-3xl text-red-500 border-2 border-red-500 rounded-lg bg-red-500/20">
                                <i class="fas fa-folder-open"></i>
                            </div>
                            <h3 class="mb-4 text-2xl font-bold text-white">CASE DASHBOARD</h3>
                            <p class="mb-6 text-gray-300">
                                Your fight hub. Track case progress, manage documents, and monitor important dates.
                            </p>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-400">SPECIAL MOVE</span>
                                <span class="font-bold text-red-500">HP: 100</span>
                            </div>
                            <div x-show="combo > 0" class="combo-counter" x-text="'COMBO x' + combo"></div>
                        </div>

                        <!-- Move 2 -->
                        <div class="p-8 fight-card" x-data="{ combo: 0 }" @click="combo++" :class="{ 'hit-effect': combo > 0 }">
                            <div class="flex items-center justify-center w-16 h-16 mb-6 text-3xl text-red-500 border-2 border-red-500 rounded-lg bg-red-500/20">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h3 class="mb-4 text-2xl font-bold text-white">AI LEGAL GUIDE</h3>
                            <p class="mb-6 text-gray-300">
                                Your training partner. Get case-specific advice and document analysis 24/7.
                            </p>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-400">ULTIMATE MOVE</span>
                                <span class="font-bold text-red-500">HP: 250</span>
                            </div>
                            <div x-show="combo > 0" class="combo-counter" x-text="'COMBO x' + combo"></div>
                        </div>

                        <!-- Move 3 -->
                        <div class="p-8 fight-card" x-data="{ combo: 0 }" @click="combo++" :class="{ 'hit-effect': combo > 0 }">
                            <div class="flex items-center justify-center w-16 h-16 mb-6 text-3xl text-red-500 border-2 border-red-500 rounded-lg bg-red-500/20">
                                <i class="fas fa-file-alt"></i>
                            </div>
                            <h3 class="mb-4 text-2xl font-bold text-white">DOC CRAFTER</h3>
                            <p class="mb-6 text-gray-300">
                                Forge powerful legal documents with section-based editing and AI generation.
                            </p>
                            <div class="flex items-center justify-between">
                                <span class="text-sm text-gray-400">POWER MOVE</span>
                                <span class="font-bold text-red-500">HP: 150</span>
                            </div>
                            <div x-show="combo > 0" class="combo-counter" x-text="'COMBO x' + combo"></div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- How It Works Section -->
            <section id="how-it-works" class="py-20 bg-black/40">
                <div class="container px-6 mx-auto">
                    <div class="mb-16 text-center">
                        <h2 class="mb-6 text-4xl font-bold md:text-5xl pixel-text">
                            HOW TO <span class="text-red-500">PLAY THE GAME</span>
                        </h2>
                        <p class="max-w-3xl mx-auto text-xl text-gray-300">
                            Follow these steps to win your legal battles
                        </p>
                    </div>

                    <div class="grid gap-8 md:grid-cols-4">
                        <!-- Round 1 -->
                        <div class="flex flex-col items-center">
                            <div class="flex items-center justify-center w-24 h-24 mb-6 text-3xl font-bold text-white bg-red-500 border-4 border-yellow-400 rounded-full shadow-lg">
                                1
                            </div>
                            <div class="p-6 text-center fight-card">
                                <i class="mb-4 text-4xl text-red-500 fas fa-upload"></i>
                                <h3 class="mb-2 text-xl font-bold text-white">UPLOAD CASE</h3>
                                <p class="text-sm text-gray-300">
                                    Add your case details and documents to begin your fight
                                </p>
                                <div class="mt-4 font-mono text-xs text-yellow-400">
                                    ← → ↑ ↓ + PUNCH
                                </div>
                            </div>
                        </div>

                        <!-- Round 2 -->
                        <div class="flex flex-col items-center">
                            <div class="flex items-center justify-center w-24 h-24 mb-6 text-3xl font-bold text-white bg-red-500 border-4 border-yellow-400 rounded-full shadow-lg">
                                2
                            </div>
                            <div class="p-6 text-center fight-card">
                                <i class="mb-4 text-4xl text-red-500 fas fa-lightbulb"></i>
                                <h3 class="mb-2 text-xl font-bold text-white">AI ANALYSIS</h3>
                                <p class="text-sm text-gray-300">
                                    Let our AI analyze your case and suggest strategies
                                </p>
                                <div class="mt-4 font-mono text-xs text-yellow-400">
                                    ↓ ↘ → + KICK
                                </div>
                            </div>
                        </div>

                        <!-- Round 3 -->
                        <div class="flex flex-col items-center">
                            <div class="flex items-center justify-center w-24 h-24 mb-6 text-3xl font-bold text-white bg-red-500 border-4 border-yellow-400 rounded-full shadow-lg">
                                3
                            </div>
                            <div class="p-6 text-center fight-card">
                                <i class="mb-4 text-4xl text-red-500 fas fa-file-signature"></i>
                                <h3 class="mb-2 text-xl font-bold text-white">DRAFT DOCS</h3>
                                <p class="text-sm text-gray-300">
                                    Create powerful legal documents with our AI editor
                                </p>
                                <div class="mt-4 font-mono text-xs text-yellow-400">
                                    → ↓ ↘ + PUNCH
                                </div>
                            </div>
                        </div>

                        <!-- Round 4 -->
                        <div class="flex flex-col items-center">
                            <div class="flex items-center justify-center w-24 h-24 mb-6 text-3xl font-bold text-white bg-red-500 border-4 border-yellow-400 rounded-full shadow-lg">
                                4
                            </div>
                            <div class="p-6 text-center fight-card">
                                <i class="mb-4 text-4xl text-red-500 fas fa-gavel"></i>
                                <h3 class="mb-2 text-xl font-bold text-white">FIGHT ON</h3>
                                <p class="text-sm text-gray-300">
                                    Manage your case and proceed with confidence
                                </p>
                                <div class="mt-4 font-mono text-xs text-yellow-400">
                                    ↓ ↙ ← → + KICK
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Fight Callout Section -->
            <section id="get-started" class="py-20 bg-gradient-to-r from-red-900 to-red-700">
                <div class="container px-6 mx-auto text-center">
                    <h2 class="mb-6 text-4xl font-bold md:text-5xl pixel-text">
                        READY TO <span class="text-yellow-400">ENTER THE ARENA</span>?
                    </h2>
                    <p class="max-w-2xl mx-auto mb-8 text-xl text-red-100">
                        Sign up now and get your first month free! Fight for justice with AI-powered tools.
                    </p>
                    <div class="flex flex-col justify-center space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4">
                        <button @click="$wire.showRegister()" class="px-8 py-4 text-lg rounded-lg fight-btn">
                            <i class="mr-2 fas fa-user-plus"></i> CREATE ACCOUNT
                        </button>
                        <button @click="$wire.showLogin()" class="px-8 py-4 font-bold text-white transition border-2 border-white rounded-lg bg-black/50 hover:bg-white/10">
                            <i class="mr-2 fas fa-sign-in-alt"></i> LOGIN
                        </button>
                    </div>
                </div>
            </section>
        </div>

        <!-- Login View -->
        <div x-show="currentView === 'login'" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
            class="w-full max-w-md">
            <div class="mb-8 text-center">
                <img src="{{ asset('images/' . app()->getLocale() . '/JQ.png') }}" class="h-20 mx-auto mb-4"
                    alt="Justice Quest Logo">
                <h2 class="text-2xl font-bold text-white">Login to Your Account</h2>
            </div>

            <div
                class="p-6 border rounded-lg shadow-lg bg-black/30 backdrop-blur-md border-indigo-500/30 shadow-indigo-500/20">
                <form wire:submit.prevent="login" class="space-y-6" x-data="{ submitting: false }"
                    @submit="submitting = true; console.log('Form submitted');">
                    <div>
                        <label for="loginEmail" class="block text-sm font-medium text-white">Email</label>
                        <input wire:model="loginEmail" id="loginEmail" type="email"
                            class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-indigo-500/30 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        @error('loginEmail')
                            <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <label for="loginPassword" class="block text-sm font-medium text-white">Password</label>
                        <input wire:model="loginPassword" id="loginPassword" type="password"
                            class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-indigo-500/30 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent">
                        @error('loginPassword')
                            <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
                        @enderror
                    </div>

                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <input wire:model="remember" id="remember" type="checkbox"
                                class="w-4 h-4 text-indigo-600 bg-white border-gray-400 rounded focus:ring-indigo-500">
                            <label for="remember" class="block ml-2 text-sm text-gray-300">Remember me</label>
                        </div>

                        @if (Route::has('password.request'))
                            <a href="{{ route('password.request') }}"
                                class="text-sm text-indigo-400 hover:text-indigo-300">
                                Forgot password?
                            </a>
                        @endif
                    </div>

                    <div>
                        <button type="submit"
                            class="flex justify-center w-full px-4 py-2 text-sm font-medium text-white transition-colors duration-300 bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                            :disabled="submitting">
                            <span x-show="!submitting">Sign in</span>
                            <span x-show="submitting" class="flex items-center">
                                <svg class="w-4 h-4 mr-2 -ml-1 text-white animate-spin"
                                    xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10"
                                        stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                                Processing...
                            </span>
                        </button>
                    </div>
                </form>

                <div class="flex items-center justify-center mt-6">
                    <button wire:click="showMain"
                        class="text-sm text-gray-400 transition-colors duration-300 hover:text-white">
                        <span class="mr-2">←</span> Back to main menu
                    </button>
                </div>
            </div>
        </div>

        <!-- Register View -->
        <div x-show="currentView === 'register'" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
            class="w-full max-w-md">
            <div class="mb-8 text-center">
                <img src="{{ asset('images/' . app()->getLocale() . '/JQ.png') }}" class="h-20 mx-auto mb-4"
                    alt="Justice Quest Logo">
                <h2 class="text-2xl font-bold text-white">Create Your Account</h2>
            </div>

            <div
                class="p-6 border rounded-lg shadow-lg bg-black/30 backdrop-blur-md border-purple-500/30 shadow-purple-500/20">
                <form wire:submit.prevent="register" class="space-y-6">
                    <div>
                        <label for="name" class="block text-sm font-medium text-white">Name</label>
                        <input wire:model="name" id="name" type="text"
                            class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-purple-500/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        @error('name')
                            <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <label for="username" class="block text-sm font-medium text-white">Username</label>
                        <input wire:model.live="username" id="username" type="text"
                            class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-purple-500/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        @error('username')
                            <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
                        @enderror
                        <p class="mt-1 text-xs text-gray-400">
                            Username must be unique, 3-25 characters, and can only contain letters, numbers, dashes, and
                            underscores.
                        </p>
                    </div>

                    <div>
                        <label for="email" class="block text-sm font-medium text-white">Email</label>
                        <input wire:model="email" id="email" type="email"
                            class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-purple-500/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        @error('email')
                            <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <label for="password" class="block text-sm font-medium text-white">Password</label>
                        <input wire:model="password" id="password" type="password"
                            class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-purple-500/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        @error('password')
                            <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <label for="password_confirmation" class="block text-sm font-medium text-white">Confirm
                            Password</label>
                        <input wire:model="password_confirmation" id="password_confirmation" type="password"
                            class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-purple-500/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                    </div>

                    <div class="mt-4">
                        <label class="flex items-center">
                            <input wire:model.live="is_attorney" type="checkbox"
                                class="w-4 h-4 text-purple-600 bg-white border-gray-400 rounded focus:ring-purple-500">
                            <span class="ml-2 text-sm text-gray-300">I am an attorney</span>
                        </label>
                    </div>

                    @if ($is_attorney)
                        <div>
                            <label for="bar_card_number" class="block text-sm font-medium text-white">Bar Card
                                Number</label>
                            <input wire:model="bar_card_number" id="bar_card_number" type="text"
                                class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-purple-500/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                            @error('bar_card_number')
                                <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
                            @enderror
                            <p class="mt-1 text-xs text-gray-400">
                                Please enter your bar card number or attorney license number.
                            </p>
                        </div>
                    @endif

                    <div>
                        <label for="zip_code" class="block text-sm font-medium text-white">ZIP Code</label>
                        <div x-data="{
                            retryCount: 0,
                            maxRetries: 20,
                            retryInterval: 200,
                            initGooglePlaces() {
                                if (typeof google === 'undefined' || typeof google.maps === 'undefined' || typeof google.maps.places === 'undefined') {
                                    this.retryCount++;
                                    if (this.retryCount <= this.maxRetries) {
                                        console.log(`Waiting for Google Maps API to load (attempt ${this.retryCount}/${this.maxRetries})`);
                                        setTimeout(() => this.initGooglePlaces(), this.retryInterval);
                                    } else {
                                        console.error('Google Maps API failed to load after maximum retries');
                                    }
                                    return;
                                }

                                console.log('Google Maps Places API loaded successfully');
                                const input = document.getElementById('zip_code');
                                const options = {
                                    componentRestrictions: { country: 'us' },
                                    fields: ['address_components', 'geometry'],
                                    types: ['postal_code']
                                };

                                const autocomplete = new google.maps.places.Autocomplete(input, options);

                                autocomplete.addListener('place_changed', () => {
                                    const place = autocomplete.getPlace();

                                    if (!place.geometry || !place.geometry.location) {
                                        return;
                                    }

                                    // Extract ZIP code from place
                                    let zipCode = '';
                                    for (const component of place.address_components) {
                                        if (component.types.includes('postal_code')) {
                                            zipCode = component.short_name;
                                            break;
                                        }
                                    }

                                    // Set ZIP code and coordinates
                                    if (zipCode) {
                                        @this.set('zip_code', zipCode);
                                        @this.call('setCoordinates',
                                            place.geometry.location.lat(),
                                            place.geometry.location.lng()
                                        );
                                    }
                                });
                            }
                        }" x-init="$nextTick(() => initGooglePlaces())">
                            <input wire:model="zip_code" id="zip_code" type="text"
                                class="block w-full px-3 py-2 mt-1 text-black placeholder-gray-500 bg-gray-100 border rounded-md border-purple-500/30 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                        </div>
                        @error('zip_code')
                            <span class="mt-1 text-xs text-red-500">{{ $message }}</span>
                        @enderror

                        @if (app()->environment('local', 'development', 'testing'))
                            <!-- Debug information -->
                            <div class="mt-2 text-xs text-gray-400">
                                <p class="font-semibold">Current values:</p>
                                <p>ZIP: <span class="text-blue-400">{{ $zip_code }}</span></p>
                                <p>Lat: <span class="text-green-400">{{ $latitude ?? 'Not set' }}</span></p>
                                <p>Lng: <span class="text-green-400">{{ $longitude ?? 'Not set' }}</span></p>
                            </div>
                            <!-- End debug information -->
                        @endif
                    </div>

                    <div class="flex items-center">
                        <input wire:model="terms" id="terms" type="checkbox"
                            class="w-4 h-4 text-purple-600 bg-white border-gray-400 rounded focus:ring-purple-500">
                        <label for="terms" class="block ml-2 text-sm text-gray-300">
                            I agree to the <a href="/terms" class="text-purple-400 hover:text-purple-300"
                                target="_blank">Terms of Service and Privacy Policy</a>
                        </label>
                        @error('terms')
                            <span class="block mt-1 text-xs text-red-500">{{ $message }}</span>
                        @enderror
                    </div>

                    <div>
                        <button type="submit"
                            class="flex justify-center w-full px-4 py-2 text-sm font-medium text-white transition-colors duration-300 bg-purple-600 border border-transparent rounded-md shadow-sm hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500">
                            Register
                        </button>
                    </div>
                </form>

                <div class="flex items-center justify-center mt-6">
                    <button wire:click="showMain"
                        class="text-sm text-gray-400 transition-colors duration-300 hover:text-white">
                        <span class="mr-2">←</span> Back to main menu
                    </button>
                </div>
            </div>
        </div>

        <!-- How It Works View -->
        <div x-show="currentView === 'howItWorks'" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
            class="w-full max-w-2xl">
            <div class="mb-8 text-center">
                <img src="{{ asset('images/' . app()->getLocale() . '/JQ.png') }}" class="h-20 mx-auto mb-4"
                    alt="Justice Quest Logo">

                <h2 class="text-2xl font-bold text-white">How Justice Quest Works</h2>
            </div>

            <div
                class="p-6 border rounded-lg shadow-lg bg-black/30 backdrop-blur-md border-green-500/30 shadow-green-500/20">
                <div class="space-y-8">
                    <!-- Step 1 -->
                    <div class="flex items-start space-x-4">
                        <div
                            class="flex items-center justify-center flex-shrink-0 w-10 h-10 text-lg font-bold text-white bg-green-600 rounded-full">
                            1</div>
                        <div>
                            <h3 class="mb-2 text-xl font-bold text-white">Describe Your Legal Issue</h3>
                            <p class="text-gray-300">Start by chatting with our AI legal assistant. Explain your
                                situation in plain language, and our system will analyze your needs.</p>
                        </div>
                    </div>

                    <!-- Step 2 -->
                    <div class="flex items-start space-x-4">
                        <div
                            class="flex items-center justify-center flex-shrink-0 w-10 h-10 text-lg font-bold text-white bg-green-600 rounded-full">
                            2</div>
                        <div>
                            <h3 class="mb-2 text-xl font-bold text-white">Get Your Strategy</h3>
                            <p class="text-gray-300">Receive a customized action plan based on your specific situation.
                                Our system will generate the necessary documents and guide you through each step.</p>
                        </div>
                    </div>

                    <!-- Step 3 -->
                    <div class="flex items-start space-x-4">
                        <div
                            class="flex items-center justify-center flex-shrink-0 w-10 h-10 text-lg font-bold text-white bg-green-600 rounded-full">
                            3</div>
                        <div>
                            <h3 class="mb-2 text-xl font-bold text-white">Take Action With Confidence</h3>
                            <p class="text-gray-300">Execute your plan with our ongoing support. Track progress,
                                receive reminders, and get assistance whenever you need it.</p>
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="pt-6 mt-8 border-t border-green-500/20">
                        <h3 class="mb-4 text-xl font-bold text-white">Key Features</h3>
                        <ul class="grid grid-cols-1 gap-4 md:grid-cols-2">
                            <li class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-300">AI-powered document generation</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-300">Smart case organization</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-300">Automated deadline reminders</span>
                            </li>
                            <li class="flex items-center space-x-2">
                                <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-300">Secure document storage</span>
                            </li>
                        </ul>
                    </div>
                </div>

                <div class="flex items-center justify-center mt-8">
                    <button wire:click="showMain"
                        class="text-sm text-gray-400 transition-colors duration-300 hover:text-white">
                        <span class="mr-2">←</span> Back to main menu
                    </button>
                </div>
            </div>
        </div>

        <!-- About Us View -->
        <div x-show="currentView === 'about'" x-transition:enter="transition ease-out duration-300"
            x-transition:enter-start="opacity-0 scale-95" x-transition:enter-end="opacity-100 scale-100"
            class="w-full max-w-2xl">
            <div class="mb-8 text-center">
                <img src="{{ asset('images/' . app()->getLocale() . '/JQ.png') }}" class="h-20 mx-auto mb-4"
                    alt="Justice Quest Logo">
                <h2 class="text-2xl font-bold text-white">About Justice Quest</h2>
            </div>

            <div
                class="p-6 border rounded-lg shadow-lg bg-black/30 backdrop-blur-md border-yellow-500/30 shadow-yellow-500/20">
                <div class="prose prose-lg prose-invert max-w-none">
                    <p class="text-gray-300">Justice Quest was founded with a simple mission: to make legal services
                        accessible to everyone. We believe that navigating the legal system shouldn't require a law
                        degree or a fortune in attorney fees.</p>

                    <p class="mt-4 text-gray-300">Our platform combines cutting-edge AI technology with legal expertise
                        to provide you with the tools and guidance you need to handle common legal matters with
                        confidence.</p>

                    <h3 class="mt-6 mb-3 text-xl font-bold text-white">Our Vision</h3>
                    <p class="text-gray-300">We envision a world where everyone has equal access to justice, regardless
                        of their background or resources. Justice Quest is our contribution to making that vision a
                        reality.</p>

                    <h3 class="mt-6 mb-3 text-xl font-bold text-white">Our Team</h3>
                    <p class="text-gray-300">Justice Quest was created by a diverse team of legal professionals,
                        technologists, and designers who share a passion for making legal services more accessible and
                        user-friendly.</p>

                    <div class="pt-6 mt-6 border-t border-yellow-500/20">
                        <p class="text-gray-300">Have questions or feedback? We'd love to hear from you! Contact us at
                            <a href="mailto:<EMAIL>"
                                class="text-yellow-400 hover:text-yellow-300"><EMAIL></a>.
                        </p>
                    </div>
                </div>

                <div class="flex items-center justify-center mt-8">
                    <button wire:click="showMain"
                        class="text-sm text-gray-400 transition-colors duration-300 hover:text-white">
                        <span class="mr-2">←</span> Back to main menu
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Video game-style footer from deepseek -->
    <footer class="py-8 bg-black/80">
        <div class="container px-6 mx-auto">
            <div class="grid gap-8 md:grid-cols-4">
                <div>
                    <img src="{{ asset('images/' . app()->getLocale() . '/JQ.png') }}" class="h-12 mb-4" alt="Justice Quest Logo">
                    <p class="mb-4 text-sm text-gray-400">
                        The ultimate legal fighting game. Use AI-powered moves to defeat injustice and win your legal battles.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-red-500">
                            <i class="fab fa-twitter"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-red-500">
                            <i class="fab fa-facebook"></i>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-red-500">
                            <i class="fab fa-instagram"></i>
                        </a>
                    </div>
                </div>

                <div>
                    <h3 class="mb-4 text-lg font-bold text-white">QUICK LINKS</h3>
                    <ul class="space-y-2">
                        <li><a href="#features" class="text-gray-400 hover:text-red-500">Features</a></li>
                        <li><a href="#how-it-works" class="text-gray-400 hover:text-red-500">How To Play</a></li>
                        <li><button @click="$wire.showLogin()" class="text-gray-400 hover:text-red-500">Login</button></li>
                        <li><button @click="$wire.showRegister()" class="text-gray-400 hover:text-red-500">Register</button></li>
                    </ul>
                </div>

                <div>
                    <h3 class="mb-4 text-lg font-bold text-white">LEGAL</h3>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-gray-400 hover:text-red-500">Terms of Service</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-red-500">Privacy Policy</a></li>
                        <li><a href="#" class="text-gray-400 hover:text-red-500">Cookie Policy</a></li>
                    </ul>
                </div>

                <div>
                    <h3 class="mb-4 text-lg font-bold text-white">CONTACT</h3>
                    <ul class="space-y-2">
                        <li class="flex items-center text-gray-400">
                            <i class="mr-2 fas fa-envelope"></i>
                            <a href="mailto:<EMAIL>" class="hover:text-red-500"><EMAIL></a>
                        </li>
                        <li class="flex items-center text-gray-400">
                            <i class="mr-2 fas fa-phone"></i>
                            <a href="tel:+1234567890" class="hover:text-red-500">+1 (234) 567-890</a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="pt-8 mt-8 text-center border-t border-gray-800">
                <p class="text-sm text-gray-500">© {{ date('Y') }} Justice Quest - Press START to Begin Your Legal Journey</p>
            </div>
        </div>
    </footer>
</div>
