<div class="flex flex-col p-3 space-y-2 rounded-lg shadow-lg bg-base-200 sm:p-4 sm:space-y-3"
     x-data="{
        uploading: false,
        recording: false,
        sending: false,
        mediaRecorder: null,
        audioChunks: [],
        allowedMimeTypes: ['audio/wav', 'audio/mp3', 'audio/mpeg', 'audio/m4a', 'audio/mp4', 'audio/x-m4a', 'audio/webm', 'video/webm', 'audio/ogg'],
        maxFileSize: 25 * 1024 * 1024, // 25MB in bytes
        queuedFiles: [],

        // Add responsive state
        isMobile: window.innerWidth < 640,

        // Initialize responsive detection
        init() {
            this.checkMobile();
            window.addEventListener('resize', () => this.checkMobile());
        },

        checkMobile() {
            this.isMobile = window.innerWidth < 640;
        },

        // File handling methods
        handleFileUpload(event) {
            const files = event.target.files;
            if (!files.length) return;

            Array.from(files).forEach(file => {
                if (!this.validateFile(file)) return;

                this.queuedFiles.push({
                    file: file,
                    name: file.name,
                    size: this.formatFileSize(file.size),
                    type: file.type
                });
            });

            // Reset file input
            event.target.value = '';
        },

        removeQueuedFile(index) {
            this.queuedFiles.splice(index, 1);
        },

        formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            else if (bytes < 1048576) return (bytes / 1024).toFixed(1) + ' KB';
            else return (bytes / 1048576).toFixed(1) + ' MB';
        },

        validateFile(file) {
            // Add your file validation logic here
            // For now, we'll just check size
            if (file.size > this.maxFileSize) {
                alert(`File size (${(file.size / (1024 * 1024)).toFixed(2)}MB) exceeds maximum allowed size of ${this.maxFileSize / (1024 * 1024)}MB`);
                return false;
            }
            return true;
        },

        // Send message and files
        async sendMessage() {
            if (!this.message && this.queuedFiles.length === 0) {
                return; // Nothing to send
            }

            this.sending = true;

            try {
                // Create form data with message and files
                const formData = new FormData();
                formData.append('message', this.message || '');

                // Add all queued files to the form data
                this.queuedFiles.forEach((fileObj, index) => {
                    formData.append(`files[${index}]`, fileObj.file);
                });

                // Call the Livewire method to handle the submission
                await $wire.submit(formData);

                // Clear the queue and message after successful submission
                this.queuedFiles = [];
                this.message = '';
                $wire.$set('message', '');

            } catch (error) {
                console.error('Error sending message:', error);
                alert('Error sending message: ' + (error.message || 'Unknown error'));
            } finally {
                this.sending = false;
            }
        },

        getSupportedMimeType() {
            const types = [
                'audio/webm;codecs=opus',
                'audio/webm',
                'audio/wav',
                'audio/mp3',
                'audio/mpeg',
                'audio/m4a',
                'audio/mp4',
                'audio/x-m4a',
                'audio/ogg'
            ];

            for (const type of types) {
                if (MediaRecorder.isTypeSupported(type)) {
                    return type;
                }
            }
            throw new Error('No supported audio MIME type found in this browser');
        },

        async startRecording() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
                const mimeType = this.getSupportedMimeType();

                this.mediaRecorder = new MediaRecorder(stream, {
                    mimeType: mimeType,
                    audioBitsPerSecond: 128000
                });

                this.audioChunks = [];

                this.mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        this.audioChunks.push(event.data);
                    }
                };

                this.mediaRecorder.onstop = async () => {
                    const audioBlob = new Blob(this.audioChunks, { type: mimeType });
                    await this.uploadAudioBlob(audioBlob);
                    stream.getTracks().forEach(track => track.stop());
                };

                this.mediaRecorder.start(1000); // Collect data every second
                this.recording = true;
            } catch (error) {
                console.error('Recording error:', error);
                alert('Error accessing microphone: ' + (error.message || 'Unknown error occurred'));
            }
        },

        stopRecording() {
            if (this.mediaRecorder && this.recording) {
                this.mediaRecorder.stop();
                this.recording = false;
            }
        },

        async uploadAudioBlob(blob) {
            if (blob.size > this.maxFileSize) {
                alert(`Recording size (${(blob.size / (1024 * 1024)).toFixed(2)}MB) exceeds maximum allowed size of ${this.maxFileSize / (1024 * 1024)}MB`);
                return;
            }

            this.uploading = true;
            const formData = new FormData();
            const extension = blob.type.includes('webm') ? 'webm' : 'wav';
            formData.append('audio', blob, `recording.${extension}`);

            try {
                console.log('Uploading audio blob:', {
                    size: (blob.size / 1024).toFixed(2) + 'KB',
                    type: blob.type,
                    filename: `recording.${extension}`
                });

                const response = await fetch('/transcribe', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content,
                        'Accept': 'application/json'
                    },
                    credentials: 'same-origin'
                });

                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);

                if (data.success) {
                    $wire.appendTranscription(data.transcription);
                    this.uploading = false;
                } else {
                    throw new Error(data.error || 'Unknown error occurred');
                }
            } catch (error) {
                console.error('Error details:', {
                    message: error.message,
                    stack: error.stack,
                    name: error.name
                });
                alert('Error uploading recording: ' + error.message);
            } finally {
                this.uploading = false;
            }
        },

         uploadAudio(event) {
            const file = event.target.files[0];
            if (!file) {
                console.log('No file selected');
                return;
            }

            if (!this.allowedMimeTypes.includes(file.type)) {
                alert('Invalid file type. Please upload an audio file.');
                return;
            }

            if (file.size > this.maxFileSize) {
                alert(`File size (${(file.size / (1024 * 1024)).toFixed(2)}MB) exceeds maximum allowed size of ${this.maxFileSize / (1024 * 1024)}MB`);
                return;
            }

            this.uploading = true;
            const formData = new FormData();
            formData.append('audio', file);

            fetch('/transcribe', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name=csrf-token]').content,
                    'Accept': 'application/json'
                },
                credentials: 'same-origin'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    $wire.appendTranscription(data.transcription);
                    // Update the local message variable to match what's in the textarea
                    this.message = $wire.message;
                } else {
                    throw new Error(data.error || 'Unknown error occurred');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error uploading file: ' + error.message);
            })
            .finally(() => {
                this.uploading = false;
                event.target.value = '';
            });
        }
     }">
    <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* Mobile optimizations */
        @media (max-width: 640px) {
            .button-text {
                display: none;
            }

            .mobile-btn-group {
                display: flex;
                flex-wrap: wrap;
                gap: 0.5rem;
                justify-content: flex-start;
                width: 100%;
            }

            .mobile-btn-group button {
                flex: 0 0 auto;
                padding: 0.5rem;
                min-width: 2.5rem;
            }

            /* Ensure the button container has proper spacing */
            .button-container {
                display: flex;
                flex-direction: column;
                width: 100%;
            }

            /* Make send button full width on mobile */
            .send-button-container {
                margin-top: 0.5rem;
                width: 100%;
            }

            .send-button-container button {
                width: 100%;
            }
        }
    </style>

    {{-- File Preview Area --}}
    <div class="mb-2" x-show="queuedFiles.length > 0">
        <div class="mb-1 text-sm font-medium">{{ __('voice.queued_files') }}</div>
        <div class="flex flex-wrap gap-2">
            <template x-for="(file, index) in queuedFiles" :key="index">
                <div class="inline-flex items-center px-2 py-1 text-xs rounded-full bg-base-300">
                    <span x-text="file.name" class="mr-1 max-w-[150px] truncate"></span>
                    <span x-text="file.size" class="mr-1 text-base-content/60"></span>
                    <button type="button" @click="removeQueuedFile(index)" class="text-error hover:text-error-focus">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </template>
        </div>
    </div>

    {{-- Text Area Container --}}
    <div class="relative" x-data="voiceMessageInput" @voice-message-updated.window="message = $event.detail.message">
        <textarea
            wire:model.live.debounce.300ms="message"
            name="{{ $name }}"
            class="w-full bg-white textarea textarea-bordered text-base-content placeholder-base-content/50"
            style="height: {{ $height }};"
            placeholder="{{ $placeholder ?? __('voice.type_your_message') }}"></textarea>

        {{-- Loading Indicator --}}
        <div x-show="uploading || recording" class="absolute inset-0 flex items-center justify-center rounded-lg bg-base-100/50">
            <div class="flex flex-col items-center">
                <div class="loading loading-spinner loading-lg text-primary"></div>
                <span x-text="recording ? '{{ __('voice.recording') }}' : '{{ __('voice.transcribing') }}'"
                      class="mt-2 text-sm font-medium animate-pulse"
                      style="animation: pulse 1.5s infinite;"></span>
            </div>
        </div>
    </div>

    {{-- Button Group --}}
    <div class="flex items-center justify-between mt-2 button-container">
        {{-- Left side: Action Buttons --}}
        <div class="flex items-center space-x-2 mobile-btn-group">
            {{-- Voice Recorder Button --}}
            <button type="button"
                    @click="recording ? stopRecording() : startRecording()"
                    :class="{'btn-error': recording}"
                    class="text-white btn btn-sm btn-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
                <span class="ml-2 button-text" x-text="recording ? '{{ __('voice.stop_recording') }}' : '{{ __('voice.start_recording') }}'"></span>
            </button>

            {{-- Voice Upload Button --}}
            <input type="file"
                   accept="audio/*,video/webm"
                   class="hidden"
                   id="audio-upload-{{ $name }}"
                   @change="uploadAudio($event)" />

            <button type="button"
                    @click="document.getElementById('audio-upload-{{ $name }}').click()"
                    :disabled="uploading || recording"
                    class="text-white btn btn-sm btn-secondary">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                <span class="ml-2 button-text" x-text="uploading ? '{{ __('voice.transcribing') }}' : '{{ __('voice.voice_note') }}'"></span>
            </button>

            {{-- File Upload Button (Configurable) --}}
            @if($showFileUpload)
                <input type="file"
                       class="hidden"
                       id="file-upload-{{ $name }}"
                       multiple
                       @change="handleFileUpload($event)" />

                <button type="button"
                        @click="document.getElementById('file-upload-{{ $name }}').click()"
                        :disabled="uploading || recording"
                        class="text-white btn btn-sm btn-secondary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span class="ml-2 button-text">{{ __('voice.file_upload') }}</span>
                </button>
            @endif
        </div>

        {{-- Right side: Send Button (Configurable) --}}
        @if($showSendButton)
            <div class="send-button-container">
                <button type="button"
                        wire:click="submit()"
                        :disabled="uploading || recording || sending || (!message && queuedFiles.length === 0)"
                        class="text-white btn btn-sm btn-primary">
                <div class="flex items-center">
                    <span x-show="!sending" class="flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10.894 2.553a1 1 0 00-1.788 0l-7 14a1 1 0 001.169 1.409l5-1.429A1 1 0 009 15.571V11a1 1 0 112 0v4.571a1 1 0 00.725.962l5 1.428a1 1 0 001.17-1.408l-7-14z" />
                        </svg>
                        <span class="ml-2 button-text">{{ $sendButtonText ?? __('voice.send') }}</span>
                    </span>
                    <span x-show="sending" class="flex items-center">
                        <div class="loading loading-spinner loading-xs"></div>
                        <span class="ml-2 button-text">{{ __('voice.sending') }}</span>
                    </span>
                    </div>
                </button>
            </div>
        @endif
    </div>
</div>
