<x-app-layout>
    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-base-100/50 dark:bg-neutral-focus/50 backdrop-blur-xl shadow-xl sm:rounded-lg p-8">
                <div class="mb-6">
                    <h2 class="text-2xl font-bold">{{ __('connect.stripe_connect_account') }}</h2>
                    <p class="mt-2 text-base-content/70">
                        {{ __('connect.setup_description') }}
                    </p>
                </div>

                <div class="card bg-base-200 shadow-md mb-8">
                    <div class="card-body">
                        <h3 class="card-title text-xl">{{ __('connect.account_status') }}</h3>

                        @if (!$connectAccount)
                            <div class="mt-4">
                                <p class="text-base-content/70 mb-4">
                                    {{ __('connect.not_setup_yet') }}
                                </p>
                                <ul class="list-disc list-inside space-y-2 mb-6">
                                    <li>{{ __('connect.benefits.create_invoices') }}</li>
                                    <li>{{ __('connect.benefits.receive_payments') }}</li>
                                    <li>{{ __('connect.benefits.track_earnings') }}</li>
                                </ul>

                                <form action="{{ route('connect.onboarding') }}" method="POST">
                                    @csrf
                                    <button type="submit" class="btn btn-primary">
                                        {{ __('connect.setup_connect_account') }}
                                    </button>
                                </form>
                            </div>
                        @else
                            <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm text-base-content/70">{{ __('connect.account_id') }}</p>
                                    <p class="text-lg font-mono">{{ substr($connectAccount->stripe_account_id, 0, 8) }}...{{ substr($connectAccount->stripe_account_id, -4) }}</p>
                                </div>

                                <div>
                                    <p class="text-sm text-base-content/70">{{ __('connect.status') }}</p>
                                    <p class="text-lg">
                                        @if($connectAccount->charges_enabled && $connectAccount->details_submitted)
                                            <span class="badge badge-success">{{ __('connect.active') }}</span>
                                        @elseif($connectAccount->details_submitted)
                                            <span class="badge badge-warning">{{ __('connect.pending') }}</span>
                                        @else
                                            <span class="badge badge-error">{{ __('connect.incomplete') }}</span>
                                        @endif
                                    </p>
                                </div>

                                <div>
                                    <p class="text-sm text-base-content/70">{{ __('connect.account_type') }}</p>
                                    <p class="text-lg capitalize">{{ $connectAccount->account_type }}</p>
                                </div>

                                <div>
                                    <p class="text-sm text-base-content/70">{{ __('connect.created_on') }}</p>
                                    <p class="text-lg">{{ $connectAccount->created_at->format('M j, Y') }}</p>
                                </div>
                            </div>

                            <div class="divider"></div>

                            <div class="flex flex-col md:flex-row gap-4">
                                @if(!$connectAccount->details_submitted)
                                    <form action="{{ route('connect.onboarding') }}" method="POST">
                                        @csrf
                                        <button type="submit" class="btn btn-primary">
                                            {{ __('connect.complete_account_setup') }}
                                        </button>
                                    </form>
                                @endif

                                @if($connectAccount->details_submitted && !$connectAccount->charges_enabled)
                                    <div class="alert alert-warning">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>
                                        <span>{{ __('connect.verification_message') }}</span>
                                    </div>

                                    @if(isset($remediationUrl) && $remediationUrl)
                                    <div class="mt-4">
                                        <a href="{{ $remediationUrl }}" class="btn btn-warning border">
                                            {{ __('connect.update_account_information') }}
                                        </a>
                                        <p class="text-sm mt-2">{{ __('connect.remediation_link_message') }}</p>
                                    </div>
                                    @endif
                                @endif

                                @if($connectAccount->charges_enabled && $connectAccount->details_submitted)
                                    <a href="{{ route('connect.dashboard') }}" class="btn btn-outline">
                                        {{ __('connect.view_stripe_dashboard') }}
                                    </a>

                                    <a href="{{ route('invoices.create') }}" class="btn btn-primary">
                                        {{ __('connect.create_invoice') }}
                                    </a>
                                @endif
                            </div>
                        @endif
                    </div>
                </div>

                @if($connectAccount && $connectAccount->charges_enabled && $connectAccount->details_submitted)
                    <div class="card bg-base-200 shadow-md">
                        <div class="card-body">
                            <h3 class="card-title text-xl">{{ __('connect.your_invoices') }}</h3>

                            <div class="mt-4">
                                <p class="text-base-content/70 mb-4">
                                    {{ __('connect.invoices_description') }}
                                </p>

                                <div class="flex flex-col md:flex-row gap-4">
                                    <a href="{{ route('invoices.index') }}" class="btn btn-outline">
                                        {{ __('connect.view_sent_invoices') }}
                                    </a>

                                    <a href="{{ route('invoices.received') }}" class="btn btn-outline">
                                        {{ __('connect.view_received_invoices') }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
</x-app-layout>
