<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Completed Documents</title>
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-base-200 dark:bg-neutral-focus">
        @livewire('navigation-menu')

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <h2 class="text-2xl font-semibold mb-6">Completed Documents</h2>
                    
                    <div class="mb-4">
                        <p class="text-gray-600 dark:text-gray-400">External ID: {{ $externalId }}</p>
                    </div>
                    
                    @if(!empty($submitter) && !empty($submitter['documents']))
                        <div class="space-y-4">
                            @foreach($submitter['documents'] as $document)
                                <div class="border rounded-lg p-4 flex justify-between items-center">
                                    <div>
                                        <h3 class="font-medium">{{ $document['filename'] ?? 'Document' }}</h3>
                                        <p class="text-sm text-gray-500">
                                            Completed: {{ \Carbon\Carbon::parse($submitter['completed_at'])->format('M d, Y H:i') }}
                                        </p>
                                    </div>
                                    <div>
                                        <a href="{{ $document['url'] }}" target="_blank" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-focus focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                            Download
                                        </a>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-8">
                            <p class="text-gray-500">No completed documents found.</p>
                        </div>
                    @endif
                    
                    <div class="mt-6">
                        <a href="{{ route('docuseal.templates') }}" class="text-primary hover:underline">
                            &larr; Back to Templates
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
