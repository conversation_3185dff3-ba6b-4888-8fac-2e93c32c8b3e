<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Document Templates</title>
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased">
    <div class="min-h-screen bg-base-200 dark:bg-neutral-focus">
        @livewire('navigation-menu')

        <div class="py-12">
            <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
                <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-xl sm:rounded-lg p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h2 class="text-2xl font-semibold">Document Templates</h2>
                        <a href="{{ route('docuseal.create') }}" class="px-4 py-2 bg-primary text-white rounded-md">Create Document</a>
                    </div>

                    @if(count($templates) > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full bg-white dark:bg-gray-800">
                                <thead>
                                    <tr>
                                        <th class="py-3 px-4 border-b text-left">ID</th>
                                        <th class="py-3 px-4 border-b text-left">Name</th>
                                        <th class="py-3 px-4 border-b text-left">Created</th>
                                        <th class="py-3 px-4 border-b text-left">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($templates as $template)
                                        <tr>
                                            <td class="py-3 px-4 border-b">{{ $template['id'] ?? $template->id ?? 'N/A' }}</td>
                                            <td class="py-3 px-4 border-b">{{ $template['name'] ?? $template->name ?? 'Unnamed Template' }}</td>
                                            <td class="py-3 px-4 border-b">
                                                @if(isset($template['created_at']) || isset($template->created_at))
                                                    {{ \Carbon\Carbon::parse($template['created_at'] ?? $template->created_at)->format('M d, Y') }}
                                                @else
                                                    N/A
                                                @endif
                                            </td>
                                            <td class="py-3 px-4 border-b">
                                                <a href="{{ route('docuseal.create') }}?template_id={{ $template['id'] ?? $template->id ?? '' }}" class="text-primary hover:underline">Use Template</a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-8">
                            <p class="text-gray-500">No templates found.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</body>
</html>
