**Existing Features:**

**I. Core Case Management:**

* **Case Files:** The fundamental organizational unit for all case-related information.
* **Address Book (Party Directory/Rolodex):** Manages contact information for parties involved in cases and correspondence.
* **Document Uploader (to be renamed Exhibit Uploader):** Allows users to upload various file types (documents, images, audio, video) related to a case.
    * **AI Document Analysis:** Automatically titles and summarizes uploaded documents.
    * **Vector Store Integration:** Stores text-based document content for the AI assistant.
* **Correspondence:** Tracks communication threads between individuals or entities, allowing for labeling and organization.
* **Docket Management:** Enables users to track their own case dockets and attach relevant documents.

**II. AI-Powered Assistance:**

* **AI Assistant:** An OpenAI Assistant instance attached to each case.
* **Vector Store:** A knowledge base linked to the AI assistant, containing case-specific information (primarily from uploaded documents).
* **Case Interview (Intake Session - In Progress):** Gathers information from the user to understand the case details, jurisdiction, and relevant laws.
* **Voice Message Input:** Allows users to record or upload voice notes, which are then transcribed using OpenAI's Whisper AI and added to a text area for editing.

**III. Collaboration & Communication:**

* **Case Collaboration:** Enables other individuals to join a case and view associated files.
* **Notifications:** Currently used to inform users about case collaboration invitations and their acceptance status.

**IV. Platform Utilities & Settings:**

* **Themes:** Supports multiple themes from Daisy UI for UI customization.
* **Multilingual Support:** Currently implemented for English and Spanish, with a framework for adding more languages.
* **API Token Manager:** An internal feature for managing project API keys, primarily for load balancing data in the vector store as the platform scales.

**Upcoming Features:**

**I. Enhanced Case Intake & Analysis:**

* **Improved Case Interview Workflow:** Refining the user experience, potentially involving uploading exhibits earlier in the process to provide AI with context during the case overview.
* **AI-Driven Questioning:** The AI assistant will analyze the initial case information and ask clarifying questions to gain deeper insights.

**II. Legal Research Capabilities:**

* **Case Research Process:** Implementing features to conduct legal research.
    * **Integration with Free APIs (e.g., CourtListener).**
    * **Potential Integration with Scraping Tools (e.g., Stagehand - under consideration).**
    * **Integration with GPT Researcher:** Utilizing this tool for in-depth legal research on specific questions.

**III. Case Strategy & Planning:**

* **Case Strategization System:** Developing a system that uses multiple LLM iterations to analyze case information and suggest the best course of action (e.g., discovery, motions, subpoenas).

**IV. Document Generation:**

* **Drafting Tool:** A feature to generate legal documents based on the information gathered and the determined case strategy.

**Categorization Strategy:**

I've tried to categorize these features based on their primary function within the application. This should provide a good starting point for your documentation. You might consider further sub-categorization within these broader categories as you delve into the details of each feature.
