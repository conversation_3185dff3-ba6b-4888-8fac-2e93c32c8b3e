# Technical Context

## Technologies
- PHP 8.2 (as seen in Dockerfile)
- Laravel framework
- Livewire for server-side rendered components
- Alpine.js for frontend interactivity
- MySQL/PostgreSQL database (using database migrations)
- Redis/Database for cache (configurable)
- S3-compatible storage (Linode Object Storage)
- OpenAI API integration
- FFmpeg for audio processing

## Development Setup
- Docker-based development environment
- PHP 8.2 FPM Alpine as base image
- Node.js and NPM for frontend asset compilation
- Composer for PHP dependency management

## Dependencies
- OpenAI API for AI assistance
- FFmpeg for audio file conversion
- AWS/S3 SDK for object storage
- <PERSON><PERSON> Jetstream for authentication and team management