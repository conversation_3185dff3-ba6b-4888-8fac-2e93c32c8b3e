# Project Brief

## Overview
Justice Quest is an AI-powered "virtual law firm" platform designed to help pro se litigants manage their legal cases. Built on Laravel, the application combines:
- Comprehensive case file management with collaboration features
- Document management with AI-powered analysis and summarization
- OpenAI integration for case assistance through dedicated AI assistants
- Structured interview/form components for data collection
- Correspondence tracking and management system
- Address book with contact management
- Multilingual support (English and Spanish)
- Theme customization (light/dark mode)
- Secure file storage using Linode Object Storage
- Authentication and user management via Laravel Jetstream

## Core Requirements
- Secure case file management with collaboration features
- EXHIBIT management with document processing pipeline
- AI assistance for case analysis using GPT-4 and Assistants API
- Structured interview data collection
- Comprehensive correspondence system
- Address book with contact management
- Multilingual support (initially English and Spanish)
- Theme customization
- File transcription and processing
- Vector-based semantic search capabilities
- OpenAI project key management for load balancing
