# AI Document Editor with TipTap Integration

This document explains the architecture and functionality of the AI Document Editor with TipTap integration, providing a rich text editing experience with markdown storage.

## Overview

The AI Document Editor is a Livewire component that allows users to create and edit documents with AI assistance. It combines:

1. **Rich Text Editing**: Using TipTap, a headless editor framework based on ProseMirror
2. **AI Assistance**: Integrated chat interface for generating content
3. **Markdown Storage**: Content is stored as markdown for portability and simplicity

## Component Structure

### Main Components

1. **AiDocumentEditor.php** (`app/Livewire/Drafts/AiDocumentEditor.php`)
   - Main Livewire component that handles document editing logic
   - Manages sections, content, and AI interactions

2. **ai-document-editor.blade.php** (`resources/views/livewire/drafts/ai-document-editor.blade.php`)
   - Main view template for the document editor
   - Contains the layout for document sections, editor, and AI chat

3. **DocumentEditor.php** (`app/Livewire/DocumentEditor.php`)
   - Livewire component for the TipTap editor integration
   - Handles content updates and editor initialization

4. **document-editor.blade.php** (`resources/views/livewire/document-editor.blade.php`)
   - View template for the TipTap editor
   - Contains the editor toolbar and content area

5. **document-editor.js** (`resources/js/document-editor.js`)
   - JavaScript for initializing and configuring the TipTap editor
   - Handles markdown conversion and editor extensions

## TipTap Editor Integration

### Initialization

The TipTap editor is initialized in `document-editor.js`:

```javascript
window.initDocumentEditor = function(elementId, content, updateCallback) {
    return new Editor({
        element: document.getElementById(elementId),
        extensions: [
            StarterKit,
            TextAlign.configure({
                types: ['heading', 'paragraph'],
            }),
            TextStyle,
            Underline,
            Table.configure({
                resizable: true,
            }),
            TableRow,
            TableCell,
            TableHeader,
            FontFamily
        ],
        content: content,
        onUpdate: ({ editor }) => {
            // Get the HTML content and convert it to markdown
            const html = editor.getHTML();
            const markdown = htmlToMarkdown(html);
            // Call the update callback with the new content
            updateCallback(markdown);
        },
        editorProps: {
            attributes: {
                class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-xl focus:outline-none min-h-[500px] p-4',
            },
        },
    });
};
```

### Alpine.js Integration

The TipTap editor is integrated with Alpine.js in `document-editor.blade.php`:

```html
<div
    wire:ignore
    x-data="{
        editor: null,
        content: @entangle('content').defer,
        // Helper methods for editor commands
        toggleBold() {
            try { if (this.editor) this.editor.chain().focus().toggleBold().run(); } 
            catch (e) { console.error('Error toggling bold:', e); }
        },
        // ... other helper methods ...
        init() {
        alert(1243)
            // Initialize the editor
            this.$nextTick(() => {
                try {
                    if (typeof window.initDocumentEditor === 'function') {
                        this.editor = window.initDocumentEditor(
                            '{{ $editorId }}',
                            this.content,
                            (newContent) => {
                                try {
                                    this.content = newContent;
                                    @this.updateContent(newContent);
                                } catch (err) {
                                    console.error('Error updating content:', err);
                                }
                            }
                        );
                    }
                } catch (err) {
                    console.error('Error initializing editor:', err);
                }
            });
        }
    }"
    x-init="init"
    x-on:destroy="destroy"
>
    <!-- Editor toolbar and content -->
</div>
```

### Markdown Conversion

HTML content from TipTap is converted to markdown using a custom function:

```javascript
function htmlToMarkdown(html) {
    // Simple markdown conversion for common elements
    let markdown = html
        // Headers
        .replace(/<h1>(.*?)<\/h1>/g, '# $1\n')
        .replace(/<h2>(.*?)<\/h2>/g, '## $1\n')
        // ... other conversions ...
        .trim();
    
    return markdown;
}
```

## AI Integration

### Chat Interface

The AI chat interface allows users to interact with an AI assistant:

```html
<form wire:submit.prevent="sendAiMessage">
    <div class="flex items-end gap-2">
        <div class="flex-1">
            <textarea
                wire:model="aiMessage"
                class="w-full textarea textarea-bordered"
                placeholder="Ask me to help with your document..."
                rows="3"
                wire:loading.attr="disabled"
                wire:target="sendAiMessage"
                @keydown.enter.prevent="$event.shiftKey || $wire.sendAiMessage()"
            ></textarea>
        </div>
        <button
            type="submit"
            class="btn btn-primary"
            wire:loading.attr="disabled"
            wire:target="sendAiMessage"
        >
            <!-- Send button content -->
        </button>
    </div>
</form>
```

### AI Content Generation

The AI can generate content for document sections:

```php
public function sendAiMessage()
{
    // Validate and process the message
    if (empty($this->aiMessage)) {
        return;
    }

    // Add user message to chat
    $this->messages[] = [
        'role' => 'user',
        'content' => $this->aiMessage
    ];

    // Clear input
    $userMessage = $this->aiMessage;
    $this->aiMessage = '';
    $this->isWaitingForResponse = true;

    // Process with AI and get response
    // ...
}
```

### Text Insertion

AI-generated content can be inserted into the document:

```javascript
window.addEventListener('insertTextAtCursor', event => {
    const text = event.detail.text;
    const editorId = 'section-editor-' + @this.activeSectionId;
    
    // Find the editor instance
    const editorElement = document.getElementById(editorId);
    if (!editorElement) return;
    
    // Find the Alpine component that contains the editor
    const alpineComponent = editorElement.closest('[x-data]');
    if (!alpineComponent) return;
    
    // Get the Alpine.js component instance
    const alpine = window.Alpine.getComponent(alpineComponent);
    if (!alpine || !alpine.editor) {
        console.error('Editor not found in Alpine component');
        return;
    }
    
    try {
        // Insert the text at the current cursor position
        alpine.editor.commands.insertContent(text);
        
        // Focus the editor
        alpine.editor.commands.focus();
        
        // Update the Livewire property with the new content
        setTimeout(() => {
            try {
                const html = alpine.editor.getHTML();
                // Use our htmlToMarkdown function
                if (typeof htmlToMarkdown === 'function') {
                    const markdown = htmlToMarkdown(html);
                    @this.set('activeSectionContent', markdown);
                    @this.updateSectionContent();
                }
            } catch (err) {
                console.error('Error updating content:', err);
            }
        }, 100);
    } catch (err) {
        console.error('Error inserting content:', err);
    }
});
```

## Document Structure

### Sections

The document is divided into sections, each with its own editor:

```php
public $sections = [];
public $activeSectionId = null;
public $activeSectionContent = '';

public function mount($draftId = null)
{
    // Initialize document sections
    if ($draftId) {
        $this->draft = Draft::findOrFail($draftId);
        $this->sections = $this->draft->sections ?? [];
    } else {
        // Create a new draft with default sections
        $this->sections = [
            [
                'id' => Str::uuid(),
                'title' => 'Introduction',
                'content' => '',
                'order' => 0
            ],
            // ... other default sections ...
        ];
    }

    // Set the active section to the first one
    if (!empty($this->sections)) {
        $this->activeSectionId = $this->sections[0]['id'];
        $this->activeSectionContent = $this->sections[0]['content'];
    }
}
```

### Section Management

Users can add, edit, and delete sections:

```php
public function addSection($afterSectionId = null)
{
    // Create a new section
    $newSection = [
        'id' => Str::uuid(),
        'title' => 'New Section',
        'content' => '',
        'order' => 0
    ];

    // Insert at the correct position
    if ($afterSectionId) {
        // Find the index of the section to insert after
        $index = collect($this->sections)->search(function ($section) use ($afterSectionId) {
            return $section['id'] === $afterSectionId;
        });

        if ($index !== false) {
            // Insert after the specified section
            array_splice($this->sections, $index + 1, 0, [$newSection]);
            // Update order values
            $this->updateSectionOrders();
        }
    } else {
        // Add to the end
        $this->sections[] = $newSection;
        $this->updateSectionOrders();
    }

    // Set the new section as active
    $this->activeSectionId = $newSection['id'];
    $this->activeSectionContent = '';
}
```

## Benefits of This Architecture

1. **Separation of Concerns**:
   - Livewire components handle server-side logic
   - TipTap handles rich text editing
   - Alpine.js provides reactivity and DOM manipulation

2. **Rich Editing Experience**:
   - WYSIWYG interface with formatting toolbar
   - Support for headings, lists, tables, and more
   - Real-time content updates

3. **Markdown Storage**:
   - Content is stored as markdown for portability
   - No need for complex HTML storage or sanitization
   - Easy to export or migrate content

4. **AI Integration**:
   - Seamless integration with AI for content generation
   - Context-aware assistance based on document content
   - Direct insertion of AI-generated content into the editor

## Conclusion

The AI Document Editor with TipTap integration provides a powerful and user-friendly way to create and edit documents with AI assistance. The combination of rich text editing with markdown storage offers the best of both worlds - a great user experience and simple, portable content storage.
