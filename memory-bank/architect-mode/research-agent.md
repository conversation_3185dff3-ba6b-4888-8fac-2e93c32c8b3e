# Justice Quest Integration

This document describes the integration between GPT Researcher and Justice Quest for legal research.

## Overview

The integration allows Justice Quest to send legal research requests to GPT Researcher, which will process them and upload the results to a specified Linode Object Storage path. Justice Quest can then check the status of the research and retrieve the results from the provided public URL.

## Architecture

The integration consists of the following components:

1. **Legal Research API**: A set of REST endpoints for Justice Quest to interact with GPT Researcher
2. **Redis Queue**: A task queue for storing and processing legal research requests
3. **Legal Research Worker**: A background process that processes tasks from the queue
4. **Legal Researcher**: A specialized version of GPT Researcher for legal research
5. **S3 Uploader**: A component for uploading research reports to Linode Object Storage

## API Endpoints

### Create Legal Research Request

```
POST /api/legal-research/
```

Request body:

```json
{
  "research_question": "What is the legal precedent for...",
  "case_id": "case-123",
  "research_item_id": "research-456"
}
```

Response:

```json
{
  "research_id": "uuid-789",
  "status": "QUEUED",
  "message": "Legal research request has been queued"
}
```

### Get Research Status

```
GET /api/legal-research/status/{research_id}
```

Response:

```json
{
  "research_id": "uuid-789",
  "status": "COMPLETED",
  "message": "Research has been completed",
  "public_report_url": "https://justicequest.us-mia-1.linodeobjects.com/path/to/report.docx",
  "error": null
}
```

### List Research Requests

```
GET /api/legal-research/
```

Response:

```json
[
  {
    "research_id": "uuid-789",
    "status": "COMPLETED",
    "message": "Research has been completed",
    "s3_report_url": "s3://justicequest/path/to/report.docx",
    "public_report_url": "https://justicequest.us-mia-1.linodeobjects.com/path/to/report.docx",
    "error": null
  },
  {
    "research_id": "uuid-790",
    "status": "IN_PROGRESS",
    "message": "Research is in progress",
    "s3_report_url": null,
    "error": null
  }
]
```

### Retry Failed Research

```
POST /api/legal-research/retry/{research_id}
```

Response:

```json
{
  "research_id": "uuid-789",
  "status": "QUEUED",
  "message": "Legal research request has been requeued for retry",
  "s3_report_url": null,
  "error": null
}
```

## Setup

### Environment Variables

The following environment variables are required:

```
# Redis configuration
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=null

# Linode Object Storage configuration
AWS_ACCESS_KEY_ID=JAY50EK6QV5TPE2LVTEC
AWS_SECRET_ACCESS_KEY=wiRag5fIChIsqIt6A26plnMtDTEhOZ9BK92Q6ZYU
AWS_REGION=us-mia-1
AWS_DEFAULT_REGION=us-mia-1
AWS_BUCKET=justicequest
AWS_URL=https://justicequest.us-mia-1.linodeobjects.com
AWS_ENDPOINT_URL=https://us-mia-1.linodeobjects.com
AWS_USE_PATH_STYLE_ENDPOINT=true
```

### Running the Worker

The legal research worker must be running to process tasks from the queue. Start it with:

```bash
./start_legal_worker.sh
```

## Integration Flow

1. Justice Quest sends a legal research request to the API
2. GPT Researcher queues the request and returns a research ID
3. The legal research worker picks up the request from the queue
4. The worker processes the request using the specialized legal researcher
5. The worker uploads the research report to the specified S3 path
6. Justice Quest checks the status of the research using the research ID
7. When the research is complete, Justice Quest retrieves the report from the provided public URL
