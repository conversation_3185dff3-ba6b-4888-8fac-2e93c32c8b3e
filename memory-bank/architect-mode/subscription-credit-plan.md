# Justice Quest - Subscription & Credit Management Technical Plan

**Current Date:** June 1, 2024

## Overview

This document outlines the technical implementation plan for Justice Quest's subscription and credit management system. We'll use Laravel Cashier with Stripe for subscription management and build a custom credit system on top of it.

## System Components

### 1. Subscription Management (Laravel Cashier + Stripe)
- Handle recurring billing for different subscription tiers
- Process payments securely through Stripe
- Manage subscription lifecycle (creation, upgrades/downgrades, cancellations)
- Track subscription status and plan information

### 2. Credit Management System (Custom Implementation)
- Track user credit balances
- Record credit transactions (additions, deductions)
- Enforce credit limits based on usage
- Provide analytics on credit consumption

### 3. Integration Layer
- Connect subscription events to credit allocation
- Handle credit top-ups through one-time payments
- Provide a unified API for the frontend

## Database Schema

### Subscription Tables (Provided by Laravel Cashier)
- `subscriptions`
- `subscription_items`

### Custom Credit Tables
```
credits_transactions
- id (bigint, primary key)
- user_id (bigint, foreign key)
- amount (integer) // Positive for additions, negative for deductions
- balance_after (integer) // Balance after this transaction
- description (string)
- metadata (json) // For storing additional context
- created_at (timestamp)
- updated_at (timestamp)

user_credits
- id (bigint, primary key)
- user_id (bigint, foreign key, unique)
- balance (integer)
- lifetime_earned (integer) // Total credits ever earned
- lifetime_spent (integer) // Total credits ever spent
- created_at (timestamp)
- updated_at (timestamp)

credit_products
- id (bigint, primary key)
- name (string)
- description (string)
- credits (integer)
- price_cents (integer)
- stripe_price_id (string)
- is_active (boolean)
- created_at (timestamp)
- updated_at (timestamp)
```

## Service Layer

### SubscriptionService
Responsibilities:
- Create and manage subscriptions
- Handle plan changes
- Process subscription cancellations
- Trigger credit allocations on subscription events

### CreditService
Responsibilities:
- Add credits to user accounts
- Deduct credits for feature usage
- Check credit balances before operations
- Generate credit transaction history
- Handle credit expiration (if applicable)

### BillingService
Responsibilities:
- Process one-time payments for credit top-ups
- Generate invoices and receipts
- Handle payment failures

## Implementation Plan

### Phase 1: Core Infrastructure (Week 1)

1. **Set up Stripe Integration**
   - Create Stripe account and configure API keys
   - Set up webhook endpoints
   - Configure Stripe products and prices for subscription plans

2. **Install and Configure Laravel Cashier**
   - `composer require laravel/cashier`
   - Run migrations
   - Configure Cashier in service provider

3. **Create Database Migrations**
   - Create migrations for custom credit tables
   - Run migrations

4. **Implement Core Services**
   - Create CreditService class
   - Create basic subscription handling

### Phase 2: Subscription Management (Week 2)

1. **Implement Subscription Controller**
   - Create/update subscription
   - Cancel subscription
   - Change plans

2. **Create Subscription Views**
   - Subscription selection page
   - Payment form
   - Subscription management dashboard

3. **Set Up Webhook Handling**
   - Handle subscription created/updated/cancelled events
   - Handle payment succeeded/failed events
   - Connect to credit allocation

### Phase 3: Credit System (Week 3)

1. **Implement Credit Management**
   - Credit balance tracking
   - Credit transaction recording
   - Credit usage authorization

2. **Create Credit Top-up System**
   - One-time payment processing
   - Credit package selection
   - Receipt generation

3. **Implement Credit Usage in Features**
   - Document processing credit deduction
   - AI chat credit deduction
   - Research report credit deduction

### Phase 4: User Interface & Testing (Week 4)

1. **Create User Dashboard**
   - Credit balance display
   - Transaction history
   - Subscription status

2. **Implement Admin Interface**
   - Credit management
   - Subscription overview
   - User credit adjustments

3. **Testing**
   - Unit tests for credit service
   - Integration tests for subscription flows
   - End-to-end testing of payment processing

## Code Examples

### Credit Service Implementation

```php
<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserCredit;
use App\Models\CreditTransaction;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CreditService
{
    /**
     * Add credits to a user's account
     *
     * @param User $user
     * @param int $amount
     * @param string $description
     * @param array $metadata
     * @return CreditTransaction
     */
    public function addCredits(User $user, int $amount, string $description, array $metadata = []): CreditTransaction
    {
        return DB::transaction(function () use ($user, $amount, $description, $metadata) {
            // Get or create user credit record
            $userCredit = UserCredit::firstOrCreate(
                ['user_id' => $user->id],
                ['balance' => 0, 'lifetime_earned' => 0, 'lifetime_spent' => 0]
            );
            
            // Update balance
            $userCredit->balance += $amount;
            $userCredit->lifetime_earned += $amount;
            $userCredit->save();
            
            // Record transaction
            return CreditTransaction::create([
                'user_id' => $user->id,
                'amount' => $amount,
                'balance_after' => $userCredit->balance,
                'description' => $description,
                'metadata' => $metadata
            ]);
        });
    }
    
    /**
     * Deduct credits from a user's account
     *
     * @param User $user
     * @param int $amount
     * @param string $description
     * @param array $metadata
     * @return CreditTransaction|false
     */
    public function deductCredits(User $user, int $amount, string $description, array $metadata = [])
    {
        return DB::transaction(function () use ($user, $amount, $description, $metadata) {
            // Get user credit record
            $userCredit = UserCredit::where('user_id', $user->id)->first();
            
            // Check if user has enough credits
            if (!$userCredit || $userCredit->balance < $amount) {
                Log::info('Insufficient credits', [
                    'user_id' => $user->id,
                    'required' => $amount,
                    'available' => $userCredit ? $userCredit->balance : 0
                ]);
                return false;
            }
            
            // Update balance
            $userCredit->balance -= $amount;
            $userCredit->lifetime_spent += $amount;
            $userCredit->save();
            
            // Record transaction
            return CreditTransaction::create([
                'user_id' => $user->id,
                'amount' => -$amount,
                'balance_after' => $userCredit->balance,
                'description' => $description,
                'metadata' => $metadata
            ]);
        });
    }
    
    /**
     * Check if user has enough credits for an operation
     *
     * @param User $user
     * @param int $amount
     * @return bool
     */
    public function hasEnoughCredits(User $user, int $amount): bool
    {
        $userCredit = UserCredit::where('user_id', $user->id)->first();
        return $userCredit && $userCredit->balance >= $amount;
    }
    
    /**
     * Get user's current credit balance
     *
     * @param User $user
     * @return int
     */
    public function getBalance(User $user): int
    {
        $userCredit = UserCredit::where('user_id', $user->id)->first();
        return $userCredit ? $userCredit->balance : 0;
    }
    
    /**
     * Get user's credit transaction history
     *
     * @param User $user
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getTransactionHistory(User $user, int $limit = 20)
    {
        return CreditTransaction::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }
}
```

### Subscription Webhook Handler

```php
<?php

namespace App\Http\Controllers;

use Laravel\Cashier\Http\Controllers\WebhookController as CashierController;
use App\Services\CreditService;
use App\Models\User;

class StripeWebhookController extends CashierController
{
    /**
     * Handle invoice payment succeeded.
     *
     * @param array $payload
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleInvoicePaymentSucceeded(array $payload)
    {
        $subscription = $this->getSubscriptionByStripeId(
            $payload['data']['object']['subscription'] ?? null
        );
        
        if ($subscription) {
            $user = $subscription->user;
            $creditService = app(CreditService::class);
            
            // Determine credits based on plan
            $planCredits = $this->getPlanCredits($subscription->stripe_price);
            
            if ($planCredits > 0) {
                $creditService->addCredits(
                    $user,
                    $planCredits,
                    'Monthly subscription credits',
                    ['subscription_id' => $subscription->id]
                );
            }
        }
        
        return $this->successMethod();
    }
    
    /**
     * Get the number of credits for a subscription plan
     *
     * @param string $stripePriceId
     * @return int
     */
    protected function getPlanCredits(string $stripePriceId): int
    {
        $creditsByPlan = [
            'price_pro_se_basic' => 2000,
            'price_pro_se_standard' => 6000,
            'price_pro_se_plus' => 25000,
        ];
        
        return $creditsByPlan[$stripePriceId] ?? 0;
    }
}
```

## Frontend Components

1. **Subscription Selection Component**
   - Display available plans
   - Show features and credit allocations
   - Handle plan selection

2. **Payment Form Component**
   - Collect payment method details
   - Process subscription creation
   - Handle payment errors

3. **Credit Balance Component**
   - Display current credit balance
   - Show recent transactions
   - Provide top-up options

4. **Credit Usage Confirmation Component**
   - Display credit cost before action
   - Confirm user wants to proceed
   - Show insufficient credit warning

## Monitoring and Analytics

1. **Credit Usage Metrics**
   - Track credit consumption by feature
   - Monitor user credit balances
   - Identify high-usage patterns

2. **Subscription Metrics**
   - Track conversion rates
   - Monitor churn and retention
   - Analyze plan popularity

3. **Revenue Metrics**
   - Track MRR (Monthly Recurring Revenue)
   - Monitor one-time credit purchases
   - Analyze revenue by user segment

## Security Considerations

1. **Payment Security**
   - Use Stripe Elements for secure payment collection
   - Never store credit card details
   - Implement proper authentication for billing actions

2. **Credit System Security**
   - Validate all credit operations server-side
   - Log all credit transactions
   - Implement rate limiting for credit operations

3. **Webhook Security**
   - Verify webhook signatures
   - Implement idempotency for webhook processing
   - Log all webhook events

## Testing Strategy

1. **Unit Tests**
   - Test credit service methods
   - Test subscription service methods
   - Test webhook handlers

2. **Integration Tests**
   - Test subscription creation flow
   - Test credit deduction flow
   - Test plan change flow

3. **End-to-End Tests**
   - Test complete subscription process
   - Test credit top-up process
   - Test feature usage with credit deduction

## Deployment Plan

1. **Development Environment**
   - Set up Stripe test keys
   - Configure webhook forwarding (using Stripe CLI)
   - Implement and test all components

2. **Staging Environment**
   - Use Stripe test mode
   - Perform full integration testing
   - Validate all subscription and credit flows

3. **Production Environment**
   - Switch to Stripe live keys
   - Configure production webhooks
   - Monitor initial transactions closely

## Maintenance Considerations

1. **Regular Audits**
   - Reconcile credit balances
   - Verify subscription statuses
   - Check for failed webhooks

2. **Backup Procedures**
   - Regular database backups
   - Stripe event log backups
   - Transaction log backups

3. **Scaling Considerations**
   - Database indexing for credit tables
   - Caching strategies for credit balances
   - Queue processing for webhook handlers
