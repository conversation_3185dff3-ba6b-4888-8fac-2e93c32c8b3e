# System Patterns

## Architecture
- Laravel-based MVC architecture
- Livewire components for reactive UI elements
- Alpine.js for frontend interactivity
- Observer pattern for model events (e.g., CaseFileObserver)
- Queue-based job processing (e.g., ProcessTranscriptionJob)

## Key Components
- Case management system
- Structured interview component
- OpenAI integration via CaseAssistantService
- File storage using S3-compatible Linode Object Storage
- Authentication via Laravel Jetstream
- Theme management using Alpine.js store