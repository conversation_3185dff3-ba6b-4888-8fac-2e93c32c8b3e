<?php

return [
    'title' => 'Case Strategy',
    'strategy_development' => 'Strategy Development',
    'generate_strategy' => 'Generate Strategy',
    'generating' => 'Generating strategy...',
    'generation_error' => 'Error generating strategy:',
    'guided_mode' => 'Guided Mode',
    'conversation_mode' => 'Conversation Mode',
    
    'tabs' => [
        'overview' => 'Overview',
        'strategy' => 'Strategy',
        'legal' => 'Legal Analysis',
        'actions' => 'Action Items',
        'notes' => 'Notes',
    ],
    
    'fields' => [
        'executive_summary' => 'Executive Summary',
        'executive_summary_placeholder' => 'A brief overview of the case and recommended strategy',
        'strategy_recommendations' => 'Strategy Recommendations',
        'strategy_recommendations_placeholder' => 'Detailed strategic approaches for this case',
        'legal_analysis' => 'Legal Analysis',
        'legal_analysis_placeholder' => 'Analysis of key legal issues and potential arguments',
        'action_items' => 'Action Items',
        'action_item_placeholder' => 'Enter an action item',
        'add_action_item' => 'Add Action Item',
        'no_action_items' => 'No action items yet. Add some using the button below.',
        'notes' => 'Notes',
        'notes_placeholder' => 'Additional notes about the strategy',
    ],
    
    'conversation' => [
        'title' => 'Strategy Conversation',
        'subtitle' => 'Discuss your case strategy with the AI assistant',
        'start_prompt' => 'Start a conversation to develop your case strategy',
        'tip' => 'Ask questions about your case, discuss legal strategies, or request specific advice',
        'message_placeholder' => 'Type your message here...',
        'save_strategy' => 'Save as Strategy',
        'tip_line_1' => 'Discuss your case with the AI to develop a comprehensive strategy',
        'tip_line_2' => 'When ready, click "Save as Strategy" to convert the conversation into a structured strategy document',
    ],
    
    'notifications' => [
        'saved' => 'Strategy saved successfully',
        'generation_failed' => 'Failed to generate strategy:',
        'message_failed' => 'Failed to send message:',
        'no_conversation' => 'No conversation to save. Please start a conversation first.',
        'conversation_saved' => 'Conversation saved as strategy successfully',
        'conversation_save_failed' => 'Failed to save conversation as strategy:',
    ],
];
