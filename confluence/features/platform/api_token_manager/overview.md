# API Token Manager

## Rationale
The API Token Manager was developed to address a critical limitation in OpenAI's Projects API: each project has a 100GB storage limit for documents used in Assistants and their associated vector stores. For a legal research platform handling extensive case documentation, this limitation necessitates a solution that can:

- Manage multiple project keys as storage limits approach capacity
- Seamlessly distribute requests across available projects
- Monitor storage usage across projects
- Automatically provision new projects when existing ones near capacity
- Maintain continuous service availability despite storage constraints

This approach ensures our platform can scale beyond the 100GB per-project limitation while maintaining optimal performance and reliability.

## Overview
The API Token Manager is a critical platform component that handles OpenAI API key management, load balancing, and usage tracking. It ensures efficient distribution of API requests across multiple project keys while maintaining rate limits and monitoring usage patterns.

## Core Features

### Project Key Management
- Secure storage of OpenAI project API keys
- Key rotation and lifecycle management
- Automatic key validation and health checks
- Integration with OpenAI Projects API

### Load Balancing
- Round-robin distribution across available keys
- Rate limit-aware assignment
- Automatic failover for exhausted keys
- Dynamic adjustment based on usage patterns

### Usage Tracking
- Real-time monitoring of API consumption
- Per-project usage metrics
- Vector store data usage tracking
- Rate limit monitoring and alerts

## Technical Implementation

### API Integration
```php
Endpoints:
- /v1/organization/projects/{project_id}/api_keys
- /v1/organization/projects/{project_id}/rate_limits
- /v1/organization/projects
```

### Rate Limit Management
```php
Tracked Limits:
- Requests per minute
- Tokens per minute
- Images per minute
- Audio processing limits
- Daily request quotas
```

### Key Assignment Strategy
```php
Assignment Factors:
- Current key usage
- Rate limit headroom
- Project-specific requirements
- Historical performance
```

## Architecture

### Components
1. Key Management Service
   - Key storage and retrieval
   - Encryption handling
   - Access control

2. Load Balancer
   - Request distribution
   - Health monitoring
   - Failover handling

3. Usage Monitor
   - Metric collection
   - Rate tracking
   - Alert generation

### Data Flow
```mermaid
graph TD
    A[API Request] --> B[Load Balancer]
    B --> C[Key Selection]
    C --> D[Rate Check]
    D --> E[Request Execution]
    E --> F[Usage Tracking]
```

## Security

### Key Storage
- Encrypted at rest
- Access control enforcement
- Audit logging
- Secure key rotation

### Access Control
- Role-based permissions
- Project-level isolation
- Audit trail maintenance
- Key usage tracking

## Monitoring

### Metrics
```php
Tracked Data:
- Request volume
- Token usage
- Error rates
- Response times
- Cost allocation
```

### Alerts
```php
Alert Conditions:
- Rate limit approaching
- Key errors
- Usage spikes
- Cost thresholds
```

## Integration Points

### Internal Systems
- AI Assistant Service
- Vector Store
- Document Processing
- Usage Analytics

### External Services
- OpenAI API
- Project Management
- Billing Systems
- Monitoring Tools

## Error Handling

### Scenarios
1. Rate Limit Exceeded
   - Automatic key switching
   - Request queuing
   - Alert generation

2. Key Failures
   - Automatic failover
   - Health check triggers
   - Administrator notification

3. Service Disruptions
   - Fallback mechanisms
   - Recovery procedures
   - Status reporting

## Configuration

### Project Settings
```yaml
Settings:
- Rate limits
- Key preferences
- Usage thresholds
- Alert configurations
```

### System Parameters
```yaml
Parameters:
- Rotation intervals
- Health check frequency
- Retry policies
- Buffer thresholds
```

## Performance Optimization

### Caching
- Rate limit status
- Key health information
- Usage statistics
- Configuration data

### Request Optimization
- Request batching
- Connection pooling
- Response caching
- Error recovery

## Future Enhancements

### Planned Features
1. Advanced Load Balancing
   - ML-based key selection
   - Predictive scaling
   - Cost optimization

2. Enhanced Monitoring
   - Real-time dashboards
   - Detailed analytics
   - Cost forecasting

3. Automation
   - Automatic key rotation
   - Self-healing capabilities
   - Dynamic rate adjustment

## Dependencies

### Required Services
- OpenAI API
- Secure Key Storage
- Monitoring System
- Alert Management

### Related Features
- AI Assistant
- Document Processing
- Vector Store
- Usage Analytics

## Documentation

### API Reference
- Key management endpoints
- Rate limit configuration
- Usage reporting
- Error handling

### Operations Guide
- Setup procedures
- Maintenance tasks
- Troubleshooting
- Best practices
