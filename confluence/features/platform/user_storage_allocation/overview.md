# User Storage Allocation System

## Overview

This system provides guaranteed storage space for users based on their subscription tiers while efficiently managing OpenAI project capacity. Each user gets allocated storage that's guaranteed to be available, solving the problem of how to fairly distribute the 100GB OpenAI project limits across multiple users.

## Storage Allocation Strategy

### **Tier-Based Allocations**
- **Basic Plans**: 1GB guaranteed storage
- **Standard Plans**: 5GB guaranteed storage  
- **Pro/Attorney Plans**: 10GB guaranteed storage

### **Project Capacity Planning**
Each 100GB OpenAI project can accommodate:
- **100 Basic users** (100 × 1GB = 100GB)
- **20 Standard users** (20 × 5GB = 100GB)
- **10 Pro users** (10 × 10GB = 100GB)
- **Mixed allocation**: e.g., 50 Basic + 5 Standard + 2 Pro = 95GB (5GB buffer)

## Architecture

### **Database Schema**

#### **user_storage_allocations**
- Tracks each user's guaranteed storage allocation
- Links users to specific OpenAI projects
- Monitors actual storage usage vs. guaranteed amount

#### **openai_project_capacity**
- Tracks capacity planning for each project
- Monitors allocated vs. used storage
- Counts users by subscription tier
- Controls whether project accepts new users

### **Key Models**

#### **UserStorageAllocation**
```php
- user_id: User who owns the allocation
- openai_project_id: Assigned OpenAI project
- guaranteed_bytes: Guaranteed storage amount
- used_bytes: Currently used storage
- subscription_tier: User's subscription level
```

#### **OpenAiProjectCapacity**
```php
- total_capacity_bytes: 100GB limit
- allocated_bytes: Total allocated to users
- used_bytes: Actually used storage
- reserved_bytes: Buffer space (5GB)
- basic_users/standard_users/pro_users: User counts
- accepts_new_users: Can accept new allocations
```

## How It Works

### **1. User Registration/Subscription**
When a user subscribes:
1. System determines their storage tier
2. Finds an OpenAI project with available capacity
3. Allocates guaranteed storage space
4. Creates UserStorageAllocation record
5. Updates project capacity tracking

### **2. Case Assignment**
When a user creates a case:
1. System checks user's storage allocation
2. Assigns case to user's allocated OpenAI project
3. All documents go to the same project

### **3. Storage Tracking**
When documents are uploaded:
1. DocumentObserver tracks file sizes
2. Updates both project storage_used and user used_bytes
3. Enforces user's guaranteed storage limit
4. Prevents uploads that exceed allocation

### **4. Subscription Changes**
When users upgrade/downgrade:
1. System calculates new storage requirement
2. Checks if current project can accommodate change
3. Moves user to different project if needed
4. Updates all user's cases to new project
5. Handles cleanup if user exceeds new limit

## Management Commands

### **Storage Status**
```bash
php artisan storage:manage-allocations status
```
Shows:
- Project capacity overview
- User allocation summary
- Unallocated users count

### **Allocate Storage**
```bash
php artisan storage:manage-allocations allocate --user=123 --tier=pro_se_standard
```
Manually allocate storage for specific user.

### **Migrate Existing Users**
```bash
php artisan storage:manage-allocations migrate
```
Migrates existing users to the allocation system.

## Benefits

### **1. Guaranteed Storage**
- Users get guaranteed storage based on their plan
- No risk of running out of space due to other users
- Predictable capacity planning

### **2. Efficient Resource Utilization**
- Optimal packing of users into projects
- 5GB buffer prevents over-allocation
- Automatic project creation when needed

### **3. Fair Distribution**
- Higher-paying users get more storage
- Clear tier-based allocation
- No "first come, first served" issues

### **4. Operational Visibility**
- Clear capacity tracking per project
- User allocation monitoring
- Proactive capacity planning

## Implementation Details

### **Storage Enforcement**
```php
// Before upload
$allocation = $user->storageAllocation;
if (!$allocation->canAccommodateFile($fileSize)) {
    throw new ValidationException('Upload exceeds your storage quota');
}
```

### **Project Assignment**
```php
// Case creation
$allocation = $user->storageAllocation;
$case->update(['openai_project_id' => $allocation->openai_project_id]);
```

### **Capacity Management**
```php
// Check if project can accept new user
if ($capacity->canAccommodateUser($tier)) {
    $capacity->allocateUser($tier);
}
```

## Migration Strategy

### **Phase 1: Deploy System**
1. Run migrations to create new tables
2. Deploy new code with allocation logic
3. System works in hybrid mode (allocated + legacy users)

### **Phase 2: Migrate Users**
1. Run migration command for existing users
2. Allocate storage based on current subscriptions
3. Monitor for any issues

### **Phase 3: Enforce Limits**
1. Enable strict storage enforcement
2. Remove legacy fallback code
3. Full allocation-based system

## Monitoring & Alerts

### **Capacity Alerts**
- Project approaching full allocation
- Users nearing their storage limits
- Failed allocation attempts

### **Usage Analytics**
- Storage usage patterns by tier
- Project utilization efficiency
- User upgrade triggers

## Future Enhancements

### **Dynamic Allocation**
- Temporary storage boosts for large cases
- Seasonal allocation adjustments
- Usage-based optimization

### **Storage Marketplace**
- Users can purchase additional storage
- Temporary storage rentals
- Storage sharing between team members

### **Advanced Analytics**
- Predictive capacity planning
- Usage pattern analysis
- Optimization recommendations

This system provides a robust foundation for managing user storage while maintaining the efficiency of the OpenAI project load balancing system.
