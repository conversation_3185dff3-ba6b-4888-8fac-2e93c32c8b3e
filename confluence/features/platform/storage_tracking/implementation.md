# OpenAI Storage Tracking Implementation

## Overview

This implementation adds comprehensive storage tracking for OpenAI projects to address the critical gap in the load balancing system. The system now properly tracks storage usage against the 100GB per-project limit and includes monitoring and alerting capabilities.

## Components Implemented

### 1. DocumentObserver (`app/Observers/DocumentObserver.php`)

**Purpose**: Automatically tracks storage usage when documents are created, updated, or deleted.

**Key Features**:
- Increments storage when documents are created
- Handles file size changes when documents are updated
- Decrements storage when documents are deleted
- Comprehensive logging for debugging and monitoring
- Error handling to prevent storage tracking failures from breaking document operations

### 2. Enhanced CaseAssistantService

**Changes Made**:
- Added storage limit checks (95GB threshold) when assigning cases to projects
- Improved project selection logic to avoid over-capacity projects
- Enhanced logging for project assignments
- Better error messages when no projects are available

### 3. OpenAiProject Model Enhancements

**New Methods**:
- `getStorageUsagePercentage()`: Calculate percentage of 100GB limit used
- `isNearingCapacity($threshold)`: Check if project is approaching capacity
- `getStorageInfo()`: Get comprehensive storage information
- `getAvailableStorage()`: Get remaining storage in bytes

### 4. Storage Monitoring Commands

#### MonitorOpenAiStorage (`app/Console/Commands/MonitorOpenAiStorage.php`)
- Monitor all projects and alert on capacity issues
- Configurable threshold (default 90%)
- Detailed reporting with status indicators
- Optional alert logging for monitoring systems

#### OpenAiStorageStatus (`app/Console/Commands/OpenAiStorageStatus.php`)
- Quick status check for all projects
- JSON output option for integration with other systems
- Summary statistics

#### Enhanced ManageOpenAiProjects
- Updated to show storage information in project listings
- Visual indicators for project status
- Capacity warnings in summary

### 5. Data Migration

**Purpose**: Recalculate storage usage for existing projects.

**File**: `database/migrations/2025_01_27_000000_recalculate_openai_project_storage_usage.php`

**What it does**:
- Iterates through all existing projects
- Calculates total storage from associated documents
- Updates storage_used field with accurate values
- Comprehensive logging of changes

### 6. Test Suite

**File**: `tests/Feature/OpenAiStorageTrackingTest.php`

**Coverage**:
- Storage increment on document creation
- Storage decrement on document deletion
- Storage updates when file sizes change
- Percentage calculations
- Storage info methods
- Edge cases (documents without projects)

## Usage

### Running the Migration

```bash
php artisan migrate
```

This will recalculate storage usage for all existing projects.

### Monitoring Commands

```bash
# Check current storage status
php artisan openai:storage-status

# Monitor with alerts (90% threshold)
php artisan openai:monitor-storage --alert

# Monitor with custom threshold
php artisan openai:monitor-storage --threshold=85 --alert

# List projects with storage info
php artisan openai:projects list

# Get JSON output for integration
php artisan openai:storage-status --json
```

### Scheduling Monitoring

Add to `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule)
{
    // Monitor storage daily and log alerts
    $schedule->command('openai:monitor-storage --alert')
             ->daily()
             ->at('09:00');
}
```

## Key Improvements

### 1. Accurate Load Balancing
- Projects are now selected based on actual storage usage
- 95GB threshold prevents exceeding OpenAI limits
- Better distribution of cases across projects

### 2. Proactive Monitoring
- Early warning system for capacity issues
- Detailed logging for troubleshooting
- Integration-ready JSON output

### 3. Production Readiness
- Comprehensive error handling
- Detailed logging throughout the system
- Test coverage for critical functionality
- Data migration for existing installations

### 4. Operational Visibility
- Clear status indicators in command outputs
- Human-readable storage information
- Summary statistics for quick assessment

## Configuration

### Storage Limits

The system uses a 95GB threshold (95% of 100GB) to provide a safety buffer. This can be adjusted in:

```php
// In CaseAssistantService::configureOpenAi()
$storageLimit = 95 * 1024 * 1024 * 1024; // 95GB in bytes
```

### Monitoring Thresholds

Default warning threshold is 90% but can be customized:

```php
// In OpenAiProject model
public function isNearingCapacity(float $threshold = 90.0): bool
```

## Next Steps

1. **Deploy and Test**: Run the migration and test with real data
2. **Set Up Monitoring**: Schedule the monitoring command
3. **Configure Alerts**: Set up log monitoring for storage alerts
4. **Documentation**: Update operational procedures
5. **Capacity Planning**: Monitor usage patterns and plan for additional projects

## Troubleshooting

### Storage Tracking Issues

Check logs for DocumentObserver errors:
```bash
tail -f storage/logs/laravel.log | grep "storage usage"
```

### Recalculate Storage Manually

If storage values seem incorrect:
```bash
php artisan migrate:rollback --step=1
php artisan migrate
```

### Check Project Assignments

Verify cases are being assigned to appropriate projects:
```bash
php artisan openai:storage-status
```

## Security Considerations

- API keys are still masked in command outputs
- Storage information doesn't expose sensitive data
- Logging includes project IDs but not API keys
- Error handling prevents information leakage
