# Multilingual Support

## Overview
Justice Quest implements a comprehensive multilingual support system, currently supporting English and Spanish. The implementation follows <PERSON><PERSON>'s localization best practices with a robust architecture for language switching and persistence.

## Architecture

### Language Switching Flow

1. **Entry Points**
   - Language selector dropdown (navigation menu)
   - User settings page
   - Initial middleware detection

2. **Component Architecture**
   ```mermaid
   graph TD
       A[User Interaction] --> B[Livewire Component]
       B --> C{User Authenticated?}
       C -->|Yes| D[Update User Model]
       C -->|No| E[Update Session]
       D --> F[Set App Locale]
       E --> F
       F --> G[Page Refresh]
   ```

### Persistence Layer

1. **Authenticated Users**
   - Language preference stored in users table
   - Updated via `Auth::user()->update(['language' => $language])`
   - Persists across sessions and devices
   - Retrieved on login via middleware

2. **Guest Users**
   - Language stored in session: `session()->put('language', $language)`
   - Persists until session expires
   - Retrieved via middleware on each request

3. **Browser Fallback**
   - Uses `$request->getPreferredLanguage()`
   - Checks against available languages
   - Falls back to config default ('en')

### Implementation Components

1. **SetLocale Middleware**
```php
Priority order:
1. Authenticated user preference
2. Session language
3. Browser preference
4. Default language
```

2. **LanguageSelector Component**
```php
Key responsibilities:
- Handles language switching
- Updates persistence layer
- Triggers page refresh
- Maintains current language state
```

3. **UserLanguageSettings Component**
```php
Features:
- Dedicated settings interface
- Validates language selection
- Broadcasts language updates
- Handles navigation
```

### State Management

1. **Database Storage**
```php
users table:
- language column (string)
- default value from config
- updated on language switch
```

2. **Session Storage**
```php
session data:
- 'language' key
- updated on switch
- checked by middleware
```

3. **Application State**
```php
Runtime state:
- App::setLocale()
- Available in views
- Middleware managed
```

### Language Switching Process

1. **User Triggers Change**
   ```php
   // Via LanguageSelector component
   public function switchLanguage($language)
   {
       // Validate language
       if (!array_key_exists($language, config('language.available'))) {
           return;
       }

       // Update current state
       $this->currentLanguage = $language;

       // Persist for authenticated users
       if (Auth::check()) {
           Auth::user()->update(['language' => $language]);
       }

       // Persist in session
       session()->put('language', $language);

       // Update application locale
       App::setLocale($language);

       // Refresh page to apply changes
       return $this->redirect(request()->header('Referer'));
   }
   ```

2. **Middleware Processing**
   ```php
   // SetLocale middleware
   public function handle(Request $request, Closure $next): Response
   {
       $locale = $this->determineLocale($request);
       App::setLocale($locale);
       return $next($request);
   }
   ```

3. **View Updates**
   - Page refreshes to load new translations
   - All translated strings update
   - Maintains user context and state

### Error Handling

1. **Invalid Language Selection**
   - Validates against available languages
   - Falls back to default if invalid
   - Logs invalid attempts

2. **Missing Translations**
   - Falls back to default language
   - Logs missing translation keys
   - Maintains application stability

## Technical Details

### Language Configuration
```php
Configuration (config/language.php):
- Available languages: English (en), Spanish (es)
- Language metadata includes:
  - Native name
  - English name
  - Flag emoji
  - Active status
- Default language: English
```

### Core Components

1. **Middleware (SetLocale)**
   - Determines user's language preference using:
     1. Authenticated user's stored preference
     2. Session-stored language
     3. Browser language preferences
     4. System default (en)

2. **Language Selector (Livewire Component)**
   - Interactive language switching
   - Real-time UI updates
   - Persists preferences for:
     - Authenticated users (database)
     - Guest users (session)

3. **User Settings**
   - Dedicated language preference section
   - Persists across sessions
   - Integrates with user profile

### Implementation

#### Language Detection Flow
```mermaid
graph TD
    A[Request] --> B{User Authenticated?}
    B -->|Yes| C[Use User Preference]
    B -->|No| D{Session Language Set?}
    D -->|Yes| E[Use Session Language]
    D -->|No| F[Use Browser Preference]
    F --> G{Browser Language Available?}
    G -->|Yes| H[Use Browser Language]
    G -->|No| I[Use Default: en]
```

#### Translation Structure
```
resources/lang/
├── en/
│   ├── app.php           # Global application strings
│   ├── auth.php          # Authentication related
│   ├── forms.php         # Common form labels/messages
│   ├── validation.php    # Validation messages
│   ├── notifications.php # System notifications
│   └── features/         # Feature-specific translations
└── es/
    └── [same structure as en/]
```

### Translation Rules

1. **Key Organization**
   - Feature-based grouping
   - Context-specific files
   - Consistent naming patterns
   - Dot notation for nesting

2. **Implementation Standards**
   - No hardcoded strings
   - Use translation helpers
   - Parameter-based dynamic content
   - Consistent placeholder handling

3. **View Implementation**
   ```php
   // Correct Usage
   {{ __('voice.start_recording') }}
   
   // With Parameters
   {{ __('cases.status', ['name' => $caseName]) }}
   ```

## Dependencies
- Laravel Localization
- Livewire for real-time updates
- Database storage for user preferences
- Session handling for guest preferences

## Related Features
- User Profile Management
- Session Management
- UI Components
- Form Validation

## Future Enhancements
- Additional language support
- RTL language capability
- Language-specific formatting
- Automated translation workflow
- Translation management interface

## Best Practices

### Language Switching
1. Always validate language selection
2. Update all persistence layers
3. Refresh page for complete update
4. Maintain user context
5. Handle errors gracefully

### State Management
1. Use middleware for consistency
2. Update all storage layers
3. Validate before persistence
4. Handle guest users appropriately
5. Maintain fallback chain

### Development Guidelines
1. Always use translation keys
2. Group translations logically
3. Maintain consistent naming
4. Document context requirements
5. Consider string length variations

### Quality Assurance
1. Verify all strings are translated
2. Test language switching
3. Validate dynamic content
4. Check formatting consistency
5. Review translation context

### Performance Considerations
1. Translation file caching
2. Lazy loading where appropriate
3. Efficient language switching
4. Optimized session handling

## Technical Considerations

### Performance
- Efficient language detection
- Minimal database queries
- Session optimization
- Smart refresh handling

### Security
- Input validation
- User preference verification
- Session data protection
- XSS prevention
