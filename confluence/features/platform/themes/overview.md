# Themes

## Overview
Justice Quest implements a flexible theme system using DaisyUI, providing multiple pre-configured themes with persistent user preferences.

## Technical Details

### Architecture

1. **Theme Storage**
   - Themes persist in localStorage
   - Fallback to 'light' theme if no preference
   - Applied globally via HTML data-theme attribute

2. **Theme Management**
```javascript
Alpine.js store implementation:
- Manages current theme state
- Handles theme switching
- Persists preferences
- Initializes on page load
```

### Implementation

#### Core Components

1. **Alpine.js Theme Store**
```javascript
Alpine.store('theme', {
    current: localStorage.getItem('theme') || 'light',
    
    init() {
        document.documentElement.setAttribute('data-theme', this.current);
    },
    
    setTheme(theme) {
        this.current = theme;
        localStorage.setItem('theme', theme);
        document.documentElement.setAttribute('data-theme', theme);
    }
});
```

2. **Theme Selector UI**
```html
Theme dropdown in navigation:
- Light/Dark options
- Additional DaisyUI themes
- Real-time switching
- Visual indicators
```

3. **Layout Integration**
```html
Base layout implementation:
- data-theme binding
- Theme application to HTML/body
- Consistent theme context
```

### Available Themes
```javascript
DaisyUI configured themes:
- light
- dark
- cupcake
- bumblebee
- emerald
- corporate
- synthwave
- retro
- cyberpunk
[and more...]
```

### Theme Application Flow
```mermaid
graph TD
    A[Page Load] --> B[Check localStorage]
    B --> C{Theme Found?}
    C -->|Yes| D[Apply Stored Theme]
    C -->|No| E[Apply Default Theme]
    F[User Selects Theme] --> G[Update Store]
    G --> H[Save to localStorage]
    H --> I[Update data-theme]
```

## Dependencies
- Alpine.js for state management
- DaisyUI for theme definitions
- Tailwind CSS for base styling
- localStorage for persistence

## Best Practices

### Theme Development
1. Use DaisyUI color variables
2. Maintain consistent contrast ratios
3. Test all themes for accessibility
4. Use semantic color names
5. Support dark/light modes

### Implementation Guidelines
1. Always use DaisyUI classes
2. Maintain theme consistency
3. Handle theme transitions
4. Consider performance
5. Test across browsers

## Technical Notes

### Theme Switching Process
1. User selects new theme
2. Alpine store updates state
3. Persists to localStorage
4. Updates HTML data-theme
5. DaisyUI applies styles

### Performance Considerations
1. Theme styles preloaded
2. Minimal theme switch flicker
3. Efficient localStorage usage
4. Optimized CSS variables

### Browser Support
1. Modern browser compatibility
2. localStorage fallbacks
3. CSS variable support
4. Theme persistence handling

## Future Enhancements
1. Custom theme creation
2. Theme scheduling
3. System theme detection
4. Theme transition animations
5. Per-component theming
