# OpenAI Key Management & Production Deployment Guide

## Overview

This guide covers how to set up, manage, and monitor OpenAI project keys in production. The system automatically distributes users across multiple OpenAI projects to overcome the 100GB storage limit per project.

## 🚀 Production Deployment

### Pre-Deployment Setup

#### 1. Create OpenAI Projects
1. **Log into OpenAI Dashboard**: https://platform.openai.com/
2. **Create 2-3 separate projects** (recommended starting point):
   - Project 1: "Production Main"
   - Project 2: "Production Overflow" 
   - Project 3: "Production Backup"
3. **For each project**:
   - Generate API key
   - Note the Organization ID
   - Test the API key with a simple call

#### 2. Prepare API Keys
```bash
# Test each API key works (optional but recommended)
curl -H "Authorization: Bearer YOUR_API_KEY" \
     -H "OpenAI-Organization: YOUR_ORG_ID" \
     https://api.openai.com/v1/models
```

### Deployment Steps

#### 1. Deploy Code & Run Migrations
```bash
# Deploy your code
git pull origin main

# Run database migrations
php artisan migrate --force

# Clear caches
php artisan config:clear
php artisan cache:clear
```

#### 2. Add OpenAI Projects (Manual - Recommended)
1. **Access Admin Interface**: 
   - URL: `https://your-domain.com/manage-project-tokens`
   - Login with admin email: `<EMAIL>`

2. **Add Each Project**:
   - Click "Add New Project"
   - Fill in details:
     - **Name**: Descriptive name (e.g., "Production Main")
     - **API Key**: Your OpenAI API key
     - **Organization ID**: Your OpenAI organization ID
     - **Active**: ✅ Checked
   - Click "Save"
   - Repeat for all projects

#### 3. Initialize Capacity Tracking
```bash
# Set up capacity tracking for all projects
php artisan openai:initialize-capacity

# Expected output:
# ✅ Initialized capacity for: Production Main
# ✅ Initialized capacity for: Production Overflow  
# ✅ Initialized capacity for: Production Backup
```

#### 4. Migrate Existing Users (If Any)
```bash
# Check current allocation status
php artisan storage:manage-allocations status

# Migrate existing users to new system
php artisan storage:manage-allocations migrate

# Verify allocations
php artisan storage:manage-allocations status
```

#### 5. Verify System Health
```bash
# Check storage monitoring
php artisan openai:monitor-storage

# Run capacity planning
php artisan openai:capacity-planning

# Verify scheduled tasks are configured
php artisan schedule:list
```

#### 6. Set Up Cron Job
Add to your server's crontab:
```bash
* * * * * cd /path-to-your-project && php artisan schedule:run >> /dev/null 2>&1
```

## 📊 Monitoring & Management

### Dashboard Access

#### Main Dashboard
- **URL**: `/dashboard`
- **Features**: Capacity widget at top (admin only)
- **Auto-refresh**: Every 5 minutes if critical

#### OpenAI Projects Management
- **URL**: `/manage-project-tokens`
- **Features**: 
  - Enhanced project list with storage details
  - Visual progress bars
  - User allocation counts
  - Add/edit/delete projects

#### Admin Dashboard
- **URL**: `/admin/dashboard`
- **Features**:
  - Comprehensive capacity overview
  - Quick action buttons
  - Project status table
  - Command reference

### Monitoring Commands

#### Real-time Status
```bash
# Quick status check
php artisan openai:storage-status

# Detailed monitoring with alerts
php artisan openai:monitor-storage --alert

# Capacity planning and forecasting
php artisan openai:capacity-planning --alert
```

#### User Management
```bash
# View allocation status
php artisan storage:manage-allocations status

# Allocate storage for specific user
php artisan storage:manage-allocations allocate --user=123 --tier=pro_se_standard

# Migrate users to allocation system
php artisan storage:manage-allocations migrate
```

### Automated Monitoring

The system automatically runs:
- **Hourly**: Storage monitoring with alerts
- **Daily 9 AM**: Capacity planning report
- **Weekly Monday 9 AM**: 60-day forecast

## 🚨 Alert System

### Alert Levels

#### 🔴 CRITICAL (Add Project Immediately)
- **Triggers**:
  - 0 projects accepting new users
  - All projects at 95%+ storage capacity
  - New user registrations failing
- **Action**: Drop everything, add new project within 15 minutes

#### 🟡 WARNING (Add Project This Week)
- **Triggers**:
  - Only 1 project accepting new users
  - Available capacity < 50GB total
  - Can accommodate < 50 new Basic users
- **Action**: Schedule project addition within 1 week

#### 🟢 NOTICE (Plan for 2-4 Weeks)
- **Triggers**:
  - Only 2 projects accepting new users
  - Growth trends indicate future issues
- **Action**: Begin planning for new project

### Where to Find Alerts

#### Dashboard Indicators
- **Color coding**: Red/Yellow/Green status
- **Auto-refresh**: Critical alerts refresh page every 5 minutes
- **Visual progress bars**: Show capacity usage

#### Log Files
```bash
# View recent capacity alerts
tail -f storage/logs/laravel.log | grep "capacity"

# Search for critical alerts
grep "critical.*capacity" storage/logs/laravel.log
```

## 🔧 Adding New Projects

### When to Add Projects

Add new projects when:
- Dashboard shows yellow/red status
- Monitoring commands show warnings
- Available capacity drops below 100GB
- Only 1-2 projects accepting users

### How to Add Projects

#### 1. Create in OpenAI Dashboard
1. Go to https://platform.openai.com/
2. Create new project
3. Generate API key
4. Note organization ID

#### 2. Add to System
1. Go to `/manage-project-tokens`
2. Click "Add New Project"
3. Enter project details
4. Set as Active
5. Save

#### 3. Initialize Capacity
```bash
# Initialize capacity for new projects
php artisan openai:initialize-capacity

# Verify new project is accepting users
php artisan openai:monitor-storage
```

## 🔍 Troubleshooting

### Common Issues

#### No Projects Accepting Users
```bash
# Check project status
php artisan openai:storage-status

# Initialize capacity if missing
php artisan openai:initialize-capacity

# Verify projects are active
# Check /manage-project-tokens dashboard
```

#### Storage Tracking Issues
```bash
# Recalculate storage usage
php artisan migrate:rollback --step=1
php artisan migrate

# Check for missing relationships
php artisan storage:manage-allocations status
```

#### Monitoring Not Working
```bash
# Check scheduled tasks
php artisan schedule:list

# Test commands manually
php artisan openai:monitor-storage --alert

# Verify cron job is running
tail -f /var/log/cron
```

### Emergency Procedures

#### If All Projects Are Full
1. **Immediate**: Create new OpenAI project
2. **Add to system**: Use web interface
3. **Initialize**: Run capacity command
4. **Verify**: Check monitoring dashboard

#### If Users Can't Register
1. **Check capacity**: Run monitoring command
2. **Add project**: If needed
3. **Temporarily disable**: New registrations if critical
4. **Clean up**: Unused storage in existing projects

## 📈 Capacity Planning

### Project Capacity

Each 100GB OpenAI project can accommodate:
- **100 Basic users** (1GB each) = 100GB
- **20 Standard users** (5GB each) = 100GB  
- **10 Pro users** (10GB each) = 100GB
- **Mixed allocation** with 5GB safety buffer

### Growth Planning

#### Monthly Review
```bash
# Run 30-day forecast
php artisan openai:capacity-planning --forecast-days=30

# Analyze growth trends
php artisan openai:monitor-storage
```

#### Quarterly Planning
```bash
# Run 60-day forecast for quarterly planning
php artisan openai:capacity-planning --forecast-days=60

# Review capacity utilization efficiency
# Plan for seasonal growth patterns
```

### Scaling Strategy

#### Small Scale (< 500 users)
- **2-3 projects**: Sufficient for most needs
- **Monitor weekly**: Check capacity status
- **Add projects**: When 1 project remaining

#### Medium Scale (500-2000 users)
- **5-8 projects**: Better distribution
- **Monitor daily**: Automated alerts
- **Add projects**: When 2 projects remaining

#### Large Scale (2000+ users)
- **10+ projects**: Enterprise level
- **Monitor hourly**: Real-time alerts
- **Add projects**: When 3+ projects remaining

## 🔒 Security Best Practices

### API Key Management
- **Rotate keys**: Every 90 days
- **Monitor usage**: Check OpenAI dashboard
- **Separate environments**: Different keys for dev/staging/prod
- **Access control**: Limit who can manage keys

### System Security
- **Admin access**: Restricted to specific emails
- **HTTPS only**: All admin interfaces
- **Log monitoring**: Track capacity changes
- **Backup keys**: Store securely offline

## 📋 Maintenance Checklist

### Weekly
- [ ] Review capacity status dashboard
- [ ] Check growth trends
- [ ] Verify scheduled tasks are running
- [ ] Review alert logs

### Monthly
- [ ] Analyze capacity utilization efficiency
- [ ] Review user tier distribution
- [ ] Plan for seasonal growth patterns
- [ ] Update capacity forecasting models

### Quarterly
- [ ] Review overall capacity strategy
- [ ] Analyze cost vs. capacity optimization
- [ ] Plan for major feature releases
- [ ] Update monitoring thresholds if needed
- [ ] Rotate API keys

This system ensures you'll always know when to add new OpenAI project keys and never run out of capacity for your users.
