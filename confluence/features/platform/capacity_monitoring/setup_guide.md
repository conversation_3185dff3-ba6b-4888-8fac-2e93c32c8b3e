# OpenAI Capacity Monitoring Setup Guide

## Overview

This guide explains how to monitor OpenAI project capacity and know exactly when to add new project keys. The system provides multiple layers of monitoring and alerting to ensure you never run out of capacity.

## 🚨 When You Need New Project Keys

### **CRITICAL - Add Immediately**
- ❌ **0 projects accepting new users**
- ❌ **All projects at 95%+ storage capacity**
- ❌ **New user registrations failing**

### **WARNING - Add Within 1 Week**
- ⚠️ **Only 1 project accepting new users**
- ⚠️ **Available capacity < 50GB total**
- ⚠️ **Can accommodate < 50 new Basic users**

### **NOTICE - Plan for 2-4 Weeks**
- 📋 **Only 2 projects accepting new users**
- 📋 **Available capacity < 100GB total**
- 📋 **Growth trends indicate capacity issues**

## 🔧 Monitoring Commands

### **1. Real-time Capacity Check**
```bash
# Quick status check
php artisan openai:storage-status

# Detailed monitoring with alerts
php artisan openai:monitor-storage --alert

# Capacity planning and forecasting
php artisan openai:capacity-planning --alert
```

### **2. User Storage Management**
```bash
# View allocation status
php artisan storage:manage-allocations status

# Migrate existing users
php artisan storage:manage-allocations migrate
```

## 📊 Automated Monitoring

### **Scheduled Tasks (Already Configured)**
- **Hourly**: Storage monitoring with alerts
- **Daily 9 AM**: Capacity planning report
- **Weekly Monday 9 AM**: 60-day forecast

### **Real-time Triggers**
- **User Registration**: Checks capacity after each new user
- **Project Capacity Changes**: Alerts when projects become full
- **Storage Threshold Breaches**: Immediate alerts at 90%, 95%

## 🎯 Key Metrics to Watch

### **Project Metrics**
- **Projects Accepting New Users**: Should be ≥ 2
- **Available Storage**: Should be ≥ 100GB total
- **Storage Usage**: Watch for projects approaching 90%

### **User Capacity**
- **Basic Users**: Can accommodate ≥ 50 new users
- **Standard Users**: Can accommodate ≥ 10 new users  
- **Pro Users**: Can accommodate ≥ 5 new users

### **Growth Indicators**
- **Daily User Growth Rate**: Track registration trends
- **Storage Growth Rate**: Monitor document upload patterns
- **Tier Distribution**: Watch for shifts to higher tiers

## 📧 Alert Channels

### **Log-based Alerts**
All alerts are logged with structured data for monitoring systems:

```bash
# View recent capacity alerts
tail -f storage/logs/laravel.log | grep "capacity"

# Search for critical alerts
grep "critical.*capacity" storage/logs/laravel.log
```

### **Dashboard Alerts**
Add the capacity widget to your admin dashboard:

```php
// In your admin dashboard view
<x-capacity-status-widget />
```

### **Email Alerts (Optional)**
Configure email alerts in `CapacityAlertService`:

```php
// Uncomment and configure in CapacityAlertService::sendAlert()
if ($alert['level'] === 'critical') {
    Mail::to(config('app.admin_email'))->send(
        new CapacityAlert($alert)
    );
}
```

## 🔍 Monitoring Workflow

### **Daily Routine**
1. Check dashboard capacity widget
2. Review overnight alert logs
3. Monitor user registration trends

### **Weekly Routine**
1. Run capacity planning report
2. Review growth forecasts
3. Plan for upcoming capacity needs

### **When Alerts Fire**
1. **Critical Alerts**: Drop everything, add project immediately
2. **Warning Alerts**: Schedule project addition within 1 week
3. **Notice Alerts**: Begin planning for new project

## 📈 Capacity Planning

### **Project Addition Timeline**
- **New OpenAI Project Setup**: 1-2 hours
- **API Key Configuration**: 15 minutes
- **System Integration**: 5 minutes
- **Testing & Validation**: 30 minutes

### **Capacity Per Project**
- **100 Basic users** (1GB each) = 100GB
- **20 Standard users** (5GB each) = 100GB
- **10 Pro users** (10GB each) = 100GB
- **Mixed allocation** with 5GB safety buffer

### **Growth Planning**
Use the forecasting command to plan ahead:

```bash
# 30-day forecast
php artisan openai:capacity-planning --forecast-days=30

# 60-day forecast for quarterly planning
php artisan openai:capacity-planning --forecast-days=60
```

## 🚀 Quick Response Actions

### **When Critical Alert Fires**
1. **Immediate**: Create new OpenAI project in dashboard
2. **Add API key**: Configure in system within 15 minutes
3. **Verify**: Run `php artisan openai:storage-status` to confirm
4. **Monitor**: Watch for successful user allocations

### **Emergency Procedures**
If you can't add a project immediately:
1. **Temporarily disable new registrations**
2. **Clean up unused storage** in existing projects
3. **Contact existing users** about storage cleanup
4. **Prioritize critical users** for available capacity

## 📋 Maintenance Checklist

### **Weekly**
- [ ] Review capacity status dashboard
- [ ] Check growth trends
- [ ] Verify scheduled tasks are running
- [ ] Review alert logs

### **Monthly**
- [ ] Analyze capacity utilization efficiency
- [ ] Review user tier distribution
- [ ] Plan for seasonal growth patterns
- [ ] Update capacity forecasting models

### **Quarterly**
- [ ] Review overall capacity strategy
- [ ] Analyze cost vs. capacity optimization
- [ ] Plan for major feature releases
- [ ] Update monitoring thresholds if needed

## 🔧 Troubleshooting

### **No Alerts Firing**
- Check scheduled tasks: `php artisan schedule:list`
- Verify log permissions: `ls -la storage/logs/`
- Test manually: `php artisan openai:monitor-storage --alert`

### **False Alerts**
- Check capacity calculation logic
- Verify project status in database
- Review user allocation accuracy

### **Missing Capacity Data**
- Run: `php artisan storage:manage-allocations status`
- Check for missing capacity records
- Verify project relationships

This monitoring system ensures you'll always know when to add new OpenAI project keys, preventing capacity issues before they impact users.
