# Chat Interface

## Overview
The Chat Interface is a Livewire component that provides real-time communication capabilities within the application. It supports both case-specific conversations and general chats, allowing users to create and manage multiple conversation threads.

## Technical Details

### Core Components

1. **Livewire Component**
   - Main Component: `App\Livewire\Chat\ChatInterface`
   - View: `resources/views/livewire/chat/chat-interface.blade.php`

2. **Models**
   - `AssistantThread`: Represents a conversation thread
   - `AssistantMessage`: Represents individual messages within a thread

3. **Services**
   - `AssistantChatService`: Handles thread creation and message management

### Architecture

#### Component Structure
The Chat Interface is built as a Livewire component with the following key properties:
- `$caseFileId`: Optional ID of the associated case file
- `$threads`: Collection of conversation threads
- `$currentThreadId`: ID of the currently selected thread
- `$messages`: Collection of messages in the current thread
- `$newMessage`: Content of the message being composed

#### Thread Management
Threads are organized conversations that can be:
- Case-specific (linked to a `CaseFile`)
- General (not linked to any specific case)

Each thread has:
- Title
- Description (optional)
- Category (optional)
- Creation timestamp
- Associated messages

#### Message Flow
1. User selects or creates a thread
2. Messages are loaded for the selected thread
3. User composes and sends new messages
4. Messages are displayed in chronological order

## Implementation Details

### Files Involved

1. **Component Class**
   ```
   app/Livewire/Chat/ChatInterface.php
   ```
   - Handles all chat logic and state management
   - Manages thread creation and selection
   - Processes message sending and receiving

2. **View Template**
   ```
   resources/views/livewire/chat/chat-interface.blade.php
   ```
   - Renders the chat UI
   - Displays thread list and messages
   - Contains input controls for new messages

3. **Service Class**
   ```
   app/Services/AssistantChatService.php
   ```
   - Provides methods for thread and message operations
   - Handles persistence and retrieval of chat data

4. **Models**
   ```
   app/Models/AssistantThread.php
   app/Models/AssistantMessage.php
   ```
   - Define database structure and relationships
   - Implement query scopes and accessors

### Key Methods

#### Thread Operations
- `loadThreads()`: Retrieves all threads for the current user/case
- `selectThread($threadId)`: Changes the active thread and loads its messages
- `createNewThread()`: Prepares the UI for thread creation
- `saveNewThread()`: Validates and persists a new thread

#### Message Operations
- `loadMessages()`: Retrieves messages for the current thread
- `sendMessage()`: Validates and saves a new message
- `deleteMessage($messageId)`: Removes a message from the thread

### Current Limitations
- Thread creation currently requires a case file context
- No support for message editing
- Limited message formatting options

## Integration Points

### Case File System
- Chat threads can be associated with specific case files
- Case context is used for AI-assisted responses
- Case documents can be referenced in conversations

### User Authentication
- Messages are associated with the authenticated user
- Thread access is controlled based on user permissions
- User avatars and names are displayed with messages

### AI Assistant
- AI can participate in conversations when enabled
- Responses are generated based on thread context
- Document references can be included in AI responses

## Workflows

### Creating a New Thread
1. User clicks "New Thread" button
2. Modal appears with thread creation form
3. User enters thread title and optional details
4. System validates input and creates thread
5. New thread is selected automatically

### Sending Messages
1. User selects a thread
2. Types message in the input field
3. Clicks send or presses Enter
4. Message is saved and displayed in the thread
5. Other participants are notified of new message

## Future Enhancements

### Planned Features
- Support for threads without case file context
- Rich text formatting for messages
- File attachments in messages
- Read receipts and typing indicators
- Thread categorization and filtering

### Technical Improvements
- Real-time updates using Laravel Echo
- Message pagination for performance
- Offline support with message queuing
- Enhanced security for sensitive conversations