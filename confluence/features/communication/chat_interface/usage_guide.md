# Chat Interface Usage Guide

## Accessing the Chat Interface

The Chat Interface can be accessed in two ways:

1. **From a Case File**: When viewing a case, click the "Chat" button in the case navigation menu. This opens a case-specific chat where all conversations are related to that case.

2. **From the Global Navigation**: Click the "Chat" icon in the main navigation bar to access the general chat interface. This allows conversations that aren't tied to a specific case.

## Managing Threads

### Viewing Threads
- All available threads are displayed in the left sidebar
- Threads are sorted by most recent activity
- Case-specific threads show the case name
- Unread threads are highlighted

### Creating a New Thread
1. Click the "New Thread" button at the top of the thread list
2. Enter a title for the thread (required)
3. Optionally add a description and category
4. Click "Create Thread" to save

### Selecting a Thread
- Click on any thread in the list to view its messages
- The active thread is highlighted in the list
- Thread details are displayed in the header of the message area

## Sending and Receiving Messages

### Composing Messages
1. Select a thread from the list
2. Type your message in the input field at the bottom
3. Press Enter or click the Send button to send

### Message Display
- Messages are displayed in chronological order
- User messages appear on the right
- AI assistant or other user messages appear on the left
- Each message shows the sender's name and timestamp

### AI Assistant Interaction
- The AI assistant automatically responds in case-specific threads
- Responses are based on case context and uploaded documents
- Assistant messages are clearly labeled with an AI indicator

## Current Limitations

### Case-Dependent Functionality
- Creating new threads currently requires a case file context
- This limitation will be addressed in future updates

### Message Features
- No support for message editing after sending
- Limited formatting options for message content
- No file attachment capability yet

## Troubleshooting

### Common Issues

#### 404 Error When Creating Thread
- This occurs when trying to create a thread without a case file context
- Temporary workaround: Always access chat from a case file view

#### Messages Not Loading
- Refresh the page to reload all threads and messages
- Check your internet connection
- Verify you have permission to access the selected thread

#### AI Assistant Not Responding
- Ensure the case has an associated AI assistant
- Check that documents have been properly processed
- Verify that your question is related to the case context