# AI Assistant

## Overview
Each case in the system is equipped with a dedicated OpenAI Assistant instance that provides contextual legal assistance. This assistant is automatically created and configured when a new case is created through the `CaseFileObserver`.

## Technical Details

### Core Components

1. **Assistant Instance**
   - Created via OpenAI Assistants API
   - Uses the `gpt-4o-mini` model
   - Configured with base legal instructions
   - Attached to each individual case via `openai_assistant_id`

2. **Vector Store**
   - Dedicated vector store per case (`openai_vector_store_id`)
   - Stores document embeddings for semantic search
   - Linked to the assistant for document-aware responses

3. **OpenAI Project Management**
   - Load balancing across multiple OpenAI API keys
   - Automatic selection of least-used active project
   - Fallback to environment configuration

### Implementation Flow

1. **Case Creation**
   - `CaseFileObserver::created()` triggers when a new case is created
   - Calls `CaseAssistantService::setupCaseResources()`
   - Creates both assistant and vector store
   - Links them together for document search capabilities

2. **Resource Setup**
   ```php
   setupCaseResources()
   ├── configureOpenAi()          // Sets up API credentials
   ├── createAssistant()          // Creates OpenAI assistant
   ├── createVectorStore()        // Creates vector store
   └── attachVectorStoreToAssistant() // Links resources
   ```

3. **Document Processing**
   - Documents uploaded to a case are processed by `ProcessExhibitJob`
   - Files are uploaded to OpenAI and linked to the assistant
   - Vector store is updated with document embeddings

4. **Cleanup**
   - When a case is deleted, `CaseFileObserver::deleted()` handles cleanup
   - Removes OpenAI assistant and vector store
   - Updates storage usage metrics

## Dependencies

- OpenAI Assistants API
- OpenAI Laravel Facade
- Laravel Model Observers
- Queue System for Document Processing

## Related Features

- Document Upload and Processing
- Case Interview System
- AI Question Generation
- Voice Message Processing (via Whisper AI)

## Configuration

The assistant is configured with base instructions:
```text
You are a dedicated legal assistant for this case. Your role is to:
1. Provide accurate legal information and context based on case documents
2. Help draft legal documents while maintaining professional standards
3. Answer questions about the case using the provided documentation
4. Maintain strict confidentiality and professional ethics
5. Always clarify when legal advice is needed from a licensed attorney
```

## Error Handling

- Failed resource creation is logged and case status updated
- Cleanup operations continue even if individual deletions fail
- API key management includes fallback mechanisms
