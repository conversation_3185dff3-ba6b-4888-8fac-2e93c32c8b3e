# Case Interview (Intake Session)

## Overview
The Case Interview system is an AI-powered workflow that guides users through a structured process of gathering case information. It combines traditional form-based data collection with AI-assisted questioning to ensure comprehensive case understanding.

### Basic Flow
1. User starts interview process
2. Progresses through predefined steps
3. Provides case details and narrative
4. Receives AI-generated follow-up questions
5. Completes interview with desired outcome

## Existing Architecture

### Core Components

#### 1. Livewire Component Structure
```php
InterviewComponent (app/Livewire/Interview/InterviewComponent.php)
├── State Management
│   ├── $currentStep
│   ├── $completedSteps
│   └── $interviewData
├── Step Navigation
│   ├── nextStep()
│   ├── previousStep()
│   └── goToStep()
└── Data Persistence
    └── saveProgress()
```

#### 2. Database Schema
```sql
interview_progress
├── case_file_id (FK)
├── current_step (Integer)
├── completed_steps (JSON)
├── step_data (JSON)
└── last_activity (Timestamp)

interview_questions
├── case_file_id (FK)
├── question_text (Text)
├── expected_response_type (Enum)
├── multiple_choice_options (JSON)
├── question_order (Integer)
└── is_answered (<PERSON><PERSON><PERSON>)
```

### Step Management

#### 1. Step Definition
```php
protected $steps = [
    'intro' => 'Introduction',
    'case_identification' => 'Case Identification',
    'parties' => 'Parties Involved',
    'overview' => 'Case Overview',
    'ai_questions' => 'Specific Details',
    'outcome' => 'Desired Outcome',
];
```

#### 2. Progress Tracking
- Uses JSON structure for completed_steps
- Maintains state across sessions
- Validates step completion
- Prevents skipping required steps

### Data Architecture

#### 1. Interview Data Structure
```php
protected $interviewData = [
    'case_identification' => [
        'has_active_case' => null,
        'user_role' => null,
        'case_number' => null,
        'considering_legal_action' => false,
        'threatened_with_legal_action' => false,
        'information_gathering' => false,
    ],
    'parties' => [],
    'overview' => ['text' => ''],
    'ai_questions' => [],
    'desired_outcome' => '',
];
```

#### 2. Validation Rules
```php
protected $rules = [
    'interviewData.case_identification.has_active_case' => 'required|boolean',
    'interviewData.case_identification.user_role' => 'nullable|required_if:interviewData.case_identification.has_active_case,1|in:plaintiff,defendant',
    // ... additional rules
];
```

### UI Implementation

#### 1. Component Structure
```blade
interview-component.blade.php
├── Progress Indicator
│   ├── Desktop Version
│   └── Mobile Version
├── Step Content
│   ├── Dynamic Step Loading
│   └── Conditional Rendering
└── Navigation Controls
```

#### 2. Progress Visualization
- Desktop: Full step labels with completion indicators
- Mobile: Compact dots with current step highlight
- Responsive design adaptation

### State Management

#### 1. Persistence Layer
```php
protected function saveProgress()
{
    // Updates or creates CaseFile
    // Updates InterviewProgress
    // Maintains step completion state
    // Preserves interview data
}
```

#### 2. Session Handling
- Automatic progress saving
- State recovery on page reload
- Step validation before progression

### AI Integration Points

#### 1. Question Generation
```php
POST /api/interview/generate-questions/{caseFileId}
├── Analyzes case overview
├── Generates relevant questions
└── Stores in interview_questions table
```

#### 2. Response Processing
- Question tracking system
- Answer validation
- Progress monitoring

### Error Handling

#### 1. Validation Errors
- Step-specific validation
- User feedback
- Progress protection

#### 2. AI Integration Errors
- Fallback mechanisms
- Error logging
- User notifications

## Integration Guidelines

### Adding New Steps
1. Add step definition to `$steps` array
2. Create corresponding view section
3. Add validation rules
4. Update progress tracking
5. Implement step-specific logic

### Extending AI Integration
1. Use existing API endpoints
2. Follow question generation pattern
3. Implement response handling
4. Update progress tracking

### UI Modifications
1. Maintain responsive design
2. Follow existing component structure
3. Use established validation patterns
4. Preserve progress indication

## Development Workflow
1. Local development setup
2. Feature branch creation
3. Component implementation
4. Integration testing
5. Progress persistence verification

## Testing Requirements
1. Step navigation
2. Data persistence
3. Validation rules
4. AI integration
5. Error handling

## Security Considerations
1. Data validation
2. User authorization
3. Progress protection
4. API security
5. Error handling

This architecture provides the foundation for completing the interview implementation while maintaining consistency with existing patterns and structures.
