## Proposed Development Plan: Implementing AI-Generated Question Presentation and Answering

This plan outlines the steps for a developer to implement the section of the interview process where AI-generated questions are presented to the user and their answers are recorded. It assumes the developer has followed previous steps and is familiar with the project up to this point.

**Phase: Implementing AI-Generated Question Presentation and Answering**

**1. Frontend Implementation (Presenting Questions):**

* **Package Initial Interview Questions:**
    * Ex:
```
Based on your project files, here's how I'd approach packaging the preliminary information for the AI assistant in step five:

## AI Analysis Data Package Structure

For the AI to generate relevant follow-up questions, we'll need to send a structured package containing all the information collected so far:

````json path=example-ai-request-payload.json mode=EDIT
{
  "case_identification": {
    "has_active_case": true,
    "user_role": "plaintiff",
    "case_number": "CV-2023-12345",
    "considering_legal_action": false,
    "threatened_with_legal_action": false,
    "information_gathering": false
  },
  "parties": [
    {
      "id": 123,
      "name": "<PERSON>",
      "relationship": "opposing party"
    },
    {
      "id": 456,
      "name": "Acme Corporation",
      "relationship": "employer"
    }
  ],
  "overview": {
    "text": "I was terminated from my position at Acme Corporation after reporting unsafe working conditions..."
  },
  "documents": [
    {
      "id": 789,
      "filename": "termination-letter.pdf",
      "content": "...[extracted text content]...",
      "summary": "Formal termination letter dated June 15, 2023"
    }
  ],
  "case_types": ["employment", "whistleblower"],
  "date_of_incident": "2023-06-15",
  "prompt_instructions": "Based on this information about an employment case, generate 5-7 specific follow-up questions that would help clarify the legal issues and strengthen the case. Focus on gathering details about the timeline of events, documentation of unsafe conditions, and any potential retaliation."
}
````

This approach:

1. Includes all structured data from the interview steps completed so far
2. Provides document content/summaries for context
3. Includes clear instructions for the AI about what kind of questions to generate
4. Maintains the JSON structure you're already using in your database

The AI would then analyze this information and return a structured list of follow-up questions that would be presented to the user in the interview interface.


* **Fetch Questions:**
    * Create a UI component (e.g., a page or section) dedicated to displaying and answering AI-generated questions.
    * Implement functionality within this component to fetch the list of AI-generated questions for the current `case_file` from the backend using an API call (e.g., `/api/interview/questions/{case_file_id}`).
    * The backend will retrieve these questions from the `interview_questions` table.
* **Display Questions:**
    * Render each question individually to the user in a clear and organized manner (e.g., using a numbered list or presenting one question at a time with navigation).
* **Determine Input Type:**
    * For each question, determine the appropriate input type based on the expected response. This can be inferred from the question text or, ideally, from an `expected_response_type` column in the `interview_questions` table.
* **Input Fields:** Render the corresponding input field for each question:
    * **Text Input:** `<textarea>` or `<input type="text">` for narrative or short text answers.
    * **Voice Input Component:** Integrate the existing voice message input component (refer to its implementation details).
    * **Document Uploader:** For questions requiring document uploads. **Reference the `add-communication-form` component for UI and functionality.** This likely involves:
        * A file input element (`<input type="file">`).
        * Visual feedback for selected files.
        * (Optional) Progress indicators for uploads.
    * **Date Picker:** Use a suitable date picker UI component if the question requires a date.
    * **Multiple Choice/Dropdown:** Render `<select>` or radio buttons if the question implies a limited set of options.
* **Navigation and Progress:**
    * Implement navigation controls (e.g., "Next," "Previous," or a list of questions) to allow the user to move through the questions.
    * Display a progress indicator showing the number of answered questions versus the total number of questions.

**2. Backend Implementation (Handling Answers):**

* **API Endpoint for Saving Answers:**
    * Create a new API endpoint (e.g., `POST /api/interview/answer`) to handle the submission of user answers.
    * This endpoint should accept the following data in the request body (likely as JSON):
        ```json
        {
          "case_file_id": <bigint>,
          "interview_question_id": <bigint>,
          "answer_text": <string|null>,
          "document_id": <bigint|null>
        }
        ```
* **Saving Text/Voice Answers:**
    * When the backend receives a request to this endpoint with `answer_text` (and `document_id` as null), it should:
        * Validate the `case_file_id` and `interview_question_id`.
        * Create a new record in the `interview_answers` table with the provided `case_file_id`, `interview_question_id`, and `answer_text`.
* **Handling Document Uploads:**
    * When the user uploads a document as an answer, the frontend should:
        * Use a form with `enctype="multipart/form-data"` to upload the file to a dedicated document upload endpoint (re-use the logic from the `add-communication-form`).
        * Upon successful upload, the backend will save the document details in the `documents` table and return the `document_id`.
        * The frontend should then make a call to the `/api/interview/answer` endpoint, including the `case_file_id`, `interview_question_id`, and the obtained `document_id`. The `answer_text` can be null or contain a note like "Document uploaded."
* **Error Handling:** Implement robust error handling for API requests and database operations.

**3. Frontend Interaction (Saving Answers):**

* **"Save Answer" Button (or similar):** Provide a button or mechanism for the user to indicate they have finished answering a question.
* **AJAX Request:** When the user saves an answer, the frontend should make an asynchronous request (e.g., using `Workspace` or `XMLHttpRequest`) to the backend's `/api/interview/answer` endpoint with the relevant data.
* **Visual Feedback:** Display a success message or update the UI to indicate that the answer has been saved.

**4. Handling Completion:**

* **"Done Answering" Button:** Implement a button (e.g., "Finish Questions") that the user can click once they have answered all the questions they intend to.
* **Trigger Next Phase:** When this button is clicked, the frontend should make a request to the backend to trigger the next phase of the interview workflow (e.g., initiating Phase 6 for deeper AI analysis or moving to Phase 7 for final questions). This could involve updating the `interview_status` in the `case_files` table.

**Developer Notes and References:**

* **`expected_response_type` Column:** Strongly consider adding an `expected_response_type` column to the `interview_questions` table to explicitly define the expected input type (e.g., "text", "voice", "document", "date", "multiple_choice"). This will simplify frontend rendering of input fields.
* **Re-use `add-communication-form`:**
    * **Frontend:** Examine the UI elements and JavaScript logic used in the `add-communication-form` for handling file selection, display, and potential progress indicators. Adapt these for the interview question document upload.
    * **Backend:** Re-use the existing API endpoint and logic for handling file uploads, including storage and creating records in the `documents` table. You will need to modify the part where the document is linked to a communication to link it to an `interview_answer` record instead.
* **State Management:** On the frontend, manage the state of the current question being displayed and the answers provided by the user (before they are saved to the backend).
* **Validation:** Implement basic frontend validation to ensure users provide some input for required questions. Backend validation is also crucial for data integrity.
* **User Experience:** Ensure a smooth and intuitive user experience. Provide clear instructions or hints if needed for specific question types.

This detailed plan should provide a clear path for the developer to implement the AI-generated question presentation and answering functionality. Remember to break down the tasks and test each part thoroughly.
