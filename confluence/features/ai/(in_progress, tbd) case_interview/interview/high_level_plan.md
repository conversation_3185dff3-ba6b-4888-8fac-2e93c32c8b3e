# Actionable Plan: Building the AI-Powered Legal Interview Feature

This plan outlines the steps to build an interactive interview feature for your legal assistant, leveraging existing components like the document uploader, voice message input, party directory, and OpenAI assistant.

___
**PURPOSE OF THIS FEATURE:** 

we would like to get to a point where AI can easily construct up a strategy, or draft a legal document for a case based on the details and the facts of the particular case. In order to do that, we need to know the case inside and out. This feature will help us gather all the necessary information about a case in a structured manner.
___
## I. Overall Plan Overview

The interview feature will guide users through a structured process to provide essential information about their legal situation. It will incorporate AI at key stages to dynamically generate follow-up questions and analyze the gathered information. The workflow is designed to be user-friendly, even for those with limited legal knowledge, and will utilize existing components to streamline data input.

The core phases of the interview are:

1.  **Initial Case Identification and User Role:** Establish the basic context of the legal matter (active case, suing/being sued, etc.).
2.  **Identifying Involved Parties:** Capture information about all relevant individuals and entities, leveraging the party directory.
3.  **Brief Overview and Document Upload:** Allow the user to describe the situation in their own words and upload relevant documents.
4.  **AI Intervention - Initial Analysis and Framework Generation:** The AI analyzes the initial information and generates a tailored set of follow-up questions.
5.  **User Answers AI-Generated Questions:** The user responds to the AI's questions using flexible input methods.
6.  **AI Intervention - Deeper Analysis and Potential Follow-Up:** (Optional) The AI performs a more in-depth analysis and may generate further questions.
7.  **Explicit Desired Outcome and Final Information:** Gather the user's goals and any additional relevant details.

## II. Detailed Actionable Plan

**Phase 1: Initial Case Identification and User Role (Structured Input)**

* **Task 1.1:** Create UI elements for the following questions with appropriate input types (radio buttons, text input):
    * "Is there an active legal case currently ongoing?" (Yes/No)
    * If Yes:
        * "Are you the one who initiated the case (Plaintiff/Petitioner), or are you responding to a case someone else filed against you (Defendant/Respondent)?" (Multiple Choice)
        * "What is the case name or number, if you know it?" (Text Input)
    * If No:
        * "Are you considering taking legal action (suing someone)?" (Yes/No)
        * "Is someone threatening to take legal action against you?" (Yes/No)
        * "Are you just looking to document information or understand your legal options regarding a situation that hasn't yet reached the point of a lawsuit?" (Yes/No)

**Phase 2: Identifying Involved Parties (Integration of Party Directory)**

* **Task 2.1:** Integrate the "party directory component" into the interview flow.
* **Task 2.2:** Implement a search bar that queries the user's party directory. Display search results dynamically.
* **Task 2.3:** Allow users to select existing parties from the directory. Upon selection, relevant information should be pre-filled (name, etc.).
* **Task 2.4:** Provide a clear option to "Add New Person/Entity".
* **Task 2.5:** When a new party is added, present fields for:
    * "What is their full name?" (Text Input)
    * "What is their relationship to you in this situation?" (Dropdown or Text Input)
    * **(Optional):** Include fields for contact details and an option to save the new party to the directory.

**Phase 3: Brief Overview and Document Upload (Open-Ended & Document Uploader)**

* **Task 3.1:** Embed the "voice message input component" with a text area, voice recorder, and file uploader.
* **Task 3.2:** Display the instruction: "Please provide a brief overview of what happened in your own words. Feel free to type or use the voice recording feature below."
* **Task 3.3:** Display the prompt for the document uploader: "Do you have any documents that are related to this situation?..." (Link this to your existing document uploader).

**Phase 4: AI Intervention - Initial Analysis and Framework Generation**

* **Task 4.1:** Set up the backend logic to trigger the case-specific OpenAI assistant after the user completes Phase 3 (provides overview and uploads documents or indicates none).
* **Task 4.2:** Construct the prompt to send to the assistant. Ensure it includes:
    * The transcribed text from the voice message input (if used).
    * The text from the text area (if used).
    * A list of IDs or references for any uploaded documents.
    * Clear instructions for the AI to identify key aspects and generate a list of specific follow-up questions, along with the type of information each question seeks.
* **Task 4.3:** Develop the frontend logic to receive the list of questions from the AI.
* **Task 4.4:** Design the UI to display these questions to the user, potentially with a mechanism to track answered questions (e.g., checkboxes).

**Phase 5: User Answers AI-Generated Questions (Flexible Input)**

* **Task 5.1:** For each question received from the AI, dynamically create appropriate input fields:
    * Short text input for concise answers.
    * Longer text area with the "voice message input component" for detailed explanations.
    * Multiple choice or dropdown menus if the AI suggests questions with limited options.
    * Potentially a document upload component if the AI requests specific documents.
* **Task 5.2:** Implement functionality to track which of the AI-generated questions have been answered by the user.

**Phase 6: AI Intervention - Deeper Analysis and Potential Follow-Up (Optional)**

* **Task 6.1:** Determine if and when to trigger a second AI analysis (e.g., after all questions from Phase 5 are answered).
* **Task 6.2:** Construct the prompt for this deeper analysis, including:
    * The initial overview.
    * The user's answers to the AI-generated questions.
    * References to uploaded documents.
    * Instructions for the AI to identify potential legal issues, remaining information gaps, and generate further follow-up questions.
    * **(Internal Analysis Only):** Request the AI to suggest a preliminary desired outcome.
* **Task 6.3:** Develop the frontend logic to display any new follow-up questions to the user (similar to Phase 4).

**Phase 7: Explicit Desired Outcome and Final Information (Structured & Open-Ended)**

* **Task 7.1:** Create a text area with the "voice message input component" for the question: "What is it that you are hoping to achieve by pursuing this matter?"
* **Task 7.2:** Create another text area with the "voice message input component" for the question: "Is there anything else you think is important for me to know about this situation?"

## III. Key Considerations Throughout Development

* **Prompt Engineering:** Continuously refine the prompts sent to the OpenAI assistant to optimize the quality and relevance of the generated questions and analysis.
* **Context Window Management:** Be mindful of the context window limitations of the OpenAI model and ensure relevant information is included in the prompts without exceeding the limit.
* **Error Handling:** Implement robust error handling for all API calls to OpenAI and for user input.
* **User Experience (UX):** Design the interview flow to be intuitive and easy to navigate for users with varying levels of technical and legal knowledge. Provide clear instructions and progress indicators.
* **Data Storage:** Ensure all collected information is securely stored and linked to the specific case.
* **Testing and Iteration:** Thoroughly test each phase of the interview process and iterate on the design and functionality based on feedback and observations.
* **Disclaimer:** Clearly state that the AI assistant is not providing legal advice and that the information gathered is for informational purposes to assist in case management.

By following this plan, you can systematically build a powerful and efficient AI-powered interview feature for your legal assistant. Remember to prioritize tasks and iterate based on your development capacity and user feedback.
