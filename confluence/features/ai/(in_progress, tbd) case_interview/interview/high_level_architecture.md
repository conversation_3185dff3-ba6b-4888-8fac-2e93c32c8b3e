
## Architecture Diagram & Order of Data Collection: AI-Powered Legal Interview Feature
___
### **Case Creation:**
minimal information required to create a new case file

```
    
    - Title (already exists)
    - (If Applicable) Date of Incident/Issue 
    - (If Applicable) Case Number/Reference number
    - Desired Outcome (already exists but not required [yet] )
    - Case Type/Category (multi-select)
        - Contract Law: Deals with agreements between two or more parties, including the formation, interpretation, and enforcement of contracts (e.g., buying a car, signing a lease, employment agreements).
        - Tort Law: Addresses civil wrongs that cause harm or injury to another person, leading to potential liability (e.g., negligence, personal injury, defamation).
        - Property Law: Concerns the rights and responsibilities associated with owning, using, and transferring real property (land and buildings) and personal property (movable possessions).
        - Family Law: Governs legal relationships within families, including marriage, divorce, child custody, child support, adoption, and domestic violence.
        - Criminal Law: Deals with actions that are considered offenses against the public or the state, punishable by fines, imprisonment, or other penalties.
        - Employment Law: Regulates the relationship between employers and employees, covering areas like hiring, wages, working conditions, discrimination, and termination.
        - Consumer Law: Protects individuals who purchase goods and services for personal, family, or household use from unfair or deceptive practices.
        - Constitutional Law: Interprets and applies the U.S. Constitution, defining the structure of government and protecting fundamental rights and freedoms.
        - Administrative Law: Governs the actions and decisions of administrative agencies of the government (federal, state, and local), such as the Social Security Administration or the Environmental Protection Agency.
        - Environmental Law: Regulates human interaction with the environment, aiming to protect natural resources and prevent pollution.
        - Immigration Law: Deals with the legal rights and obligations of foreign nationals entering and residing in the United States.
        - Estate Planning and Probate Law: Concerns the management and distribution of a person's assets after their death, including wills, trusts, and the probate process.
        - Bankruptcy Law: Provides a legal process for individuals and businesses that cannot pay their debts to seek relief.
        - Intellectual Property Law: Protects creations of the mind, such as inventions (patents), literary and artistic works (copyrights), and brands (trademarks).
    - Brief Initial Summary (to be created)
    
```
___

## Explanation of the Diagram and Interview Question Flow:

1. **User Starts Interview:** The user initiates the interview process within the application.
2.  **Application Frontend: Initial Structured Questions:** The application presents the first set of pre-defined questions (e.g., case status, user role).
3.  **User Answers Initial Questions:** The user provides their responses to these structured questions.
4.  **Application Backend Stores in Database:** The application backend stores these initial answers in the `case_files` table.
5.  **Application Frontend: Brief Overview & Document Upload Prompt:** The user is then prompted to provide a free-form overview of their situation and upload any relevant documents.
6.  **User Provides Overview & Uploads Documents:** The user inputs their overview (text or voice) and uploads documents.
7.  **Application Backend Stores in Database:** The overview is stored in the `case_summaries` table (or a dedicated column in `case_files`), and the uploaded documents are stored in the `documents` table, linked to the `case_file`.
8.  **Application Backend Triggers OpenAI Assistant:** Once the initial user input is gathered, the backend triggers the case-specific OpenAI assistant.
9.  **Application Backend Creates/Uses OpenAI Thread:** The backend either creates a new OpenAI thread or uses an existing one associated with the `case_file`, storing the `openai_thread_id` in the `case_files` table.
10. **Application Backend Sends to OpenAI Assistant API:** The backend sends the user's overview (transcribed text) and references to the uploaded documents to the OpenAI Assistant API, along with a prompt to generate relevant questions.
11. **OpenAI Assistant Analyzes Information:** The OpenAI assistant analyzes the provided information within the context of the thread history.
12. **OpenAI Assistant Generates Question Framework:** Based on its analysis, the AI generates a framework of specific questions it needs answered.
13. **Application Backend Receives Question Framework:** The backend receives this list of questions from the OpenAI Assistant API.
14. **Application Backend Stores in Database:** The backend stores each of these AI-generated questions in the `interview_questions` table, linking them to the specific `case_file`.
15. **Application Frontend Displays AI-Generated Questions:** The application frontend retrieves the questions from the `interview_questions` table and displays them to the user.
16. **User Answers AI-Generated Questions:** The user responds to each of the AI's questions.
17. **Application Backend Stores in Database:** The user's answers are stored in the `interview_answers` table, linked to the corresponding `interview_question` and `case_file`. If a document is uploaded as an answer, its ID is also stored.
18. **(Optional) Further AI Intervention:** The backend can optionally send the user's answers back to the OpenAI Assistant within the same thread for further analysis, potentially leading to more dynamic follow-up questions (looping back to steps 11-14).
19. **Application Frontend Displays Final Questions:** After the AI-driven questioning (or if no further questions are needed), the application presents the final set of pre-defined questions (e.g., desired outcome, any other important information).
20. **User Answers Final Questions:** The user provides their responses.
21. **Application Backend Stores in Database:** These final answers are stored in the `case_files` table.

This diagram should clarify the flow, especially how the AI generates questions after the initial information gathering and how these questions and their answers are managed within your database. The OpenAI assistant itself manages the logic of question generation and maintains the conversational context within its thread. Your application acts as the intermediary to facilitate the interaction and persist the relevant data.
