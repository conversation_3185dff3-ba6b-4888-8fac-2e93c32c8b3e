# Case Research System (CRS) - MVP Implementation Plan

Here's an actionable plan to implement a proof-of-concept CRS within your existing Justice Quest system:

## 1. Database Migration & Models

```php
// Create research_sessions table
Schema::create('research_sessions', function (Blueprint $table) {
    $table->id();
    $table->foreignId('case_id')->constrained();
    $table->string('status'); // initiated, researching, completed, failed
    $table->json('case_summary'); // Extracted case details
    $table->json('legal_issues'); // Identified legal issues
    $table->json('desired_outcome'); // User's desired outcome
    $table->json('research_results')->nullable(); // Final research output
    $table->text('error_message')->nullable();
    $table->timestamps();
});

// Create legal_references table
Schema::create('legal_references', function (Blueprint $table) {
    $table->id();
    $table->foreignId('research_session_id')->constrained();
    $table->string('type'); // case, statute, regulation, article
    $table->string('title');
    $table->text('summary');
    $table->text('citation');
    $table->text('url')->nullable();
    $table->json('relevance_data'); // Why this reference matters
    $table->decimal('relevance_score', 5, 2);
    $table->timestamps();
});
```

## 2. Case Information Extraction Service

```php
namespace App\Services\CRS;

class CaseExtractionService
{
    protected $openAiService;
    protected $vectorStore;
    
    public function __construct(OpenAiService $openAiService, VectorStore $vectorStore)
    {
        $this->openAiService = $openAiService;
        $this->vectorStore = $vectorStore;
    }
    
    public function extractCaseInformation($caseId)
    {
        // Get all case documents from vector store
        $documents = $this->vectorStore->getDocumentsByCaseId($caseId);
        
        // Extract structured case information using GPT-4.1-mini
        $prompt = $this->buildExtractionPrompt($documents);
        $response = $this->openAiService->completion($prompt);
        
        // Parse structured JSON response
        return json_decode($response, true);
    }
    
    private function buildExtractionPrompt($documents)
    {
        // Build prompt that requests key case information in JSON format:
        // - Parties involved
        // - Key facts
        // - Causes of action / claims
        // - Jurisdiction
        // - Timeline of events
        // Use actual document content as context
    }
}
```

## 3. Legal Issue Identifier Service

```php
namespace App\Services\CRS;

class LegalIssueIdentifierService
{
    protected $openAiService;
    
    public function identifyLegalIssues($caseInformation, $desiredOutcome)
    {
        // Create prompt for AI to identify key legal issues
        $prompt = $this->buildIssueIdentificationPrompt($caseInformation, $desiredOutcome);
        $response = $this->openAiService->completion($prompt);
        
        // Parse and return structured issues
        return json_decode($response, true);
    }
    
    private function buildIssueIdentificationPrompt($caseInformation, $desiredOutcome)
    {
        // Build prompt that asks the AI to:
        // - Identify the core legal issues in dispute
        // - Map issues to relevant legal domains (contract, tort, etc.)
        // - Consider the desired outcome when identifying issues
        // Request response in structured JSON format
    }
}
```

## 4. Research Worker - Free API Integration

```php
namespace App\Services\CRS;

class LegalResearchService
{
    protected $openAiService;
    
    // Define free legal resource APIs
    protected $legalResources = [
        'courtlistener' => 'https://www.courtlistener.com/api/rest/v3/',
        'cornell_lii' => 'https://www.law.cornell.edu/supct/search',
        'google_scholar' => 'https://scholar.google.com/scholar',
        'findlaw' => 'https://www.findlaw.com/'
    ];
    
    public function performResearch($legalIssues, $jurisdiction)
    {
        $results = [];
        
        foreach ($legalIssues as $issue) {
            // Build search queries for the issue
            $searchQueries = $this->buildSearchQueries($issue, $jurisdiction);
            
            // Search across free legal resources
            $issueResults = [];
            foreach ($searchQueries as $query) {
                // Get results from Court Listener (example)
                $courtListenerResults = $this->searchCourtListener($query, $jurisdiction);
                $issueResults = array_merge($issueResults, $courtListenerResults);
                
                // Could add other free resources here in the full implementation
            }
            
            // Filter and deduplicate results
            $filteredResults = $this->filterRelevantResults($issueResults, $issue);
            $results[$issue['id']] = $filteredResults;
        }
        
        return $results;
    }
    
    private function searchCourtListener($query, $jurisdiction)
    {
        // Use GuzzleHttp to query Court Listener API
        // Format and return results
    }
    
    private function buildSearchQueries($issue, $jurisdiction)
    {
        // Use GPT to generate effective legal search queries
        // based on the issue and jurisdiction
    }
    
    private function filterRelevantResults($results, $issue)
    {
        // Use GPT to evaluate and rank results by relevance
        // Return only the most relevant results
    }
}
```

## 5. Argument Construction Service

```php
namespace App\Services\CRS;

class ArgumentConstructionService
{
    protected $openAiService;
    
    public function buildLegalArguments($caseInfo, $legalIssues, $researchResults, $desiredOutcome)
    {
        $arguments = [];
        
        foreach ($legalIssues as $issue) {
            $issueId = $issue['id'];
            $relevantPrecedents = $researchResults[$issueId] ?? [];
            
            // Build case theory for this issue
            $prompt = $this->buildArgumentPrompt($issue, $relevantPrecedents, $caseInfo, $desiredOutcome);
            $response = $this->openAiService->completion($prompt);
            
            // Parse and add to arguments collection
            $arguments[$issueId] = json_decode($response, true);
        }
        
        return $arguments;
    }
    
    private function buildArgumentPrompt($issue, $precedents, $caseInfo, $desiredOutcome)
    {
        // Create prompt that:
        // - Asks GPT to build legal arguments supporting desired outcome
        // - References the precedents found
        // - Requests counter-arguments and responses
        // - Asks for applicable citations
    }
}
```

## 6. Controller & Job Queuing

```php
namespace App\Http\Controllers;

use App\Jobs\ResearchJob;
use App\Models\ResearchSession;
use Illuminate\Http\Request;

class CaseResearchController extends Controller
{
    public function initiateResearch(Request $request, $caseId)
    {
        // Validate request
        $request->validate([
            'desired_outcome' => 'required|string'
        ]);
        
        // Create research session
        $session = ResearchSession::create([
            'case_id' => $caseId,
            'status' => 'initiated',
            'desired_outcome' => json_encode(['description' => $request->desired_outcome])
        ]);
        
        // Queue research job
        ResearchJob::dispatch($session->id)->onQueue('research');
        
        return response()->json([
            'message' => 'Research initiated',
            'session_id' => $session->id
        ]);
    }
    
    public function getResearchStatus($sessionId)
    {
        $session = ResearchSession::findOrFail($sessionId);
        return response()->json($session);
    }
    
    public function getResearchResults($sessionId)
    {
        $session = ResearchSession::findOrFail($sessionId);
        $results = $session->research_results;
        
        if ($session->status !== 'completed') {
            return response()->json([
                'message' => 'Research not yet completed',
                'status' => $session->status
            ], 202);
        }
        
        return response()->json([
            'results' => $results,
            'references' => $session->legalReferences
        ]);
    }
}
```

## 7. Research Job Implementation

```php
namespace App\Jobs;

use App\Models\ResearchSession;
use App\Services\CRS\CaseExtractionService;
use App\Services\CRS\LegalIssueIdentifierService;
use App\Services\CRS\LegalResearchService;
use App\Services\CRS\ArgumentConstructionService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ResearchJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;
    
    protected $sessionId;
    
    public function __construct($sessionId)
    {
        $this->sessionId = $sessionId;
    }
    
    public function handle(
        CaseExtractionService $extractionService,
        LegalIssueIdentifierService $issueService,
        LegalResearchService $researchService,
        ArgumentConstructionService $argumentService
    ) {
        try {
            // Get session
            $session = ResearchSession::findOrFail($this->sessionId);
            $session->update(['status' => 'researching']);
            
            // Step 1: Extract case information from documents
            $caseInfo = $extractionService->extractCaseInformation($session->case_id);
            $session->update(['case_summary' => $caseInfo]);
            
            // Step 2: Identify legal issues
            $desiredOutcome = json_decode($session->desired_outcome, true);
            $legalIssues = $issueService->identifyLegalIssues($caseInfo, $desiredOutcome);
            $session->update(['legal_issues' => $legalIssues]);
            
            // Step 3: Perform legal research
            $jurisdiction = $caseInfo['jurisdiction'] ?? 'unknown';
            $researchResults = $researchService->performResearch($legalIssues, $jurisdiction);
            
            // Step 4: Store references
            $this->storeReferences($session, $researchResults);
            
            // Step 5: Build legal arguments
            $arguments = $argumentService->buildLegalArguments(
                $caseInfo, 
                $legalIssues, 
                $researchResults, 
                $desiredOutcome
            );
            
            // Step 6: Complete research
            $finalResults = [
                'issues' => $legalIssues,
                'arguments' => $arguments,
                'case_summary' => $caseInfo
            ];
            
            $session->update([
                'status' => 'completed',
                'research_results' => $finalResults
            ]);
        } catch (\Exception $e) {
            // Handle failure
            $session->update([
                'status' => 'failed',
                'error_message' => $e->getMessage()
            ]);
        }
    }
    
    private function storeReferences($session, $researchResults)
    {
        // Flatten and store all references
        $allReferences = [];
        foreach ($researchResults as $issueId => $results) {
            foreach ($results as $result) {
                $session->legalReferences()->create([
                    'type' => $result['type'],
                    'title' => $result['title'],
                    'summary' => $result['summary'],
                    'citation' => $result['citation'],
                    'url' => $result['url'] ?? null,
                    'relevance_data' => json_encode($result['relevance']),
                    'relevance_score' => $result['score']
                ]);
            }
        }
    }
}
```

## 8. Routes & Livewire Component

```php
// Routes
Route::post('/cases/{caseId}/research', [CaseResearchController::class, 'initiateResearch'])->name('cases.research.initiate');
Route::get('/research/{sessionId}/status', [CaseResearchController::class, 'getResearchStatus'])->name('research.status');
Route::get('/research/{sessionId}/results', [CaseResearchController::class, 'getResearchResults'])->name('research.results');

// Livewire Component
namespace App\Http\Livewire;

use Livewire\Component;
use App\Models\Case;
use App\Models\ResearchSession;

class CaseResearch extends Component
{
    public $case;
    public $desiredOutcome;
    public $researchSessionId;
    public $researchStatus;
    public $researchResults;
    
    public function mount($caseId)
    {
        $this->case = Case::findOrFail($caseId);
        $this->researchStatus = 'not_started';
    }
    
    public function initiateResearch()
    {
        $response = Http::post(route('cases.research.initiate', ['caseId' => $this->case->id]), [
            'desired_outcome' => $this->desiredOutcome
        ]);
        
        $this->researchSessionId = $response->json('session_id');
        $this->researchStatus = 'initiated';
    }
    
    public function checkStatus()
    {
        if (!$this->researchSessionId) return;
        
        $response = Http::get(route('research.status', ['sessionId' => $this->researchSessionId]));
        $this->researchStatus = $response->json('status');
        
        if ($this->researchStatus === 'completed') {
            $resultsResponse = Http::get(route('research.results', ['sessionId' => $this->researchSessionId]));
            $this->researchResults = $resultsResponse->json('results');
        }
    }
    
    public function render()
    {
        return view('livewire.case-research');
    }
}
```

## Implementation Steps for Developer

1. **Set up database tables**
    - Create migrations for research sessions and legal references
    - Run migrations
    - Create corresponding Eloquent models with relationships

2. **Create the service classes**
    - Implement the extraction service to pull info from existing vector store
    - Build the legal issue identifier service
    - Implement the research service with at least one free API
    - Create the argument construction service

3. **Set up job queue infrastructure**
    - Configure Laravel queue with Redis or database
    - Create the research job class
    - Implement job retry and error handling logic

4. **Build the controller and routes**
    - Create endpoints for initiating research and checking status
    - Wire up proper validation and error handling

5. **Implement the Livewire component**
    - Create blade view with UI for desired outcome input
    - Add status checking with polling
    - Show loading states and results display

6. **Test and iterate**
    - Start with a single simple test case
    - Adjust prompts based on output quality
    - Fine-tune API usage to stay within rate limits

## Free Legal Research APIs for MVP

1. **Court Listener API**
    - Free access with registration
    - Provides federal and state case law
    - Documentation: https://www.courtlistener.com/api/

2. **Legal Information Institute (Cornell)**
    - Web scraping can be implemented (with respect to ToS)
    - Comprehensive legal information and US Code

3. **Google Scholar (Case Law)**
    - Can be accessed programmatically with proper scraping approach
    - Rich source of case law across jurisdictions

4. **FindLaw**
    - State and federal resources
    - Web scraping with proper respect for ToS

For the MVP, focus on integrating just one of these sources thoroughly rather than partial implementation of multiple sources.
