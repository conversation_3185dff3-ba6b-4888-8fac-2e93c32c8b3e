Below is a **lower-level plan** for the MVP implementation of an **issue-based CRS**. It’s structured so you can hand it off to a developer for relatively straightforward execution. I’ve sketched out Laravel migrations, model definitions, Livewire component outlines, and pseudo-code illustrating each step. Adjust field names or structures as needed to fit your existing app conventions.

---

## 1. Migrations & Database Design

### 1.1 `case_issues` Table

```php
Schema::create('case_issues', function (Blueprint $table) {
    $table->id();
    $table->unsignedBigInteger('case_id')->index();
    $table->string('issue_name');          // e.g. 'Breach of Contract'
    $table->text('description')->nullable(); // optional extra notes
    $table->enum('research_status', ['queued','processing','complete','failed'])
          ->default('queued');
    $table->json('research_results_json')->nullable(); // store raw/summarized results
    $table->timestamps();

    // optional foreign key if you have a cases table
    // $table->foreign('case_id')->references('id')->on('cases')->onDelete('cascade');
});
```

### 1.2 Optional: `case_issues_results` Table (If you want a separate table for each retrieved opinion)
```php
Schema::create('case_issues_results', function (Blueprint $table) {
    $table->id();
    $table->unsignedBigInteger('case_issue_id')->index();
    $table->string('case_title')->nullable();      // e.g. "Smith v. Jones"
    $table->string('citation')->nullable();        // e.g. "500 U.S. 123"
    $table->text('summary')->nullable();           // short summary
    $table->json('raw_data')->nullable();          // full text or raw JSON from the source
    $table->boolean('selected')->default(false);   // user can mark if they want to keep it
    $table->timestamps();

    // $table->foreign('case_issue_id')->references('id')->on('case_issues')->onDelete('cascade');
});
```

---

## 2. Models (Eloquent)

### 2.1 `CaseIssue.php`
```php
namespace App\\Models;

use Illuminate\\Database\\Eloquent\\Model;

class CaseIssue extends Model
{
    protected $table = 'case_issues';

    protected $fillable = [
        'case_id',
        'issue_name',
        'description',
        'research_status',
        'research_results_json',
    ];

    protected $casts = [
        'research_results_json' => 'array', // so Laravel converts JSON to array automatically
    ];

    public function results()
    {
        return $this->hasMany(CaseIssueResult::class);
    }
}
```

### 2.2 `CaseIssueResult.php` (Optional if you store individual results)
```php
namespace App\\Models;

use Illuminate\\Database\\Eloquent\\Model;

class CaseIssueResult extends Model
{
    protected $table = 'case_issues_results';

    protected $fillable = [
        'case_issue_id',
        'case_title',
        'citation',
        'summary',
        'raw_data',
        'selected',
    ];

    protected $casts = [
        'raw_data' => 'array', // for storing large JSON from the source
    ];

    public function issue()
    {
        return $this->belongsTo(CaseIssue::class, 'case_issue_id');
    }
}
```

---

## 3. Issue Identification (Optional MVP Step)

If you plan to have the system auto-extract issues from the user’s documents:

### 3.1 Pseudocode for an LLM-based “Issue Detection” Process

```php
public function detectIssuesFromCaseDocuments($case)
{
    // 1. Aggregate text from all documents for this case (or get from vector store)
    $allText = $this->collectCaseText($case->id);

    // 2. Call GPT or other LLM with a prompt to find potential issues
    $prompt = "Given the following text describing a case, list the possible legal issues:
               ---START TEXT---
               {$allText}
               ---END TEXT---
               Provide a JSON array of issues like ['Breach of Contract', 'Negligence'].";
    $llmOutput = $this->callOpenAIGPT($prompt);

    // 3. Parse the JSON array from the LLM output
    $issues = json_decode($llmOutput, true);

    // 4. For each issue, create a CaseIssue record
    foreach($issues as $issueName) {
        CaseIssue::create([
            'case_id' => $case->id,
            'issue_name' => $issueName,
            'research_status' => 'queued',
        ]);
    }
    // Possibly return the list of created issues
    return $issues;
}
```

> For the MVP, you can skip or handle this manually (the user picks the issue).

---

## 4. Enqueue Research Jobs

### 4.1 The `PerformLegalResearchForIssue` Job

**File**: `app/Jobs/PerformLegalResearchForIssue.php`
```php
namespace App\\Jobs;

use App\\Models\\CaseIssue;
use Illuminate\\Bus\\Queueable;
use Illuminate\\Queue\\SerializesModels;
use Illuminate\\Foundation\\Bus\\Dispatchable;
use Illuminate\\Contracts\\Queue\\ShouldQueue;
use Illuminate\\Support\\Facades\\Http;

class PerformLegalResearchForIssue implements ShouldQueue
{
    use Dispatchable, Queueable, SerializesModels;

    protected $issueId;

    public function __construct($issueId)
    {
        $this->issueId = $issueId;
    }

    public function handle()
    {
        $issue = CaseIssue::find($this->issueId);
        if (!$issue) {
            return; // maybe log an error
        }

        // 1. Mark status as processing
        $issue->update(['research_status' => 'processing']);

        // 2. Build Query (very simplistic)
        // For MVP, just use the issue name, or add other facts if you have them.
        $queryString = urlencode($issue->issue_name);

        // 3. Call a free legal API (e.g., CourtListener) for opinions
        // Example: GET /search/?q=<query>&type=o for opinions
        // NOTE: You need to see the official docs for exact endpoints or possibly use the FreedLaw Project's dumps
        $apiUrl = "https://www.courtlistener.com/api/rest/v3/search/?q={$queryString}&type=o";
        $response = Http::get($apiUrl);

        if ($response->successful()) {
            $data = $response->json();

            // 4. Extract top results
            // The structure depends on CourtListener's actual response format. Suppose 'results' is the key:
            $resultsArray = [];
            if (isset($data['results'])) {
                $resultsArray = array_slice($data['results'], 0, 5); // first 5 results for MVP
            }

            // 5. Summaries (optional GPT step)
            $finalResults = [];
            foreach ($resultsArray as $result) {
                // Prepare a summary. MVP might just use snippet from the API if available
                $title = $result['caseName'] ?? 'Unknown Title';
                $summary = $result['snippet'] ?? '';

                // If you want GPT summary:
                // $summary = $this->summarizeWithGPT($result['snippet'] ?? '');

                // Add to final results
                $finalResults[] = [
                    'case_title' => $title,
                    'citation'   => $result['citation'] ?? '',
                    'summary'    => $summary,
                    'raw_data'   => $result,
                ];
            }

            // 6. Save results
            if (!empty($finalResults)) {
                // Option A: if storing in results_json
                $issue->update([
                    'research_results_json' => $finalResults
                ]);

                // Option B: store in case_issues_results table
                // foreach ($finalResults as $r) {
                //     $issue->results()->create($r);
                // }
            }
        } else {
            // If fails
            // maybe set a failure message
            $issue->update(['research_status' => 'failed']);
            return;
        }

        // 7. Mark complete
        $issue->update(['research_status' => 'complete']);
    }

    // protected function summarizeWithGPT($text) {
    //     // If you have a GPT service
    //     // Pseudo code
    //     // $prompt = "Summarize this text in 1-2 sentences: {$text}";
    //     // $gptResponse = SomeAiService::generate($prompt);
    //     // return $gptResponse;
    // }
}
```

> You’ll adapt the code to CourtListener’s actual response. If no official, stable API is free, you might mock the data or use something else (e.g., FreedLaw CSV data, etc.).

---

## 5. Triggers & UI

### 5.1 Livewire Component: `CaseIssuesDashboard`

**Goal**: Display all issues for a given case, let user queue or re-queue research, show results.

Pseudo-code:

```php
namespace App\\Http\\Livewire;

use Livewire\\Component;
use App\\Models\\CaseIssue;
use App\\Jobs\\PerformLegalResearchForIssue;

class CaseIssuesDashboard extends Component
{
    public $caseId;

    public function mount($caseId)
    {
        $this->caseId = $caseId;
    }

    public function render()
    {
        // fetch from DB
        $issues = CaseIssue::where('case_id', $this->caseId)->get();

        return view('livewire.case-issues-dashboard', [
            'issues' => $issues,
        ]);
    }

    public function queueResearch($issueId)
    {
        $issue = CaseIssue::find($issueId);
        if ($issue) {
            // reset status to queued
            $issue->update(['research_status' => 'queued']);
            // dispatch job
            PerformLegalResearchForIssue::dispatch($issue->id);
        }
    }
}
```

**Blade template** (sketch):
```html
<div>
    <h2>Issues for Case #{{ $caseId }}</h2>
    <table>
        <thead>
            <tr><th>Issue Name</th><th>Status</th><th>Actions</th></tr>
        </thead>
        <tbody>
            @foreach($issues as $issue)
            <tr>
                <td>{{ $issue->issue_name }}</td>
                <td>{{ $issue->research_status }}</td>
                <td>
                    @if($issue->research_status !== 'complete')
                        <button wire:click="queueResearch({{ $issue->id }})">Queue Research</button>
                    @endif
                    <a href="{{ route('issue-details', $issue->id) }}">View Details</a>
                </td>
            </tr>
            @endforeach
        </tbody>
    </table>
</div>
```

### 5.2 A Second Component: `CaseIssueDetails`

**Goal**: Show the research results for one `CaseIssue`.

```php
namespace App\\Http\\Livewire;

use Livewire\\Component;
use App\\Models\\CaseIssue;

class CaseIssueDetails extends Component
{
    public $issueId;
    public $issue;

    public function mount($issueId)
    {
        $this->issueId = $issueId;
        $this->issue = CaseIssue::findOrFail($issueId);
    }

    public function render()
    {
        return view('livewire.case-issue-details', [
            'results' => $this->getResults(),
        ]);
    }

    protected function getResults()
    {
        // Option A: stored as JSON in the model
        return $this->issue->research_results_json ?? [];

        // Option B: retrieve from case_issues_results table
        // return $this->issue->results;
    }
}
```

**Blade template**:
```html
<div>
    <h2>{{ $issue->issue_name }} - Details</h2>
    <p>Status: {{ $issue->research_status }}</p>

    <div>
        @foreach($results as $result)
            <div style="border:1px solid #ccc; margin:1em 0;">
                <h3>{{ $result['case_title'] ?? 'No title' }}</h3>
                <p><strong>Citation:</strong> {{ $result['citation'] ?? '' }}</p>
                <p>{{ $result['summary'] }}</p>
                <!-- link to full text if you have it: e.g. $result['raw_data'] or a CourtListener URL -->
            </div>
        @endforeach
    </div>
</div>
```

---

## 6. Draft/Strategy Outline (MVP)

### 6.1 Button to Generate a Strategy Outline

Within `CaseIssueDetails` or a higher-level “Strategy” component, you can add a button: “Generate Outline.” On click, you might do:

```php
public function generateStrategyOutline()
{
    // Gather relevant data
    $issue = $this->issue;
    $results = $this->getResults();

    // MVP approach: build a simple text
    $points = [];
    foreach($results as $r) {
        $points[] = "- {$r['case_title']}: {$r['summary']}";
    }
    $outline = "Strategy Outline for Issue: {$issue->issue_name}\n\n".
               "1. Facts relevant:\n   (user to fill)\n".
               "2. Law discovered:\n".implode("\n", $points)."\n".
               "3. Potential arguments:\n   - ...\n";

    // Save to DB or store in a component property
    $this->outline = $outline;
}
```

**UI** then shows `$this->outline` in a `<textarea>` for user editing. This is extremely minimal, but enough for a proof of concept.

### 6.2 Option: GPT-4 Summarize Step
If you want GPT-based text, do something like:

```php
public function generateStrategyOutline()
{
    $prompt = "You are a legal assistant. Here is a list of 5 cases relevant to the issue: {$this->issue->issue_name}\n".
              "Summaries:\n".
              json_encode($results)."\n".
              "Create a short strategy outline referencing the key points from these cases, placeholders for facts.\n".
              "Output in markdown or bullet points.";
    $gptResponse = $this->callOpenAIGPT($prompt);

    $this->outline = $gptResponse;
}
```

---

## 7. Putting it All Together: Minimal Steps

1. **Add Migrations**: run `php artisan make:migration ...` for `case_issues` (and optionally `case_issues_results`), finalize schema, `php artisan migrate`.
2. **Create Models**: `CaseIssue`, `CaseIssueResult`.
3. **Queue Setup**: Ensure your `.env` is set for queue driver (database, redis, etc.), run `php artisan queue:work` or similar to process jobs.
4. **Job Class**: `PerformLegalResearchForIssue` with your actual code for calling a free or mock API.
5. **Livewire Components**:
    - `CaseIssuesDashboard` (lists issues, triggers jobs)
    - `CaseIssueDetails` (shows results, optionally a button to generate outline)
6. **Route Definitions**:
   ```php
   Route::get('/cases/{caseId}/issues', CaseIssuesDashboard::class)->name('case-issues');
   Route::get('/issues/{issueId}', CaseIssueDetails::class)->name('issue-details');
   ```
7. **UI Test**:
    - Create a new `CaseIssue` row in Tinker or admin UI.
    - Navigate to `case-issues` route, see your issue.
    - Click “Queue Research,” watch the queue job log.
    - Refresh and see results.
    - Generate a strategy outline.

---

## 8. Summary

This plan **walks through** the essential data structures, the asynchronous job for retrieving legal data, how to present it in Livewire components, and a minimal drafting approach. It’s enough for a developer to build a functioning MVP where:

1. Issues get created or identified.
2. Each issue’s research is automated with a job.
3. Results are displayed in a straightforward UI for the user to review.
4. A simple “draft strategy” or “outline” is generated from the findings.

This should **validate** the concept of multi-issue research, open-source legal data integration, and rudimentary drafting support – all within your existing **Laravel 11 + Livewire 3** stack. Once confirmed, you’ll iterate to add deeper citation checking, more robust drafting, and more refined workflows.
