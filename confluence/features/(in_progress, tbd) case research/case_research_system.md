Below is a revised MVP plan that addresses the **broader goal**: we want to help the user **win their case** by automatically identifying and researching the relevant legal issues based on all the facts and documents they’ve provided—not just a single question. This approach uses **the user’s case data** (already stored or uploaded) to derive potential legal issues and launch focused research tasks in the background. Then it aggregates results into a coherent knowledge base for drafting and strategy.

---

## 1. Overview of the Revised MVP Flow

1. **Case Data Intake**: We assume the user has uploaded their documents (pleadings, contracts, evidence) and provided a factual narrative, all stored in your vector database or standard DB tables.
2. **Issue Identification**: The system processes these documents to spot potential legal issues or angles of attack/defense (e.g., “breach of contract,” “negligence,” “procedural defense,” etc.).
3. **Automated Research Jobs**: For each identified legal issue, the system creates a research task, pulling relevant case law from a free or open-source legal repository.
4. **Aggregation & Summaries**: The results are summarized and stored, flagged for verification, and presented to the user for review.
5. **Draft/Strategy Starter**: The MVP can then produce an outline or simple draft strategy that references the discovered authorities. The user finalizes or revises as needed.

This MVP approach acknowledges that **the user might not know exactly which questions to ask**. Instead, the system helps surface issues and gather the relevant law that can help them prevail.

---

## 2. Data Extraction & Issue Identification

### 2.1 Document Parsing to Identify Facts & Parties
1. **Existing Document Pipeline**: You already have a pipeline that ingests user documents (PDFs, Word files, etc.) and extracts text. You also have a vector store for semantic search.
2. **Issue Extraction Script**
    - Use a **small LLM prompt** or a specialized NLP approach to parse the entire text corpus of the user’s case.
    - For example, prompt GPT-4 or another model with:
      > “Given these documents describing a legal dispute, identify the main factual allegations, any references to legal claims, injuries, or defenses, and categorize them under potential legal issues.”
    - The model might return a JSON-like structure:
      ```json
      {
        "issues": [
          "Breach of Contract",
          "Failure to Perform Services",
          "Potential Counterclaim for Damages"
        ],
        "factual_highlights": [
          "Party A signed contract on X date",
          ...
        ]
      }
      ```
    - Store these results in your DB (e.g., a `CaseAnalysis` table) or in the vector store with tags like “issues:breach_of_contract.”

### 2.2 Confirmation Step (Optional in MVP)
- Present the identified issues to the user: “We found these potential claims/defenses. Please confirm which are relevant or add any we missed.”
- This ensures the system doesn’t go down a rabbit hole if it misreads the documents.

---

## 3. Automated Research Jobs per Issue

Once the system has a set of legal issues (like “breach of contract”), we automate research for each:

### 3.1 Create a “CaseIssue” Entity
- **Table**: `case_issues`
- **Fields**:
    - `case_id`
    - `issue_name` (e.g., “Breach of Contract”)
    - `description` (optional longer text)
    - `research_status` (queued, in-progress, complete, etc.)
    - `research_results_json` (nullable; we can store the summary or references here as an MVP)

### 3.2 Enqueue a Research Job
- When a user confirms or the system auto-assigns an issue, create a record in `case_issues` with `research_status = 'queued'`.
- Dispatch a **Laravel job** (e.g., `PerformLegalResearchForIssue`) to process it:

#### PerformLegalResearchForIssue Job
1. Mark `research_status` as `in-progress`.
2. **Hit a Free Legal API** (e.g., CourtListener):
    - Use a query like: “breach of contract + [jurisdiction if known] + relevant keywords from the facts.”
    - Retrieve top results (like 5–10 relevant cases).
3. (Optional) Summarize each case with an LLM.
4. Attempt a **basic citation check** (MVP style) using free endpoints or store a placeholder “verification pending.”
5. Aggregate into a short summary or reference list.
6. Save it in `case_issues.research_results_json` or a related table.
7. Mark `research_status = 'complete'`.

### 3.3 Displaying Multiple Issues
- In the UI, the user visits “Case Research Overview,” sees each identified issue (breach, negligence, etc.), and checks whether the job for that issue is done. They can review the list of cases found, short summaries, or linked full texts. This **multi-issue approach** is more realistic than a single “one-off question” scenario.

---

## 4. Aggregation & Summaries

### 4.1 Combine Issue Results
- Once each issue’s job finishes, the system can produce a consolidated “Case Research Summary,” merging the top points from each issue. A **simple approach**:
    - A short LLM prompt:
      > “We have these issues and their top cases. Summarize them for use in a case strategy doc. Include the main takeaways from each case and how it might apply to the user’s scenario.”
    - Store the output in a `case_strategy_summary` table or similar.

### 4.2 Verification & User Feedback
- The user can see: “Issue 1: 5 cases found. Summaries of each. Potential negative history unknown or partial.”
- They can click any case to read more, skip irrelevant ones, or add a known case.
- The final step is user sign-off: “Mark these 3 cases as relevant to our argument on breach of contract, discard the others.”
- This feedback can feed back into the vector store for future queries.

---

## 5. Draft/Strategy Starter

### 5.1 Simple Outline Generation
- A “Generate Preliminary Strategy or Draft” button:
    - The system uses the final curated list of issues and relevant cases.
    - Runs an LLM prompt:
      > “Create a short legal strategy outline referencing the confirmed issues and these top authorities.”
    - Produces an outline with headings:
        1. Introduction (brief summary of facts)
        2. Legal Issues & Supporting Cases (for each issue)
        3. Potential Counterarguments
        4. Conclusion / Next Steps

### 5.2 MVP Editor
- Present that outline in a basic WYSIWYG or Markdown editor.
- Let the user reorder sections, fill in additional facts, or remove sections that don’t apply.

---

## 6. Implementation Steps (Detailed)

Below is a more granular to-do list reflecting this multi-issue approach:

1. **Data Structures**
    - **Migrate**: create `case_issues` table to store each potential legal issue.
    - Possibly a small `case_analysis` or `case_strategy_summaries` table.

2. **Document Parsing & Issue Detection**
    - **LLM-based approach** (MVP style):
        - On “Case Data Upload,” once the user’s docs are stored in your system, call GPT-4 with the aggregated text (truncated or chunked) and ask it to produce a list of potential claims/defenses.
        - Save the result to DB in a single JSON field (like `identified_issues`) and create `case_issues` records from them.
    - Alternatively, let the user do it manually if the LLM approach is too advanced for now. (E.g., user picks from a list of common claims.)

3. **Queue for Research**
    - For each “issue” row, dispatch a `PerformLegalResearchForIssue(caseIssueId)` job.
    - **In the job**:
        1. Build a query string from the `issue_name` + optional details from the user’s factual summary.
        2. Call CourtListener or any open legal DB.
        3. Parse top results, store them (like a `research_results_json` or a separate table `case_issues_results`).
        4. Summarize them if you can (using GPT, if feasible).
        5. Mark done.

4. **UI for Research Progress**
    - “Case Issues” dashboard shows each issue with a progress indicator (queued/in-progress/complete).
    - Once complete, show the short summaries or list of cases, with a link to more detail (like a CourtListener link).

5. **User Review**
    - Provide a UI for the user to select which cases they want to keep in the final strategy. Possibly a checkbox for each result.
    - Offer a freeform note field: “How does this case help your argument?” if they want to add personal context.

6. **Draft Strategy Outline**
    - Once at least one issue’s research is complete, enable a “Generate Outline” button.
    - A minimal GPT-4 prompt merges the user’s facts with the top cases, producing a bullet-point outline.
    - Store and display it in a new Livewire component.
    - Let the user edit or finalize it (MVP style).

7. **Testing & Validation**
    - Upload a sample set of documents describing a scenario.
    - Verify that the system identifies one or more issues.
    - Confirm that the job queue fetches actual cases from CourtListener.
    - Check the final outline for coherence.

---

## 7. Future Optimizations

1. **Advanced Citation Checking**
    - Integrate with more robust citator APIs (paid or advanced open-source) to verify good law.
2. **Iterative Agent**
    - If initial research is insufficient, let the system refine queries or gather more targeted info, automatically.
3. **User Guidance**
    - Provide step-by-step guidance or a wizard for *pro se* litigants explaining why these issues matter, how these cases help, etc.
4. **Vector Index**
    - For truly robust performance, store all retrieved case texts in a vector DB and let your GPT-based system do in-depth semantic referencing.
5. **Drafting Maturity**
    - Move from a bullet-point outline to a near-complete brief or memorandum generator, with placeholders for user facts.

---

## Conclusion

**Key Difference:** Instead of handling a single question, this plan focuses on **scanning the user’s case data for potential legal issues**, then **enqueuing** a specialized research job for each. The system aggregates the results, enabling the user (and eventually the AI) to craft a **strategy** or legal document referencing the discovered authorities.

This approach is still lean enough to build as an MVP—where you prove that:
- You can parse case facts to suggest potential legal issues.
- You can automate free legal research calls for each issue.
- You can unify the results into a preliminary outline or memo.

Once validated, you’ll continue refining each stage (issue detection, research, verification, drafting) into a robust legal assistant that helps users marshal all relevant law to *win* their case.
