# Address Book (Party Directory/Rolodex)

## Overview
The Address Book is a comprehensive contact management system designed to handle parties involved in cases and correspondence. It provides functionality for storing, managing, and selecting contacts with support for both manual and automated address entry.

## Technical Details

### Core Components

1. **Livewire Component**
   - Main Component: `App\Livewire\AddressBook\PartyDirectory`
   - View: `resources/views/livewire/address-book/party-directory.blade.php`

2. **Models**
   - `App\Models\Party`: Represents a contact entry
   - `App\Enums\UsState`: Enumeration for US states

### Architecture

#### Component Structure
```php
class PartyDirectory extends Component
{
    use WithPagination;

    // State Management
    public $activeTab = 'manual';
    public $isFormVisible = false;
    public $editingParty = null;
    public $search = '';
    public $selectable = false;

    // Form Properties
    public $name;
    public $address_line1;
    public $address_line2;
    public $city;
    public $state;
    public $zip;
    public $email;
    public $phone;
    public $relationship;
}
```

#### Key Features

1. **Contact Management**
   - CRUD operations for contacts
   - Form validation
   - Soft delete support
   - Relationship tracking

2. **Search & Filtering**
   - Real-time search functionality
   - Multi-field search (name, address, city, state, zip)
   - Pagination support

3. **Address Auto-completion**
   - Google Places API integration
   - Automatic address component parsing
   - Real-time address suggestions

4. **UI Components**
   - Tabbed interface
   - Modal confirmations
   - Responsive table layout
   - Search interface

## Implementation

### 1. Component Registration
```php
// web.php or routes file
Livewire::component('address-book.party-directory', PartyDirectory::class);
```

### 2. View Integration
```php
// In any blade view
<livewire:address-book.party-directory />

// With selection mode
<livewire:address-book.party-directory :selectable="true" />
```

### 3. Data Flow

#### Create/Update Flow
```php
public function save()
{
    $this->validate();

    if ($this->editingParty) {
        // Update existing party
        $this->editingParty->update([...]);
        $this->dispatch('party-updated');
    } else {
        // Create new party
        Party::create([
            'user_id' => auth()->id(),
            ...
        ]);
        $this->dispatch('party-saved');
    }
}
```

#### Search Implementation
```php
public function render()
{
    $parties = Party::where('user_id', auth()->id())
        ->where(function($query) {
            $query->where('name', 'like', '%' . $this->search . '%')
                ->orWhere('address_line1', 'like', '%' . $this->search . '%')
                // Additional search fields...
        })
        ->orderBy('created_at', 'desc')
        ->paginate(5);

    return view('livewire.address-book.party-directory', [
        'parties' => $parties
    ]);
}
```

### 4. Address Auto-completion Integration
```javascript
const autocomplete = new google.maps.places.Autocomplete(input, {
    types: ['address'],
    fields: ['address_components', 'formatted_address']
});

// Handle address selection
autocomplete.addListener('place_changed', () => {
    const place = autocomplete.getPlace();
    const addressComponents = extractAddressComponents(place);
    
    Livewire.dispatch('address-selected', { 
        address: {
            address_line1: addressComponents.street_number + ' ' + addressComponents.route,
            city: addressComponents.locality,
            state: addressComponents.administrative_area_level_1,
            zip: addressComponents.postal_code
        }
    });
});
```

## Dependencies

### Direct Dependencies
- Laravel Livewire
- Google Places API
- DaisyUI (for UI components)
- Alpine.js (for interactive components)

### Related Features
- Case Management
- Correspondence System
- Document Generation
- Interview System

## Database Schema

```sql
parties
├── id (bigint, auto-increment)
├── user_id (bigint, foreign key)
├── name (string)
├── address_line1 (string)
├── address_line2 (string, nullable)
├── city (string)
├── state (string)
├── zip (string)
├── email (string, nullable)
├── phone (string, nullable)
├── relationship (string, nullable)
├── created_at (timestamp)
├── updated_at (timestamp)
└── deleted_at (timestamp, nullable)
```

## Localization Support

The feature includes comprehensive localization support through Laravel's translation system:

```php
// resources/lang/en/address_book.php
return [
    'title' => 'Address Book',
    'parties' => [
        'title' => 'Parties',
        'add_new' => 'Add New Party',
        // ...
    ],
];

// resources/lang/es/address_book.php
return [
    'title' => 'Directorio',
    'parties' => [
        'title' => 'Partes',
        'add_new' => 'Agregar Nueva Parte',
        // ...
    ],
];
```

## Best Practices

1. **Data Validation**
   - Server-side validation for all inputs
   - Real-time client-side validation
   - Proper error handling and display

2. **Performance**
   - Pagination for large datasets
   - Debounced search
   - Lazy loading of Google Places API

3. **Security**
   - User data isolation
   - CSRF protection
   - Input sanitization

4. **User Experience**
   - Responsive design
   - Real-time updates
   - Clear feedback on actions
   - Accessible interface

## Future Enhancements

1. **Planned Features**
   - Batch import/export
   - Advanced filtering
   - Custom fields
   - Contact groups/categories

2. **Technical Improvements**
   - Caching implementation
   - API endpoints
   - Enhanced search capabilities
   - Additional address format support

## Known Issues

Based on `confluence/bugs/remove_choices_from_party_directory.md`:
- Voice input feature temporarily disabled pending improvements
- Need to implement more robust voice processing
