# Exhibit Uploader Component

## Overview

The Exhibit Uploader (formerly Document Uploader) is a Livewire component that provides a drag-and-drop interface for uploading various types of exhibits to a case file. The component has been enhanced to support multimedia files including audio and video, in addition to the previously supported document types.

## Features

- Drag-and-drop file upload interface
- Multiple file support
- Expanded file type validation:
  - Documents: PDF, DOC, DOCX
  - Images: JPG, PNG
  - Audio: MP3, WAV, M4A, OGG
  - Video: MP4, MOV, WEBM
- Tiered file size validation:
  - Documents/Images: 10MB max
  - Audio: 25MB max
  - Video: 150MB max
- Progress indication
- Title and description fields for each exhibit
- Individual and batch save options
- Preview functionality for all supported file types

## Technical Implementation

### Component Structure

- **Livewire Component**: `App\Livewire\ExhibitUploader`
- **Blade View**: `resources/views/livewire/exhibit-uploader.blade.php`
- **Alpine.js Integration**: Uses `exhibitUploader` Alpine component for frontend interactivity
- **Background Processing**: `App\Jobs\ProcessExhibitJob` for asynchronous file processing

### Media File Handling

The component now intelligently processes different file types:

1. **Documents (PDF, DOC, DOCX)**:
   - Uploaded to OpenAI for processing
   - Added to vector store for AI-assisted search
   - Automatic summary generation

2. **Images (JPG, PNG)**:
   - Processed with GPT-4 Vision for content analysis
   - Automatic title and summary generation
   - Excluded from vector store

3. **Audio Files (MP3, WAV, M4A, OGG)**:
   - Automatic transcription via OpenAI
   - Transcription used for summary generation
   - Excluded from vector store

4. **Video Files (MP4, MOV, WEBM)**:
   - Basic metadata extraction
   - No transcription (currently)
   - Excluded from vector store

### Preview Functionality

The component includes a modal preview system that adapts to different file types:
- Documents: Embedded PDF viewer
- Images: Responsive image display
- Audio: HTML5 audio player with controls
- Video: HTML5 video player with controls

## Usage

```php
<livewire:exhibit-uploader :case-file="$caseFile" />
```

## Processing Flow

1. User uploads file(s) via drag-drop or file input
2. Files are temporarily stored and queued for processing
3. User can add title/description and save individual files or batch save
4. `ProcessExhibitJob` handles background processing based on file type
5. Processed exhibits appear in the case file's exhibit list

## AI Integration

### 1. Document Analysis
```php
Current Capabilities:
- Automatic title generation
- Content summarization
- Party information extraction
- Document classification
- Text extraction
- Image content analysis
- Vector store integration

Processing Types:
- Text Documents: Full content analysis with party extraction
- Images: Vision-based analysis with GPT-4 Vision
- Future: Audio transcription and video analysis

Planned Features:
- Audio transcription
- Video frame analysis
- Multi-modal content understanding
```

### 2. OpenAI Integration
```php
Features:
- Document upload to OpenAI
- GPT-4 based analysis
- Vision capabilities for images
- Assistants API integration
- Thread-based processing
```

## File Processing Pipeline

### 1. Upload Flow
```mermaid
graph TD
    A[File Selection] --> B[Type Validation]
    B --> C[Size Validation]
    C --> D[Temporary Storage]
    D --> E[AI Processing]
    E --> F[Permanent Storage]
    F --> G[Vector Store Integration]
```

### 2. Processing Stages
```php
Stages:
1. Initial validation
2. Temporary storage
3. File sanitization
4. AI analysis
5. Metadata extraction
6. Vector embedding
7. Permanent storage
```

## Planned Enhancements

### 1. Audio Support
```php
Features:
- Format support: MP3, WAV, M4A
- Automatic transcription
- Speaker identification
- Timestamp markers
- Content summarization
```

### 2. Video Support
```php
Features:
- Format support: MP4, MOV
- Frame extraction
- Scene detection
- Transcription
- Visual content analysis
```

### 3. Enhanced Image Processing
```php
Features:
- OCR improvements
- Object detection
- Face detection
- Image classification
- Content moderation
```

## Security & Validation

### 1. File Validation
```php
Rules:
- Maximum file size: 10MB
- Allowed MIME types
- File integrity checks
- Malware scanning
```

### 2. Access Control
```php
Controls:
- User permissions
- Case-level access
- File-type restrictions
- Upload quotas
```

## Performance Considerations

### 1. Upload Optimization
```php
Features:
- Chunk uploading
- Progress tracking
- Parallel processing
- Background jobs
```

### 2. Storage Management
```php
Features:
- Automatic cleanup
- Storage quotas
- Temporary file handling
- Version control
```

## Integration Points

### 1. Internal Systems
```php
Integrations:
- Case Management
- Document Storage
- AI Analysis Pipeline
- Search System
```

### 2. External Services
```php
Services:
- OpenAI API
- Cloud Storage
- Vector Database
- Media Processing
```

## Error Handling

### 1. Upload Errors
```php
Handled Cases:
- File size exceeded
- Invalid file type
- Upload interruption
- Storage failures
```

### 2. Processing Errors
```php
Handled Cases:
- AI processing failures
- File corruption
- Storage errors
- Integration failures
```

## Dependencies

### Direct Dependencies
```php
Required:
- Laravel Livewire
- Alpine.js
- OpenAI Laravel
- AWS SDK
```

### Related Features
```php
Connected Systems:
- Case Management
- Document Storage
- AI Assistant
- Search System
```

## Testing Strategy

### 1. Unit Tests
```php
Test Cases:
- File validation
- Upload processing
- Error handling
- State management
```

### 2. Integration Tests
```php
Test Cases:
- End-to-end upload
- AI processing
- Storage integration
- UI interactions
```

## Monitoring & Logging

### 1. Performance Metrics
```php
Tracked Metrics:
- Upload success rate
- Processing time
- Storage usage
- Error frequency
```

### 2. Error Tracking
```php
Logged Events:
- Upload failures
- Processing errors
- AI analysis issues
- Storage problems
```

## Future Roadmap

### Phase 1: Audio Support
1. Implementation of audio file handling
2. Transcription service integration
3. Audio metadata extraction
4. Search integration

### Phase 2: Video Support
1. Video upload infrastructure
2. Frame extraction system
3. Video transcription
4. Content analysis

### Phase 3: Enhanced Analysis
1. Multi-modal AI processing
2. Advanced search capabilities
3. Content relationships
4. Automated tagging

## Best Practices

### 1. File Handling
- Validate before processing
- Secure storage practices
- Efficient cleanup
- Version control

### 2. User Experience
- Clear feedback
- Progress indication
- Error messaging
- Intuitive interface
```
