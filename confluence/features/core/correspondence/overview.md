# Correspondence System

## Overview
The Correspondence system manages communication threads and individual communications between parties involved in case files. It provides a structured way to track, store, and display various types of communications (emails, letters, phone calls, etc.) with a chronological timeline view and document attachment capabilities.

## Core Components

### 1. Models

#### Thread Model
```php
class Thread extends Model
{
    protected $fillable = [
        'case_file_id',
        'title',
        'status',
        'created_by'
    ];

    // Relationships
    - caseFile(): BelongsTo
    - communications(): <PERSON><PERSON><PERSON>
    - creator(): <PERSON>ongsT<PERSON>
}
```

#### Communication Model
```php
class Communication extends Model
{
    protected $fillable = [
        'thread_id',
        'type',        // email, letter, phone, other
        'subject',
        'content',
        'sent_at'
    ];

    // Relationships
    - thread(): BelongsTo
    - participants(): BelongsToMany
    - documents(): BelongsToMany
}
```

### 2. Livewire Components

#### ThreadView Component
- Displays communication timeline
- Manages document previews
- Handles party details modal
- Provides pagination

#### AddCommunicationForm Component
```php
Features:
- Party selection with search
- Document attachment
- Multiple participant roles (sender/recipient)
- File uploads
- Existing document linking
```

### 3. View Structure

```
correspondence/
├── thread-view.blade.php
├── add-communication-form.blade.php
└── components/
    ├── timeline-entry.blade.php
    ├── document-preview.blade.php
    └── party-details.blade.php
```

## Technical Implementation

### 1. Communication Timeline

#### Visual Structure
```html
<div class="relative">
    <!-- Timeline connector -->
    <div class="absolute left-6 top-12 bottom-0 w-0.5 bg-base-300"></div>
    
    <!-- Timeline marker -->
    <div class="absolute left-0 top-4">
        <div class="flex h-12 w-12 items-center justify-center rounded-full">
            <div class="h-3 w-3 rounded-full bg-primary"></div>
        </div>
    </div>
</div>
```

#### Communication Entry Components
- Header section (date, subject, type)
- Participants section (senders and recipients)
- Content section
- Documents/attachments section

### 2. Communication Management

#### Adding Communications
```php
Features:
- Multiple participant selection
- Document attachment options
- Date and time selection
- Communication type selection
- Content editor
```

#### Document Handling
```php
Capabilities:
- File uploads (max: 10MB)
- Existing document linking
- Preview support for:
  - Images
  - PDFs
  - Other file types
```

### 3. Party Management

#### Party Selection
```php
Features:
- Real-time search
- Role assignment (sender/recipient)
- Quick add/remove
- Party details view
```

#### Party Details Display
```php
Information:
- Name
- Address
- Contact information
- Relationship type
- Communication history
```

## User Interface

### 1. Timeline View

#### Layout Structure
```
- Thread title and actions
- Pagination controls
- Communication entries
  - Timeline markers
  - Content cards
  - Participant badges
  - Document attachments
```

#### Interactive Elements
- Document preview modal
- Party details modal
- Add communication button
- Navigation controls

### 2. Communication Form

#### Form Sections
```
1. Basic Information
   - Type selection
   - Subject
   - Date/time
2. Participants
   - Search interface
   - Role selection
3. Content
   - Text editor
4. Attachments
   - File upload
   - Document search
```

## Workflows

### 1. Adding a Communication

```mermaid
graph TD
    A[Open Thread] --> B[Click Add Communication]
    B --> C[Fill Form Details]
    C --> D[Select Participants]
    C --> E[Add Documents]
    D --> F[Save Communication]
    E --> F
    F --> G[Update Timeline]
```

### 2. Document Management

#### Upload Process
1. File selection
2. Validation
3. Storage
4. Association with communication
5. Preview generation

#### Document Preview
1. Modal trigger
2. Format detection
3. Appropriate viewer selection
4. Fallback handling

## Security & Validation

### 1. Form Validation
```php
protected $rules = [
    'type' => 'required|in:email,letter,phone,other',
    'content' => 'required_without:selectedDocuments',
    'subject' => 'nullable|string|max:255',
    'sent_at' => 'required|date',
    'selectedParties' => 'required|array|min:1',
    'newDocuments.*' => 'nullable|file|max:10240'
];
```

### 2. Access Control
- Thread-level permissions
- Document access restrictions
- Party information protection

## Events & Listeners

### 1. Communication Events
```php
Events:
- communication-saved
- document-uploaded
- party-selected
```

### 2. UI Updates
- Real-time timeline refresh
- Modal management
- Search results updates

## Performance Considerations

### 1. Data Loading
- Pagination implementation
- Eager loading relationships
- Selective content loading

### 2. Resource Management
- Document size limits
- Search result limits
- Cache implementation

## Integration Points

### 1. Related Systems
- Case Files
- Document Management
- Party Directory
- User Authentication

### 2. External Services
- File Storage
- Preview Generation
- Search Services

## Best Practices

### 1. Communication Management
- Consistent formatting
- Clear participant roles
- Proper document organization
- Accurate timestamps

### 2. User Interface
- Intuitive navigation
- Clear status indicators
- Responsive design
- Accessible controls

## Future Enhancements

### 1. Planned Features
- Advanced search capabilities
- Bulk operations
- Template system
- Export functionality

### 2. Technical Improvements
- Real-time updates
- Enhanced preview capabilities
- Advanced filtering
- Performance optimization

## Dependencies

### Direct Dependencies
- Laravel Framework
- Livewire
- Alpine.js
- DaisyUI

### Related Features
- Document Management
- Party Directory
- Case Management
- User Authentication
