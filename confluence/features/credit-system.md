# Justice Quest Credit System

## Overview

The Justice Quest credit system powers all AI-driven features within the application. Credits are consumed based on the computational complexity and resources required for each operation.

## Credit Costs

### Case Management
- **Initializing a New Case**: 100 credits
  - Sets up a new legal case with AI context and initial analysis
- **Legal Research Report (within case)**: 100 credits per report
  - Comprehensive research report generated within an active case context

### Document Processing
- **Document Analysis & Summary**: 10 credits
  - 5 credits for document summarization
  - 5 credits for storing in AI vector store
- **Audio Transcription**: +5 additional credits
  - Added to document processing cost when audio files are successfully transcribed
  - **Total for exhibits**: 10 credits (documents) or 15 credits (documents with audio)

### Independent Research
- **Short Web Search**: 50 credits
  - Quick web-based legal research
- **Regular Research Report**: 100 credits
  - Standard comprehensive research report
- **Deep Research Report**: 300 credits
  - Extensive, detailed research with multiple sources and analysis

### AI Assistant
- **Chat Interaction**: 5 credits per interaction
  - Each message exchange with the AI assistant

## Implementation

### Backend
- Credits are managed through the `CreditService` class
- User balances stored in `user_credits` table
- All transactions logged in `credit_transactions` table
- Subscription enforcement prevents non-subscribers from purchasing additional credits

### Frontend
- Credit dashboard shows current balance and usage breakdown
- Real-time calculation of available actions based on current balance
- Clear pricing information for all features
- Visual indicators for low/zero credit states

## User Experience

### Credit Dashboard Features
1. **Current Balance Display**: Large, prominent credit count
2. **Collapsible Usage Guide**: Detailed explanation of how credits are used (expandable section)
3. **Available Actions**: Shows what users can do with their current balance
4. **Purchase Options**: Conditional display based on subscription status
5. **Transaction History**: Recent credit additions and deductions

### Subscription Page Features
1. **Collapsible Credit Information**: Same detailed credit guide available on subscription page
2. **Plan Comparison**: Clear display of credit allocations per plan
3. **Educational Content**: Helps users understand value proposition

### Welcome Page Features
1. **Integrated Credit Information**: Credit system explanation within the pricing section
2. **Gaming Theme Integration**: Styled with fight-card design to match the landing page's aesthetic
3. **Pre-signup Education**: Helps users understand the system before registering
4. **Contextual Placement**: Located directly below pricing plans for immediate context

### Subscription Integration
- One-time credit purchases require active subscription
- Monthly subscription plans include credit allocations
- Clear messaging about subscription requirements
- Seamless navigation to subscription plans

## Business Logic

### Why Credits?
Credits ensure fair usage of AI-powered legal tools while maintaining sustainable service costs. The pricing structure reflects the computational complexity of each feature:

- Simple operations (chat) = low cost (5 credits)
- Medium operations (document processing) = moderate cost (10-15 credits)
- Complex operations (research, case initialization) = higher cost (50-300 credits)

### Subscription Model
- **Basic Plan**: 2,000 credits/month + 1 active case
- **Standard Plan**: 6,000 credits/month + 2-3 cases + enhanced AI
- **Plus Plan**: 25,000 credits/month + unlimited cases + all features

## Technical Notes

### Credit Deduction Flow
1. Check user has sufficient credits
2. Perform the requested operation
3. Deduct credits and log transaction
4. Update user balance
5. Return operation result

### Error Handling
- Insufficient credits: Graceful failure with clear messaging
- Failed operations: Credits not deducted
- Transaction logging: All credit movements tracked for audit

### Future Enhancements
- Credit expiration policies
- Bulk credit discounts
- Usage analytics and recommendations
- Credit gifting between users
