# Notifications

## Overview
The Notifications system provides a robust infrastructure for informing users about various events within the application. While currently primarily used for collaboration-related notifications, the system is designed to be extensible for future notification types.

## Technical Details

### Core Components

1. **Notification Classes**
   ```php
   Current Implementations:
   - CollaborationInviteNotification
   - CollaboratorRemovedNotification
   - CollaboratorRoleChangedNotification
   ```

2. **Delivery Channels**
   ```php
   Supported Channels:
   - Mail
   - Database
   - Broadcast (Real-time)
   ```

3. **User Interface**
   - Real-time notification updates
   - Notification center dropdown
   - Mark as read functionality
   - Notification count badge

### Architecture

1. **Base Structure**
   ```php
   class CollaborationInviteNotification extends Notification 
   implements ShouldQueue, ShouldBroadcast
   {
       use Queueable;

       public function via($notifiable): array
       {
           return ['mail', 'database', 'broadcast'];
       }

       // Channel-specific formatting methods
       public function toMail($notifiable)
       public function toArray($notifiable)
       public function toBroadcast($notifiable)
   }
   ```

2. **Database Schema**
   ```sql
   notifications
   ├── id (uuid)
   ├── type (string)
   ├── notifiable_type (string)
   ├── notifiable_id (bigint)
   ├── data (json)
   ├── read_at (timestamp, nullable)
   └── created_at, updated_at
   ```

## Implementation

### 1. Notification Dispatch
```php
// Example: Sending collaboration invite
$user->notify(new CollaborationInviteNotification(
    $caseFile,
    $collaborator
));
```

### 2. Channel Implementations

#### Email Channel
```php
public function toMail($notifiable): MailMessage
{
    return (new MailMessage)
        ->subject(__('collaboration.notifications.invite_subject'))
        ->line(__('notifications.invite_received', [
            'case' => $this->caseFile->title,
            'role' => $this->collaborator->role
        ]))
        ->action(__('notifications.view_case'), route('cases.show', $this->caseFile));
}
```

#### Database Channel
```php
public function toArray($notifiable): array
{
    return [
        'case_file_id' => $this->caseFile->id,
        'case_title' => $this->caseFile->title,
        'role' => $this->collaborator->role,
        'type' => 'collaboration_invite'
    ];
}
```

#### Broadcast Channel
```php
public function toBroadcast($notifiable): BroadcastMessage
{
    return new BroadcastMessage([
        'case_file_id' => $this->caseFile->id,
        'case_title' => $this->caseFile->title,
        'role' => $this->collaborator->role,
        'type' => 'collaboration_invite'
    ]);
}
```

### 3. User Interface Integration

#### Notification List Component
```php
// Livewire component for real-time updates
class NotificationsList extends Component
{
    public function getUnreadNotificationsProperty()
    {
        return auth()->user()->unreadNotifications()
            ->latest()
            ->take(5)
            ->get();
    }

    public function markAsRead($notificationId)
    {
        auth()->user()
            ->notifications
            ->where('id', $notificationId)
            ->first()
            ->markAsRead();
    }
}
```

## Localization Support

### 1. Translation Keys
```php
// English (en/notifications.php)
return [
    'invite_received' => 'You have been invited to collaborate on case ":case" as :role',
    'access_revoked' => 'Your access to case ":case" has been revoked',
    'role_changed' => 'Your role for case ":case" has been changed to :role',
];

// Spanish (es/notifications.php)
return [
    'invite_received' => 'Has sido invitado a colaborar en el caso ":case" como :role',
    'access_revoked' => 'Tu acceso al caso ":case" ha sido revocado',
    'role_changed' => 'Tu rol para el caso ":case" ha sido cambiado a :role',
];
```

## Dependencies

### Direct Dependencies
- Laravel's Notification System
- Queue System
- Broadcasting System
- Mail System
- Database

### Related Features
- Case Collaboration
- User Authentication
- Real-time Updates

## Best Practices

### 1. Notification Design
- Keep notifications concise
- Include relevant action links
- Provide clear context
- Support localization

### 2. Performance
- Queue notifications when possible
- Implement read/unread status
- Limit notification history
- Clean up old notifications

### 3. User Experience
- Real-time updates
- Clear notification indicators
- Easy mark-as-read functionality
- Actionable notification content

## Future Enhancements

### 1. Planned Notification Types
- Document upload notifications
- Comment notifications
- Deadline notifications
- System announcements

### 2. Additional Features
- Notification preferences
- Channel preferences per notification type
- Batch notification processing
- Advanced filtering options

## Error Handling

### 1. Delivery Failures
- Queue retry mechanism
- Failed notification logging
- Fallback delivery methods
- User notification preferences

### 2. Data Validation
- Required notification data
- Channel-specific formatting
- Invalid notification handling
- Missing translation handling

## Security Considerations

### 1. Access Control
- Notification ownership verification
- Channel access validation
- Data exposure prevention
- Rate limiting

### 2. Data Protection
- Sensitive data handling
- Encrypted delivery channels
- Access logging
- Privacy compliance
