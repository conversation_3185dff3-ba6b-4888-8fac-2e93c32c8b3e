# Query Classification System

## Overview
The Query Classification System determines the optimal research path for legal queries by leveraging existing case context from our vector store and selecting appropriate research tools based on query complexity and requirements.

## Classification Flow

```mermaid
graph TD
    A[Query + Case ID] --> B[Vector Store Context Check]
    B --> C[Initial LLM Analysis]
    C --> D{Needs External Research?}
    D -->|No| E[Direct LLM Response]
    D -->|Yes| F{Classify Research Type}
    F -->|Quick| G[Court Listener API]
    F -->|Targeted| H[Stage Hand]
    F -->|Deep| I[GPT Researcher]
```

## Decision Logic

### 1. Vector Store Context Check
- Query vector store for relevant case documents
- Calculate similarity scores
- Extract key legal concepts and citations
- Determine if existing context is sufficient

### 2. Initial LLM Analysis
Input factors:
- Query text
- Vector store results
- Similarity scores
- Document metadata

Output:
- Confidence score (0-1)
- Required context types
- Identified legal concepts
- Time sensitivity

### 3. Research Tool Selection

#### Tier 1: Direct API (Court Listener)
Conditions:
- High vector store similarity (>0.8)
- Single jurisdiction
- Citation verification
- Basic legal definitions
- Expected response time: <5s

#### Tier 2: Stage Hand
Conditions:
- Medium vector store similarity (0.5-0.8)
- Known target sources
- Specific legal domains
- Single-topic research
- Expected response time: <30s

#### Tier 3: GPT Researcher
Conditions:
- Low vector store similarity (<0.5)
- Multiple jurisdictions
- Strategy formation
- Complex legal analysis
- Expected response time: <5m

## Implementation Priority

1. Vector Store Integration
   - Implement similarity scoring
   - Define relevance thresholds
   - Set up context extraction

2. Classification Logic
   - Build decision tree
   - Implement scoring system
   - Create routing rules

3. Tool Integration
   - Court Listener API wrapper
   - Stage Hand automation
   - GPT Researcher setup

4. Response Handling
   - Format standardization
   - Error handling
   - Result caching

## Success Metrics
- Classification accuracy >90%
- Tool selection efficiency >85%
- Response time within tier limits
- Context utilization rate >75%

## Next Steps
1. Implement vector store similarity check
2. Create classification scoring system
3. Set up tool routing logic
4. Add monitoring and logging
