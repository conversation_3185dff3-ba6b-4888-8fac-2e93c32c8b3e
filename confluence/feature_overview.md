# Justice Quest Feature Overview

## Core System

### Case Files
- Central organizing unit for all case-related information
- Named "Case File" instead of "Case" to avoid PHP reserved word conflicts
- Contains all documents, correspondence, and case-specific AI assistance
- Serves as the primary container for all case-related activities

### Case Collaboration
- Allows multiple users to access and work on the same case
- Invitation system for adding collaborators
- Notification system for collaboration status updates
- Permission management for case access

### Notifications
- Currently used primarily for collaboration notifications
- Notifies users when added as collaborators
- Notifies case owners when collaboration invitations are accepted/declined
- Foundation for future notification types

## Document Management

### Exhibit Management (Document Uploader)
- Secure document storage using Linode Object Storage
- AI-powered document analysis for automatic title and summary generation
- Vector store integration for searchable document content
- Support for multiple file types (PDF, Word, images)
- Planned expansion to support all exhibit types including audio/video

## AI Integration

### AI Assistant
- OpenAI Assistants API integration
- Case-specific AI assistant created for each case
- Vector store coupling for document-aware responses
- Provides contextual assistance based on case documents and information

### API Token Manager
- Internal feature for managing OpenAI project keys
- Load balancing across multiple API keys
- Tracking of vector store data usage per project
- Round-robin assignment of project keys to AI assistants

## Communication Tools

### Correspondence System
- Thread-based communication tracking
- Support for various communication types
- Chronological organization of communications
- Integration with address book for participant management

### Address Book (Party Directory)
- Contact management system ("rolodex")
- Stores information about all parties relevant to cases
- Used when adding parties to cases or correspondence
- Central repository for contact information

### Voice Message Input
- Speech-to-text functionality for efficient input
- OpenAI Whisper integration for transcription
- Allows users to record or upload audio for transcription
- Editable transcription results

## Case Management

### Case Interview (In Progress)
- Structured intake process for gathering case information
- Determines case status (active or preparation)
- Collects party information
- Captures case overview and relevant documents
- AI-powered follow-up questions for additional information

### Docket Management
- Tracking of case events and deadlines
- Document attachment capabilities for docket entries
- Chronological organization of case proceedings

## User Experience

### Multilingual Support
- Current support for English and Spanish
- Infrastructure for adding additional languages
- Complete UI translation capabilities

### Theme Customization
- Multiple theme options from DaisyUI
- User-selectable interface appearance
- Personalized visual experience

## Planned Features

### Enhanced Interview Process
- Improved document handling during intake
- Better integration of exhibits with case overview
- Optimized user experience for information gathering

### Case Research
- Integration with Court Listener API
- Potential integration with GPT Researcher for deep legal research
- Automated legal research capabilities

### Case Strategy
- AI-powered case strategy recommendations
- Multiple LLM iterations for strategy refinement
- Decision support for legal actions (motions, discovery, etc.)

### Document Drafting
- Automated legal document generation
- Context-aware drafting based on case information
- Template-based document creation