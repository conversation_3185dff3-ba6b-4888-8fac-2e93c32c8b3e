{"api_token_usage": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "personal_access_token_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "endpoint": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "method": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "ip_address": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "response_code": {"type": "int", "nullable": false, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "api_token_usage_personal_access_token_id_foreign": {"columns": ["personal_access_token_id"], "primary": false, "unique": false}}, "foreign_keys": {"api_token_usage_personal_access_token_id_foreign": {"local_columns": ["personal_access_token_id"], "foreign_table": "personal_access_tokens", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "cache": {"columns": {"key": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "value": {"type": "mediumtext", "nullable": false, "default": null, "autoIncrement": false}, "expiration": {"type": "int", "nullable": false, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["key"], "primary": true, "unique": true}}, "foreign_keys": []}, "cache_locks": {"columns": {"key": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "owner": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "expiration": {"type": "int", "nullable": false, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["key"], "primary": true, "unique": true}}, "foreign_keys": []}, "case_access_logs": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "user_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "action": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "metadata": {"type": "json", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "case_access_logs_case_file_id_created_at_index": {"columns": ["case_file_id", "created_at"], "primary": false, "unique": false}, "case_access_logs_user_id_created_at_index": {"columns": ["user_id", "created_at"], "primary": false, "unique": false}}, "foreign_keys": {"case_access_logs_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "case_access_logs_user_id_foreign": {"local_columns": ["user_id"], "foreign_table": "users", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "case_collaborators": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "user_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "role": {"type": "enum('viewer','editor','manager')", "nullable": false, "default": null, "autoIncrement": false}, "status": {"type": "enum('pending','active','revoked')", "nullable": false, "default": "pending", "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "case_collaborators_case_file_id_user_id_unique": {"columns": ["case_file_id", "user_id"], "primary": false, "unique": true}, "case_collaborators_user_id_foreign": {"columns": ["user_id"], "primary": false, "unique": false}, "case_collaborators_status_created_at_index": {"columns": ["status", "created_at"], "primary": false, "unique": false}}, "foreign_keys": {"case_collaborators_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "case_collaborators_user_id_foreign": {"local_columns": ["user_id"], "foreign_table": "users", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "case_files": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "user_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "title": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "case_number": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "desired_outcome": {"type": "text", "nullable": false, "default": null, "autoIncrement": false}, "status": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": "open", "autoIncrement": false}, "filed_date": {"type": "date", "nullable": true, "default": null, "autoIncrement": false}, "case_types": {"type": "json", "nullable": true, "default": null, "autoIncrement": false}, "date_of_incident": {"type": "date", "nullable": true, "default": null, "autoIncrement": false}, "initial_summary": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "interview_status": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": "not_started", "autoIncrement": false}, "openai_project_id": {"type": "bigint unsigned", "nullable": true, "default": null, "autoIncrement": false}, "openai_assistant_id": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "openai_vector_store_id": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "collaboration_enabled": {"type": "tinyint(1)", "nullable": false, "default": "0", "autoIncrement": false}, "max_collaborators": {"type": "int", "nullable": false, "default": "0", "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "structured_interview_data": {"type": "json", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "case_files_user_id_foreign": {"columns": ["user_id"], "primary": false, "unique": false}, "case_files_openai_project_id_foreign": {"columns": ["openai_project_id"], "primary": false, "unique": false}}, "foreign_keys": {"case_files_openai_project_id_foreign": {"local_columns": ["openai_project_id"], "foreign_table": "openai_projects", "foreign_columns": ["id"], "on_delete": "SET NULL", "on_update": "NO ACTION"}, "case_files_user_id_foreign": {"local_columns": ["user_id"], "foreign_table": "users", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "case_parties": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "party_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "relationship_type": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "role_description": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "case_parties_case_file_id_party_id_relationship_type_unique": {"columns": ["case_file_id", "party_id", "relationship_type"], "primary": false, "unique": true}, "case_parties_party_id_foreign": {"columns": ["party_id"], "primary": false, "unique": false}}, "foreign_keys": {"case_parties_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "case_parties_party_id_foreign": {"local_columns": ["party_id"], "foreign_table": "parties", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "case_summaries": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "content": {"type": "longtext", "nullable": false, "default": null, "autoIncrement": false}, "version": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": "1.0", "autoIncrement": false}, "updated_by": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "change_notes": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "case_summaries_case_file_id_foreign": {"columns": ["case_file_id"], "primary": false, "unique": false}, "case_summaries_updated_by_foreign": {"columns": ["updated_by"], "primary": false, "unique": false}}, "foreign_keys": {"case_summaries_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "case_summaries_updated_by_foreign": {"local_columns": ["updated_by"], "foreign_table": "users", "foreign_columns": ["id"], "on_delete": "NO ACTION", "on_update": "NO ACTION"}}}, "communication_documents": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "communication_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "document_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "communication_documents_communication_id_document_id_unique": {"columns": ["communication_id", "document_id"], "primary": false, "unique": true}, "communication_documents_document_id_foreign": {"columns": ["document_id"], "primary": false, "unique": false}}, "foreign_keys": {"communication_documents_communication_id_foreign": {"local_columns": ["communication_id"], "foreign_table": "communications", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "communication_documents_document_id_foreign": {"local_columns": ["document_id"], "foreign_table": "documents", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "communication_participants": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "communication_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "party_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "role": {"type": "enum('sender','recipient')", "nullable": false, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "communication_participants_communication_id_party_id_role_unique": {"columns": ["communication_id", "party_id", "role"], "primary": false, "unique": true}, "communication_participants_party_id_foreign": {"columns": ["party_id"], "primary": false, "unique": false}}, "foreign_keys": {"communication_participants_communication_id_foreign": {"local_columns": ["communication_id"], "foreign_table": "communications", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "communication_participants_party_id_foreign": {"local_columns": ["party_id"], "foreign_table": "parties", "foreign_columns": ["id"], "on_delete": "NO ACTION", "on_update": "NO ACTION"}}}, "communications": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "thread_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "type": {"type": "enum('email','letter','phone','other')", "nullable": false, "default": null, "autoIncrement": false}, "content": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "sent_at": {"type": "timestamp", "nullable": false, "default": null, "autoIncrement": false}, "subject": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "communications_thread_id_foreign": {"columns": ["thread_id"], "primary": false, "unique": false}}, "foreign_keys": {"communications_thread_id_foreign": {"local_columns": ["thread_id"], "foreign_table": "threads", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "docket_deadlines": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "docket_entry_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "deadline_date": {"type": "date", "nullable": false, "default": null, "autoIncrement": false}, "deadline_type": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "description": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "reminder_id": {"type": "bigint unsigned", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "docket_deadlines_docket_entry_id_foreign": {"columns": ["docket_entry_id"], "primary": false, "unique": false}, "docket_deadlines_reminder_id_foreign": {"columns": ["reminder_id"], "primary": false, "unique": false}}, "foreign_keys": {"docket_deadlines_docket_entry_id_foreign": {"local_columns": ["docket_entry_id"], "foreign_table": "docket_entries", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "docket_deadlines_reminder_id_foreign": {"local_columns": ["reminder_id"], "foreign_table": "reminders", "foreign_columns": ["id"], "on_delete": "SET NULL", "on_update": "NO ACTION"}}}, "docket_documents": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "docket_entry_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "document_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "is_primary": {"type": "tinyint(1)", "nullable": false, "default": "0", "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "unique_primary_doc": {"columns": ["docket_entry_id", "is_primary"], "primary": false, "unique": true}, "docket_documents_document_id_foreign": {"columns": ["document_id"], "primary": false, "unique": false}}, "foreign_keys": {"docket_documents_docket_entry_id_foreign": {"local_columns": ["docket_entry_id"], "foreign_table": "docket_entries", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "docket_documents_document_id_foreign": {"local_columns": ["document_id"], "foreign_table": "documents", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "docket_entries": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "entry_date": {"type": "date", "nullable": false, "default": null, "autoIncrement": false}, "entry_type": {"type": "enum('filing','order','hearing','notice','motion','judgment','other')", "nullable": false, "default": null, "autoIncrement": false}, "title": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "description": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "filing_party_id": {"type": "bigint unsigned", "nullable": true, "default": null, "autoIncrement": false}, "judge_id": {"type": "bigint unsigned", "nullable": true, "default": null, "autoIncrement": false}, "docket_number": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "status": {"type": "enum('pending','granted','denied','heard','continued','withdrawn')", "nullable": true, "default": null, "autoIncrement": false}, "is_sealed": {"type": "tinyint(1)", "nullable": false, "default": "0", "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "docket_entries_case_file_id_foreign": {"columns": ["case_file_id"], "primary": false, "unique": false}, "docket_entries_filing_party_id_foreign": {"columns": ["filing_party_id"], "primary": false, "unique": false}, "docket_entries_judge_id_foreign": {"columns": ["judge_id"], "primary": false, "unique": false}}, "foreign_keys": {"docket_entries_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "docket_entries_filing_party_id_foreign": {"local_columns": ["filing_party_id"], "foreign_table": "parties", "foreign_columns": ["id"], "on_delete": "SET NULL", "on_update": "NO ACTION"}, "docket_entries_judge_id_foreign": {"local_columns": ["judge_id"], "foreign_table": "parties", "foreign_columns": ["id"], "on_delete": "SET NULL", "on_update": "NO ACTION"}}}, "documents": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "storage_path": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "original_filename": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "mime_type": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "file_size": {"type": "bigint", "nullable": false, "default": null, "autoIncrement": false}, "title": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "description": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "openai_file_id": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "ingestion_status": {"type": "enum('pending','uploading','summarizing','indexing','indexed','failed')", "nullable": false, "default": "pending", "autoIncrement": false}, "ingestion_error": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "ingested_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "skip_vector_store": {"type": "tinyint(1)", "nullable": false, "default": "0", "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "documents_case_file_id_foreign": {"columns": ["case_file_id"], "primary": false, "unique": false}}, "foreign_keys": {"documents_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "drafts": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "draft_type": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "status": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": "draft", "autoIncrement": false}, "structured_context": {"type": "json", "nullable": true, "default": null, "autoIncrement": false}, "ai_summary": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "interview_completed_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "published_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "drafts_case_file_id_foreign": {"columns": ["case_file_id"], "primary": false, "unique": false}}, "foreign_keys": {"drafts_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "exhibits": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "draft_id": {"type": "bigint unsigned", "nullable": true, "default": null, "autoIncrement": false}, "document_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "label": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "description": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "sort_order": {"type": "smallint unsigned", "nullable": false, "default": "0", "autoIncrement": false}, "dated": {"type": "date", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "exhibits_case_file_id_foreign": {"columns": ["case_file_id"], "primary": false, "unique": false}, "exhibits_draft_id_foreign": {"columns": ["draft_id"], "primary": false, "unique": false}, "exhibits_document_id_foreign": {"columns": ["document_id"], "primary": false, "unique": false}}, "foreign_keys": {"exhibits_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "exhibits_document_id_foreign": {"local_columns": ["document_id"], "foreign_table": "documents", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "exhibits_draft_id_foreign": {"local_columns": ["draft_id"], "foreign_table": "drafts", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "failed_jobs": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "uuid": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "connection": {"type": "text", "nullable": false, "default": null, "autoIncrement": false}, "queue": {"type": "text", "nullable": false, "default": null, "autoIncrement": false}, "payload": {"type": "longtext", "nullable": false, "default": null, "autoIncrement": false}, "exception": {"type": "longtext", "nullable": false, "default": null, "autoIncrement": false}, "failed_at": {"type": "timestamp", "nullable": false, "default": "CURRENT_TIMESTAMP", "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "failed_jobs_uuid_unique": {"columns": ["uuid"], "primary": false, "unique": true}}, "foreign_keys": []}, "interview_answers": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "interview_question_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "answer_text": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "document_id": {"type": "bigint unsigned", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "interview_answers_case_file_id_foreign": {"columns": ["case_file_id"], "primary": false, "unique": false}, "interview_answers_interview_question_id_foreign": {"columns": ["interview_question_id"], "primary": false, "unique": false}, "interview_answers_document_id_foreign": {"columns": ["document_id"], "primary": false, "unique": false}}, "foreign_keys": {"interview_answers_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "interview_answers_document_id_foreign": {"local_columns": ["document_id"], "foreign_table": "documents", "foreign_columns": ["id"], "on_delete": "SET NULL", "on_update": "NO ACTION"}, "interview_answers_interview_question_id_foreign": {"local_columns": ["interview_question_id"], "foreign_table": "interview_questions", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "interview_progress": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "current_step": {"type": "int", "nullable": false, "default": "0", "autoIncrement": false}, "completed_steps": {"type": "json", "nullable": true, "default": null, "autoIncrement": false}, "step_data": {"type": "json", "nullable": true, "default": null, "autoIncrement": false}, "last_activity": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "interview_progress_case_file_id_foreign": {"columns": ["case_file_id"], "primary": false, "unique": false}}, "foreign_keys": {"interview_progress_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "interview_questions": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "question_text": {"type": "text", "nullable": false, "default": null, "autoIncrement": false}, "expected_response_type": {"type": "enum('text','voice','document','date','multiple_choice')", "nullable": false, "default": "text", "autoIncrement": false}, "multiple_choice_options": {"type": "json", "nullable": true, "default": null, "autoIncrement": false}, "question_order": {"type": "int", "nullable": false, "default": "0", "autoIncrement": false}, "is_answered": {"type": "tinyint(1)", "nullable": false, "default": "0", "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "interview_questions_case_file_id_foreign": {"columns": ["case_file_id"], "primary": false, "unique": false}}, "foreign_keys": {"interview_questions_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "job_batches": {"columns": {"id": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "name": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "total_jobs": {"type": "int", "nullable": false, "default": null, "autoIncrement": false}, "pending_jobs": {"type": "int", "nullable": false, "default": null, "autoIncrement": false}, "failed_jobs": {"type": "int", "nullable": false, "default": null, "autoIncrement": false}, "failed_job_ids": {"type": "longtext", "nullable": false, "default": null, "autoIncrement": false}, "options": {"type": "mediumtext", "nullable": true, "default": null, "autoIncrement": false}, "cancelled_at": {"type": "int", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "int", "nullable": false, "default": null, "autoIncrement": false}, "finished_at": {"type": "int", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}}, "foreign_keys": []}, "jobs": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "queue": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "payload": {"type": "longtext", "nullable": false, "default": null, "autoIncrement": false}, "attempts": {"type": "tinyint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "reserved_at": {"type": "int unsigned", "nullable": true, "default": null, "autoIncrement": false}, "available_at": {"type": "int unsigned", "nullable": false, "default": null, "autoIncrement": false}, "created_at": {"type": "int unsigned", "nullable": false, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "jobs_queue_index": {"columns": ["queue"], "primary": false, "unique": false}}, "foreign_keys": []}, "migrations": {"columns": {"id": {"type": "int unsigned", "nullable": false, "default": null, "autoIncrement": true}, "migration": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "batch": {"type": "int", "nullable": false, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}}, "foreign_keys": []}, "notifications": {"columns": {"id": {"type": "char(36)", "nullable": false, "default": null, "autoIncrement": false}, "type": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "notifiable_type": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "notifiable_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "data": {"type": "text", "nullable": false, "default": null, "autoIncrement": false}, "read_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "notifications_notifiable_type_notifiable_id_index": {"columns": ["notifiable_type", "notifiable_id"], "primary": false, "unique": false}}, "foreign_keys": []}, "openai_projects": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "name": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "api_key": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "organization_id": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "storage_used": {"type": "bigint unsigned", "nullable": false, "default": "0", "autoIncrement": false}, "is_active": {"type": "tinyint(1)", "nullable": false, "default": "1", "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}}, "foreign_keys": []}, "parties": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "user_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "name": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "address_line1": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "address_line2": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "city": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "state": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "zip": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "email": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "phone": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "relationship": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "parties_user_id_foreign": {"columns": ["user_id"], "primary": false, "unique": false}}, "foreign_keys": {"parties_user_id_foreign": {"local_columns": ["user_id"], "foreign_table": "users", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "password_reset_tokens": {"columns": {"email": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "token": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["email"], "primary": true, "unique": true}}, "foreign_keys": []}, "personal_access_tokens": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "tokenable_type": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "tokenable_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "name": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "token": {"type": "<PERSON><PERSON><PERSON>(64)", "nullable": false, "default": null, "autoIncrement": false}, "abilities": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "last_used_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "expires_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "personal_access_tokens_token_unique": {"columns": ["token"], "primary": false, "unique": true}, "personal_access_tokens_tokenable_type_tokenable_id_index": {"columns": ["tokenable_type", "tokenable_id"], "primary": false, "unique": false}}, "foreign_keys": []}, "reminders": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "title": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "description": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "due_date": {"type": "date", "nullable": false, "default": null, "autoIncrement": false}, "completed_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "is_recurring": {"type": "tinyint(1)", "nullable": false, "default": "0", "autoIncrement": false}, "recurrence_pattern": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "reminders_case_file_id_foreign": {"columns": ["case_file_id"], "primary": false, "unique": false}}, "foreign_keys": {"reminders_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "sessions": {"columns": {"id": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "user_id": {"type": "bigint unsigned", "nullable": true, "default": null, "autoIncrement": false}, "ip_address": {"type": "<PERSON><PERSON><PERSON>(45)", "nullable": true, "default": null, "autoIncrement": false}, "user_agent": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "payload": {"type": "longtext", "nullable": false, "default": null, "autoIncrement": false}, "last_activity": {"type": "int", "nullable": false, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "sessions_user_id_index": {"columns": ["user_id"], "primary": false, "unique": false}, "sessions_last_activity_index": {"columns": ["last_activity"], "primary": false, "unique": false}}, "foreign_keys": []}, "team_invitations": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "team_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "email": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "role": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "team_invitations_team_id_email_unique": {"columns": ["team_id", "email"], "primary": false, "unique": true}}, "foreign_keys": {"team_invitations_team_id_foreign": {"local_columns": ["team_id"], "foreign_table": "teams", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "team_user": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "team_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "user_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "role": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "team_user_team_id_user_id_unique": {"columns": ["team_id", "user_id"], "primary": false, "unique": true}, "team_user_user_id_foreign": {"columns": ["user_id"], "primary": false, "unique": false}}, "foreign_keys": {"team_user_team_id_foreign": {"local_columns": ["team_id"], "foreign_table": "teams", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "team_user_user_id_foreign": {"local_columns": ["user_id"], "foreign_table": "users", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "assistant_threads": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "title": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "description": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "category": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": true, "default": null, "autoIncrement": false}, "openai_thread_id": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "created_by": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "last_activity_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "assistant_threads_case_file_id_index": {"columns": ["case_file_id"], "primary": false, "unique": false}, "assistant_threads_openai_thread_id_index": {"columns": ["openai_thread_id"], "primary": false, "unique": false}, "assistant_threads_created_by_foreign": {"columns": ["created_by"], "primary": false, "unique": false}}, "foreign_keys": {"assistant_threads_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "assistant_threads_created_by_foreign": {"local_columns": ["created_by"], "foreign_table": "users", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}}}, "assistant_messages": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "assistant_thread_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "role": {"type": "enum('user','assistant')", "nullable": false, "default": null, "autoIncrement": false}, "content": {"type": "text", "nullable": false, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "team_user_team_id_user_id_unique": {"columns": ["team_id", "user_id"], "primary": false, "unique": true}}, "foreign_keys": []}, "teams": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "user_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "name": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "personal_team": {"type": "tinyint(1)", "nullable": false, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "teams_user_id_index": {"columns": ["user_id"], "primary": false, "unique": false}}, "foreign_keys": []}, "threads": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "case_file_id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "title": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "status": {"type": "enum('open','closed','archived')", "nullable": false, "default": "open", "autoIncrement": false}, "created_by": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "threads_case_file_id_foreign": {"columns": ["case_file_id"], "primary": false, "unique": false}, "threads_created_by_foreign": {"columns": ["created_by"], "primary": false, "unique": false}}, "foreign_keys": {"threads_case_file_id_foreign": {"local_columns": ["case_file_id"], "foreign_table": "case_files", "foreign_columns": ["id"], "on_delete": "CASCADE", "on_update": "NO ACTION"}, "threads_created_by_foreign": {"local_columns": ["created_by"], "foreign_table": "users", "foreign_columns": ["id"], "on_delete": "NO ACTION", "on_update": "NO ACTION"}}}, "users": {"columns": {"id": {"type": "bigint unsigned", "nullable": false, "default": null, "autoIncrement": true}, "name": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "email": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "email_verified_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "password": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": null, "autoIncrement": false}, "two_factor_secret": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "two_factor_recovery_codes": {"type": "text", "nullable": true, "default": null, "autoIncrement": false}, "two_factor_confirmed_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "remember_token": {"type": "<PERSON><PERSON><PERSON>(100)", "nullable": true, "default": null, "autoIncrement": false}, "current_team_id": {"type": "bigint unsigned", "nullable": true, "default": null, "autoIncrement": false}, "profile_photo_path": {"type": "<PERSON><PERSON><PERSON>(2048)", "nullable": true, "default": null, "autoIncrement": false}, "created_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "updated_at": {"type": "timestamp", "nullable": true, "default": null, "autoIncrement": false}, "language": {"type": "<PERSON><PERSON><PERSON>(255)", "nullable": false, "default": "en", "autoIncrement": false}}, "indexes": {"PRIMARY": {"columns": ["id"], "primary": true, "unique": true}, "users_email_unique": {"columns": ["email"], "primary": false, "unique": true}}, "foreign_keys": []}}