<?php
require_once 'vendor/autoload.php';

use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\Style\Table;

function generateLegalDocument($caseDetails) {
    $phpWord = new PhpWord();
    $phpWord->setDefaultFontName('Times New Roman');
    $phpWord->setDefaultFontSize(14);

    $section = $phpWord->addSection();

    // Header
    $section->addText(
        'IN THE UNITED STATES DISTRICT COURT FOR THE NORTHERN DISTRICT OF GEORGIA',
        ['bold' => true, 'allCaps' => true, 'size' => 14],
        ['alignment' => 'center']
    );

    $section->addText(
        'Atlanta Division',
        ['bold' => true, 'size' => 14],
        ['alignment' => 'center']
    );

    // Space before caption table
    $section->addText('', ['size' => 14], ['spacing' => 120]);

    // Caption table
    $table = $section->addTable([
        'width' => 100 * 50,
        'unit' => 'pct',
        'alignment' => 'center',
        'layout' => 'fixed'
    ]);

    $row = $table->addRow();
    $cell1 = $row->addCell(5000, [ // Increased width of the left cell
                                   'valign' => 'center',
                                   'borderRightSize' => 1,
                                   'borderRightColor' => '000000',
                                   'spaceAfter' => 0,
                                   'spaceBefore' => 0,
                                   'padding' => 120,
    ]);

// Left cell content
    $cell1->addText('Ian Bruce,', ['size' => 14]);
    $cell1->addText('', ['size' => 14]);
    $cell1->addText('Petitioner,', ['size' => 14]);
    $cell1->addText('', ['size' => 14]);
    $cell1->addText('V.', ['size' => 14]);
    $cell1->addText('', ['size' => 14]);
    $cell1->addText('WESTLAKE SERVICES, LLC dba', ['size' => 14]);
    $cell1->addText('WESTLAKE FINANCIAL,', ['size' => 14]);
    $cell1->addText('', ['size' => 14]);
    $cell1->addText('Defendant,', ['size' => 14]);

    $cell2 = $row->addCell(5000, [ // Decreased width of the right cell
                                   'valign' => 'center',
                                   'spaceAfter' => 0,
                                   'spaceBefore' => 0,
                                   'padding' => 240,
                                   'paddingLeft' => 360
    ]);

// Right cell content
    $cell2->addText('Civil Action File No.', ['size' => 14]);
    $cell2->addText('', ['size' => 14]);
    $cell2->addText('1:24-cv-05040-MLB', ['underline' => 'single', 'size' => 14]);
    $cell2->addText('', ['size' => 14]);
    $cell2->addText('Assigned to: HON. JUDGE MICHAEL L BROWN', ['underline' => 'single', 'size' => 14]);



    // Extra space after caption (table)
    $section->addText('', ['size' => 14], ['spacing' => 480]);

    // Motion Title with space after
    $section->addText(
        'MOTION TO COMPEL ARBITRATION AND STAY PROCEEDINGS',
        ['bold' => true, 'underline' => 'single', 'size' => 14],
        ['alignment' => 'center']
    );
    $section->addText('', ['size' => 14], ['spacing' => 240]);

    // To The Court text with space after
    $section->addText(
        'TO THE COURT AND ALL PARTIES HEREIN:',
        ['bold' => true, 'size' => 14],
        ['alignment' => 'center']
    );
    $section->addText('', ['size' => 14], ['spacing' => 240]);

    // Body text style
    $bodyStyle = [
        'alignment' => 'both',
        'lineHeight' => 2.0
    ];

    // Comes Now paragraph
    $section->addText(
        'COMES NOW Petitioner Ian Bruce, proceeding pro se, and moves this Court to compel arbitration and stay these proceedings pursuant to the Federal Arbitration Act ("FAA"), 9 U.S.C. §§ 3-4. This motion is based on the following grounds:',
        ['size' => 14],
        $bodyStyle
    );

    // Indented text style
    $indentStyle = [
        'alignment' => 'both',
        'indentation' => ['left' => 720, 'hanging' => 360],
        'lineHeight' => 2.0
    ];

    // Numbered points
    $points = [
        'The contract between parties contains a valid, binding arbitration provision;',
        'Petitioner initiated arbitration (JAMS Reference No. 5220005072) on January 11, 2024;',
        'The arbitration provision covers all disputes between the parties relating to the contract;',
        'Petitioner\'s original complaint was filed, in part, seeking reimbursement of the arbitration fees paid, demonstrating an intent to enforce the arbitration clause.',
        'Despite Petitioner\'s efforts to maintain arbitration, including paying Defendant\'s fees, Defendant explicitly refused on November 7, 2024 stating, \'we will not be proceeding in arbitration.',
        'The arbitration provision explicitly survives any legal proceedings between the parties;',
        'All current disputes fall within the scope of arbitration.',
        'The disputes subject to arbitration include claims under both federal and California state law, demonstrating the comprehensive scope of the arbitration provision;'
    ];

    foreach ($points as $index => $point) {
        $section->addText(
            ($index + 1) . '. ' . $point,
            ['size' => 14],
            $indentStyle
        );
    }

    // Prayer for Relief title with space after
    $section->addText(
        'PRAYER FOR RELIEF',
        ['bold' => true, 'size' => 14],
        ['alignment' => 'center', 'spaceBefore' => 360]
    );
    $section->addText('', ['size' => 14], ['spacing' => 240]);

    $section->addText(
        'WHEREFORE, Petitioner respectfully requests that this Court:',
        ['size' => 14],
        $bodyStyle
    );

    // Prayer points
    $prayers = [
        'Compel the parties to arbitration pursuant to 9 U.S.C. § 4;',
        'Stay these proceedings pending completion of arbitration pursuant to 9 U.S.C. § 3;',
        'Grant such other relief as this Court deems just and proper.'
    ];

    foreach ($prayers as $index => $prayer) {
        $section->addText(
            ($index + 1) . '. ' . $prayer,
            ['size' => 14],
            $indentStyle
        );
    }

    // Signature block
    $section->addText(
        'Respectfully submitted,',
        ['bold' => true, 'size' => 14],
        ['alignment' => 'left', 'spaceBefore' => 360]
    );

    $section->addText(
        '_______________________________',
        ['size' => 14],
        ['alignment' => 'left']
    );

    $section->addText('Ian Bruce, Petitioner', ['size' => 14], ['alignment' => 'left']);
    $section->addText('267 Langley Dr #1267', ['size' => 14], ['alignment' => 'left']);
    $section->addText('Lawrenceville, Georgia', ['size' => 14], ['alignment' => 'left']);
    $section->addText('<EMAIL>', ['size' => 14], ['alignment' => 'left']);

    return $phpWord;
}

try {
    $caseDetails = [
        'caseNumber' => '1:24-cv-05040-MLB',
        'judge' => 'HON. JUDGE MICHAEL L BROWN',
        'petitioner' => 'Ian Bruce',
        'defendant' => 'WESTLAKE SERVICES, LLC dba WESTLAKE FINANCIAL'
    ];

    $phpWord = generateLegalDocument($caseDetails);
    $date = date("ymdhis");
    $save = sprintf('/Users/<USER>/Downloads/%s_MotionTemplate.docx', $date);
    $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
    $objWriter->save($save);

    echo "Document generated successfully!";
} catch (Exception $e) {
    echo "Error generating document: " . $e->getMessage();
}
?>