{"private": true, "type": "module", "scripts": {"build": "vite build", "dev": "vite", "watch": "vite build --watch"}, "devDependencies": {"@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "autoprefixer": "^10.4.16", "axios": "^1.7.4", "concurrently": "^9.0.1", "laravel-vite-plugin": "^1.0", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "vite": "^6.0"}, "dependencies": {"@editorjs/editorjs": "^2.30.8", "@editorjs/header": "^2.8.8", "@editorjs/inline-code": "^1.5.1", "@editorjs/list": "^2.0.6", "@editorjs/marker": "^1.4.0", "@editorjs/quote": "^2.7.6", "@editorjs/table": "^2.4.4", "@tiptap/core": "^2.11.7", "@tiptap/extension-font-family": "^2.11.7", "@tiptap/extension-table": "^2.11.7", "@tiptap/extension-table-cell": "^2.11.7", "@tiptap/extension-table-header": "^2.11.7", "@tiptap/extension-table-row": "^2.11.7", "@tiptap/extension-text-align": "^2.11.7", "@tiptap/extension-text-style": "^2.11.7", "@tiptap/extension-underline": "^2.11.7", "@tiptap/starter-kit": "^2.11.7", "daisyui": "^4.12.23", "docx-preview": "^0.3.5", "editorjs-paragraph-with-alignment": "^3.0.0", "editorjs-text-alignment-blocktune": "^1.0.3", "jszip": "^3.10.1", "laravel-echo": "^2.0.2", "pusher-js": "^8.4.0", "quill": "^2.0.3", "theme-change": "^2.5.0", "trix": "^2.1.13"}}