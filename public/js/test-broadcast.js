/**
 * Test script for broadcasting
 * 
 * To use this script:
 * 1. Open your browser console
 * 2. Run: $.getScript('/js/test-broadcast.js')
 * 3. Then run: testBroadcast(5) - replace 5 with your thread ID
 */

function testBroadcast(threadId) {
    if (!window.Echo) {
        console.error('Echo is not initialized!');
        return;
    }

    console.log(`Testing broadcast to private-chat.${threadId}`);
    
    // Test if we can listen to the channel
    try {
        window.Echo.private(`private-chat.${threadId}`)
            .listen('.new-message', (e) => {
                console.log('TEST LISTENER received event:', e);
                alert('TEST LISTENER received event!');
            });
        console.log(`Successfully subscribed to private-chat.${threadId}`);
    } catch (error) {
        console.error('Error subscribing to channel:', error);
    }
    
    // Test if we can whisper to the channel
    try {
        window.Echo.private(`private-chat.${threadId}`)
            .whisper('typing', {
                user: 'Test User',
                typing: true,
                time: new Date().toISOString()
            });
        console.log('Whisper sent to channel');
    } catch (error) {
        console.error('Error sending whisper:', error);
    }
    
    // Make an AJAX call to trigger the test broadcast command
    fetch(`/api/test-broadcast/${threadId}`)
        .then(response => response.json())
        .then(data => {
            console.log('Test broadcast API response:', data);
        })
        .catch(error => {
            console.error('Error calling test broadcast API:', error);
        });
}
