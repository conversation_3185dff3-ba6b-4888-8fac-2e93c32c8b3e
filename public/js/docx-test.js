// Simple test script to verify docx-preview functionality
console.log('DocxJS Test Script: Loaded');

// Check if the docx object is available
if (typeof docx !== 'undefined') {
    console.log('DocxJS Test: Library is available', docx);
} else {
    console.error('DocxJS Test: Library is NOT available');
}

// Add a global function to test rendering
window.testDocxRendering = function(url) {
    console.log('DocxJS Test: Testing rendering with URL', url);
    
    // Create a test container
    const testContainer = document.createElement('div');
    testContainer.id = 'docx-test-container';
    testContainer.style.position = 'fixed';
    testContainer.style.top = '50%';
    testContainer.style.left = '50%';
    testContainer.style.transform = 'translate(-50%, -50%)';
    testContainer.style.width = '80%';
    testContainer.style.height = '80%';
    testContainer.style.backgroundColor = 'white';
    testContainer.style.zIndex = '9999';
    testContainer.style.padding = '20px';
    testContainer.style.boxShadow = '0 0 10px rgba(0,0,0,0.5)';
    testContainer.style.overflow = 'auto';
    
    // Add a close button
    const closeButton = document.createElement('button');
    closeButton.textContent = 'Close Test';
    closeButton.style.position = 'absolute';
    closeButton.style.top = '10px';
    closeButton.style.right = '10px';
    closeButton.style.zIndex = '10000';
    closeButton.onclick = function() {
        document.body.removeChild(testContainer);
    };
    
    // Add a content container
    const contentContainer = document.createElement('div');
    contentContainer.id = 'docx-test-content';
    contentContainer.style.width = '100%';
    contentContainer.style.height = 'calc(100% - 40px)';
    contentContainer.style.marginTop = '40px';
    
    // Add elements to the DOM
    testContainer.appendChild(closeButton);
    testContainer.appendChild(contentContainer);
    document.body.appendChild(testContainer);
    
    // Show loading message
    contentContainer.innerHTML = '<div style="text-align: center; padding: 20px;">Loading preview...</div>';
    
    // Fetch and render the document
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Failed to fetch document: ${response.statusText}`);
            }
            return response.blob();
        })
        .then(blob => {
            try {
                return docx.renderAsync(blob, contentContainer);
            } catch (error) {
                throw new Error(`Rendering error: ${error.message}`);
            }
        })
        .then(() => {
            console.log('DocxJS Test: Rendering completed successfully');
        })
        .catch(error => {
            console.error('DocxJS Test: Error', error);
            contentContainer.innerHTML = `
                <div style="padding: 20px; color: red;">
                    <h3>Error</h3>
                    <p>${error.message}</p>
                    <pre>${error.stack || 'No stack trace available'}</pre>
                </div>
            `;
        });
};
