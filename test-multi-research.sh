#!/bin/bash

# Make the script executable
chmod +x test-multi-research.sh

# Default values
API_URL="https://research.justicequest.pro/api/multi-research"
FILE_IDENTIFIER=""
MAX_SECTIONS=3
INCLUDE_HUMAN_FEEDBACK=false
FOLLOW_GUIDELINES=false
GUIDELINES=()
MODEL="gpt-4.1-mini"
MARKDOWN=true
PDF=false
DOCX=false
SAVE_TO="./research"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --url=*)
      API_URL="${1#*=}"
      shift
      ;;
    --file-id=*)
      FILE_IDENTIFIER="${1#*=}"
      shift
      ;;
    --sections=*)
      MAX_SECTIONS="${1#*=}"
      shift
      ;;
    --human-feedback)
      INCLUDE_HUMAN_FEEDBACK=true
      shift
      ;;
    --follow-guidelines)
      FOLLOW_GUIDELINES=true
      shift
      ;;
    --guideline=*)
      GUIDELINES+=("${1#*=}")
      shift
      ;;
    --model=*)
      MODEL="${1#*=}"
      shift
      ;;
    --markdown)
      MARKDOWN=true
      shift
      ;;
    --pdf)
      PDF=true
      shift
      ;;
    --docx)
      DOCX=true
      shift
      ;;
    --verbose)
      CMD="$CMD --enable-verbose"
      shift
      ;;
    --save-to=*)
      SAVE_TO="${1#*=}"
      shift
      ;;
    *)
      QUESTION="$1"
      shift
      ;;
  esac
done

# Build the command
CMD="php artisan test:multi-research"

if [ ! -z "$QUESTION" ]; then
  # Escape quotes in the question
  QUESTION=$(echo "$QUESTION" | sed 's/"/\\"/g')
  CMD="$CMD \"$QUESTION\""
fi

if [ ! -z "$FILE_IDENTIFIER" ]; then
  CMD="$CMD --file-identifier=\"$FILE_IDENTIFIER\""
fi

CMD="$CMD --max-sections=$MAX_SECTIONS"

if [ "$INCLUDE_HUMAN_FEEDBACK" = true ]; then
  CMD="$CMD --include-human-feedback"
fi

if [ "$FOLLOW_GUIDELINES" = true ]; then
  CMD="$CMD --follow-guidelines"
fi

for guideline in "${GUIDELINES[@]}"; do
  CMD="$CMD --guidelines=\"$guideline\""
done

CMD="$CMD --model=\"$MODEL\""

if [ "$MARKDOWN" = true ]; then
  CMD="$CMD --markdown"
fi

if [ "$PDF" = true ]; then
  CMD="$CMD --pdf"
fi

if [ "$DOCX" = true ]; then
  CMD="$CMD --docx"
fi

# Verbose flag is passed directly when specified on command line

if [ ! -z "$API_URL" ]; then
  CMD="$CMD --api-url=\"$API_URL\""
fi

CMD="$CMD --save-to=\"$SAVE_TO\""

# Execute the command
eval $CMD
