<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vector_stores', function (Blueprint $table) {
            $table->id();
            $table->string('vector_store_id')->unique()->comment('OpenAI vector store ID');
            $table->string('name');
            $table->text('description')->nullable();
            $table->json('source_data')->nullable()->comment('Original data used to create the vector store');
            $table->timestamp('last_refreshed_at')->nullable();
            $table->timestamp('expires_at')->nullable()->comment('When this vector store will expire');
            $table->timestamps();

            $table->index('vector_store_id');
            $table->index('expires_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vector_stores');
    }
};
