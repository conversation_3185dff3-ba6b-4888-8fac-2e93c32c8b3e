<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // User credits table - stores the current balance for each user
        Schema::create('user_credits', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer('balance')->default(0);
            $table->bigInteger('lifetime_earned')->default(0);
            $table->bigInteger('lifetime_spent')->default(0);
            $table->timestamps();
            
            // Each user can only have one credit record
            $table->unique('user_id');
        });

        // Credit transactions table - records all credit movements
        Schema::create('credit_transactions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->integer('amount'); // Positive for additions, negative for deductions
            $table->integer('balance_after'); // Balance after this transaction
            $table->string('description');
            $table->json('metadata')->nullable(); // For storing additional context
            $table->timestamps();
            
            // Index for faster queries
            $table->index('user_id');
            $table->index('created_at');
        });

        // Credit products table - stores available credit packages for purchase
        Schema::create('credit_products', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('description')->nullable();
            $table->integer('credits');
            $table->integer('price_cents');
            $table->string('stripe_price_id')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('credit_transactions');
        Schema::dropIfExists('user_credits');
        Schema::dropIfExists('credit_products');
    }
};
