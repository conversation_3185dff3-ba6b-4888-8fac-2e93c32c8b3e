<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Invoices table - stores invoice details
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number')->unique();
            $table->foreignId('creator_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('recipient_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('case_file_id')->nullable()->constrained()->onDelete('set null');
            $table->integer('amount_cents');
            $table->integer('platform_fee_cents');
            $table->integer('tax_cents')->default(0);
            $table->integer('total_cents');
            $table->string('status', 20); // draft, sent, paid, partial, overdue, cancelled
            $table->text('notes')->nullable();
            $table->date('due_date');
            $table->date('issued_date');
            $table->date('paid_date')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            // Indexes for faster queries
            $table->index('creator_id');
            $table->index('recipient_id');
            $table->index('status');
            $table->index('due_date');
        });

        // Invoice items table - stores line items for each invoice
        Schema::create('invoice_items', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->text('description');
            $table->decimal('quantity', 10, 2);
            $table->integer('unit_price_cents');
            $table->integer('amount_cents');
            $table->decimal('tax_rate', 5, 2)->nullable();
            $table->integer('tax_cents')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            // Index for faster queries
            $table->index('invoice_id');
        });

        // Invoice payments table - stores payment records for invoices
        Schema::create('invoice_payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->integer('amount_cents');
            $table->string('payment_method', 50); // credit_card, bank_transfer, platform_credits
            $table->string('payment_reference')->nullable();
            $table->string('stripe_payment_intent_id')->nullable();
            $table->string('status', 20); // pending, completed, failed, refunded
            $table->foreignId('paid_by_id')->constrained('users')->onDelete('cascade');
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            // Indexes for faster queries
            $table->index('invoice_id');
            $table->index('status');
            $table->index('stripe_payment_intent_id');
        });

        // Connect accounts table - stores Stripe Connect account information
        Schema::create('connect_accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('stripe_account_id');
            $table->boolean('charges_enabled')->default(false);
            $table->boolean('payouts_enabled')->default(false);
            $table->boolean('details_submitted')->default(false);
            $table->string('account_type', 20); // express, standard, custom
            $table->string('country', 2);
            $table->string('default_currency', 3);
            $table->json('requirements')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamps();
            
            // Each user can only have one Connect account
            $table->unique('user_id');
            $table->index('stripe_account_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_payments');
        Schema::dropIfExists('invoice_items');
        Schema::dropIfExists('invoices');
        Schema::dropIfExists('connect_accounts');
    }
};
