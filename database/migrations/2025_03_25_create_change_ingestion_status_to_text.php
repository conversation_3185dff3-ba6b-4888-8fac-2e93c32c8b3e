<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('documents', function (Blueprint $table) {
            // Only drop constraint if we're using PostgreSQL
            if (DB::getDriverName() === 'pgsql') {
                DB::statement('ALTER TABLE documents DROP CONSTRAINT IF EXISTS documents_ingestion_status_check');
            }
            $table->string('ingestion_status')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('documents', function (Blueprint $table) {
            $table->enum('ingestion_status', ['pending', 'uploading', 'summarizing', 'indexing', 'indexed', 'failed'])->change();
        });
    }
};
