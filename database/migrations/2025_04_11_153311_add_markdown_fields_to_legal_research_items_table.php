<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('legal_research_items', function (Blueprint $table) {
            $table->text('research_markdown_content')->nullable()->comment('Markdown content of the research report');
            $table->string('research_local_path')->nullable()->comment('Local path to the markdown file');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('legal_research_items', function (Blueprint $table) {
            $table->dropColumn([
                'research_markdown_content',
                'research_local_path'
            ]);
        });
    }
};
