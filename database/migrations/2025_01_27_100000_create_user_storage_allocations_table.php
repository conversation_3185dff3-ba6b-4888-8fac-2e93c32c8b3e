<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_storage_allocations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('openai_project_id')->constrained()->onDelete('cascade');
            $table->bigInteger('guaranteed_bytes');      // Guaranteed storage allocation
            $table->bigInteger('used_bytes')->default(0); // Currently used storage
            $table->string('subscription_tier');         // Track which tier this allocation is for
            $table->timestamp('allocated_at');
            $table->timestamps();
            
            $table->unique('user_id'); // Each user can only have one allocation
            $table->index(['openai_project_id', 'subscription_tier']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_storage_allocations');
    }
};
