<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('assistant_threads', function (Blueprint $table) {
            $table->string('type')->nullable()->after('category')->comment('Type of thread (e.g., chat, interview, research, drafting)');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('assistant_threads', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
};
