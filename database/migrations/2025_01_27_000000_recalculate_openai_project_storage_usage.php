<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\OpenAiProject;
use Illuminate\Support\Facades\Log;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Log::info('Starting OpenAI project storage usage recalculation');

        $projects = OpenAiProject::with(['caseFiles.documents'])->get();

        foreach ($projects as $project) {
            $totalStorage = 0;
            $documentCount = 0;

            foreach ($project->caseFiles as $caseFile) {
                foreach ($caseFile->documents as $document) {
                    if ($document->file_size) {
                        $totalStorage += $document->file_size;
                        $documentCount++;
                    }
                }
            }

            $oldStorage = $project->storage_used;
            $project->update(['storage_used' => $totalStorage]);

            Log::info('Updated OpenAI project storage usage', [
                'project_id' => $project->id,
                'project_name' => $project->name,
                'old_storage_used' => $oldStorage,
                'new_storage_used' => $totalStorage,
                'document_count' => $documentCount,
                'storage_gb' => round($totalStorage / (1024 * 1024 * 1024), 2)
            ]);
        }

        Log::info('Completed OpenAI project storage usage recalculation', [
            'projects_updated' => $projects->count()
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Reset all storage_used values to 0
        Log::info('Resetting OpenAI project storage usage to 0');
        
        OpenAiProject::query()->update(['storage_used' => 0]);
        
        Log::info('Reset completed');
    }
};
