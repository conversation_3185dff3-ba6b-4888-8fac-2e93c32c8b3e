<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('openai_project_capacity', function (Blueprint $table) {
            $table->id();
            $table->foreignId('openai_project_id')->constrained()->onDelete('cascade');
            $table->bigInteger('total_capacity_bytes')->default(100 * 1024 * 1024 * 1024); // 100GB default
            $table->bigInteger('allocated_bytes')->default(0);  // Total allocated to users
            $table->bigInteger('used_bytes')->default(0);       // Actually used storage
            $table->bigInteger('reserved_bytes')->default(5 * 1024 * 1024 * 1024); // 5GB buffer
            $table->integer('basic_users')->default(0);         // Count of basic users
            $table->integer('standard_users')->default(0);      // Count of standard users  
            $table->integer('pro_users')->default(0);           // Count of pro users
            $table->boolean('accepts_new_users')->default(true); // Can accept new allocations
            $table->timestamps();
            
            $table->unique('openai_project_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('openai_project_capacity');
    }
};
