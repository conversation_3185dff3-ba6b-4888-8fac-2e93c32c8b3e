<?php

namespace Database\Factories;

use App\Models\OpenAiProject;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\OpenAiProject>
 */
class OpenAiProjectFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = OpenAiProject::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->company() . ' Project',
            'api_key' => 'sk-test-' . $this->faker->regexify('[A-Za-z0-9]{48}'),
            'organization_id' => 'org-' . $this->faker->regexify('[A-Za-z0-9]{24}'),
            'storage_used' => 0,
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the project is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Set a specific storage usage amount.
     */
    public function withStorageUsed(int $bytes): static
    {
        return $this->state(fn (array $attributes) => [
            'storage_used' => $bytes,
        ]);
    }

    /**
     * Set storage usage to near capacity (95GB).
     */
    public function nearCapacity(): static
    {
        return $this->state(fn (array $attributes) => [
            'storage_used' => 95 * 1024 * 1024 * 1024, // 95GB
        ]);
    }
}
