<?php

namespace Database\Factories;

use App\Models\Document;
use App\Models\CaseFile;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Document>
 */
class DocumentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Document::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $filename = $this->faker->word() . '.' . $this->faker->randomElement(['pdf', 'docx', 'txt']);
        
        return [
            'case_file_id' => CaseFile::factory(),
            'storage_path' => 'test/documents/' . $filename,
            'original_filename' => $filename,
            'mime_type' => $this->getMimeType($filename),
            'file_size' => $this->faker->numberBetween(1024, 10 * 1024 * 1024), // 1KB to 10MB
            'title' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph(),
            'ingestion_status' => 'completed',
            'skip_vector_store' => false,
            'document_type' => 'exhibit',
        ];
    }

    /**
     * Get MIME type based on file extension.
     */
    private function getMimeType(string $filename): string
    {
        $extension = pathinfo($filename, PATHINFO_EXTENSION);
        
        return match($extension) {
            'pdf' => 'application/pdf',
            'docx' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'txt' => 'text/plain',
            default => 'application/octet-stream'
        };
    }

    /**
     * Create a PDF document.
     */
    public function pdf(): static
    {
        return $this->state(fn (array $attributes) => [
            'original_filename' => $this->faker->word() . '.pdf',
            'mime_type' => 'application/pdf',
        ]);
    }

    /**
     * Create a Word document.
     */
    public function docx(): static
    {
        return $this->state(fn (array $attributes) => [
            'original_filename' => $this->faker->word() . '.docx',
            'mime_type' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ]);
    }

    /**
     * Create a document with specific file size.
     */
    public function withFileSize(int $bytes): static
    {
        return $this->state(fn (array $attributes) => [
            'file_size' => $bytes,
        ]);
    }

    /**
     * Create a large document (100MB).
     */
    public function large(): static
    {
        return $this->state(fn (array $attributes) => [
            'file_size' => 100 * 1024 * 1024, // 100MB
        ]);
    }

    /**
     * Create a document that skips vector store.
     */
    public function skipVectorStore(): static
    {
        return $this->state(fn (array $attributes) => [
            'skip_vector_store' => true,
        ]);
    }
}
