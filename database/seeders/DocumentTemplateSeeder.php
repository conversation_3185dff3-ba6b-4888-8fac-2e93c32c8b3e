<?php

namespace Database\Seeders;

use App\Models\DocumentTemplate;
use Illuminate\Database\Seeder;

class DocumentTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a standard complaint template
        DocumentTemplate::create([
            'name' => 'Standard Complaint',
            'document_type' => 'complaint',
            'description' => 'A standard complaint template with all required sections for filing in court.',
            'structure' => json_encode([
                ['id' => 'caption', 'name' => 'Caption', 'required' => true, 'order' => 1],
                ['id' => 'introduction', 'name' => 'Introduction', 'required' => true, 'order' => 2],
                ['id' => 'jurisdiction_venue', 'name' => 'Jurisdiction and Venue', 'required' => true, 'order' => 3],
                ['id' => 'parties', 'name' => 'Parties', 'required' => true, 'order' => 4],
                ['id' => 'factual_allegations', 'name' => 'Factual Allegations', 'required' => true, 'order' => 5],
                ['id' => 'causes_of_action', 'name' => 'Causes of Action', 'required' => true, 'order' => 6],
                ['id' => 'prayer_for_relief', 'name' => 'Prayer for Relief', 'required' => true, 'order' => 7],
                ['id' => 'signature', 'name' => 'Signature Block', 'required' => true, 'order' => 8]
            ]),
            'default_content' => json_encode([
                'caption' => 'IN THE SUPERIOR COURT OF THE STATE OF [STATE]\nIN AND FOR THE COUNTY OF [COUNTY]\n\n[PLAINTIFF NAME],\nPlaintiff,\n\nvs.\n\n[DEFENDANT NAME],\nDefendant.',
                'introduction' => 'COMES NOW the Plaintiff, [PLAINTIFF NAME], by and through undersigned counsel, and files this Complaint against Defendant [DEFENDANT NAME], and alleges as follows:',
                'jurisdiction_venue' => '1. This Court has jurisdiction over this matter pursuant to [JURISDICTION BASIS].\n2. Venue is proper in this Court pursuant to [VENUE BASIS].',
                'parties' => '3. Plaintiff [PLAINTIFF NAME] is a [INDIVIDUAL/ENTITY] residing at/with principal place of business at [ADDRESS].\n4. Defendant [DEFENDANT NAME] is a [INDIVIDUAL/ENTITY] residing at/with principal place of business at [ADDRESS].',
                'factual_allegations' => '5. [FACTUAL ALLEGATIONS]',
                'causes_of_action' => 'FIRST CAUSE OF ACTION\n[CAUSE OF ACTION TITLE]\n\n6. Plaintiff incorporates by reference all preceding paragraphs as if fully set forth herein.\n7. [ELEMENTS OF CAUSE OF ACTION]',
                'prayer_for_relief' => 'WHEREFORE, Plaintiff prays for judgment against Defendant as follows:\n\n1. For general damages in an amount to be proven at trial;\n2. For special damages in an amount to be proven at trial;\n3. For costs of suit incurred herein;\n4. For such other and further relief as the Court deems just and proper.',
                'signature' => 'Dated: [DATE]\n\nRespectfully submitted,\n\n_________________________\n[ATTORNEY NAME]\nAttorney for Plaintiff'
            ]),
            'ai_prompts' => json_encode([
                'caption' => 'Generate a caption for a complaint in {jurisdiction} court with plaintiff {plaintiff_name} and defendant {defendant_name}.',
                'introduction' => 'Write an introduction paragraph for a complaint regarding {case_type}.',
                'jurisdiction_venue' => 'Explain the jurisdiction and venue for this case in {jurisdiction} based on {jurisdiction_basis}.',
                'parties' => 'Describe the parties involved in this case: plaintiff {plaintiff_name} and defendant {defendant_name}.',
                'factual_allegations' => 'Write factual allegations for a {case_type} case based on the following facts: {case_facts}',
                'causes_of_action' => 'Generate causes of action for a {case_type} case based on the following facts: {case_facts}',
                'prayer_for_relief' => 'Generate a prayer for relief appropriate for a {case_type} case with the following damages: {damages}',
                'signature' => 'Create a signature block for {attorney_name}, attorney for the plaintiff.'
            ]),
            'metadata' => json_encode([
                'jurisdiction' => 'General',
                'court_type' => 'Superior Court',
                'document_category' => 'Pleading',
                'formatting' => [
                    'font' => 'Times New Roman',
                    'font_size' => 12,
                    'line_spacing' => 2.0,
                    'margins' => [
                        'top' => 1.0,
                        'bottom' => 1.0,
                        'left' => 1.0,
                        'right' => 1.0
                    ]
                ]
            ]),
            'is_active' => true,
            'created_by' => null
        ]);

        // Create a motion template
        DocumentTemplate::create([
            'name' => 'Standard Motion',
            'document_type' => 'motion',
            'description' => 'A standard motion template with all required sections.',
            'structure' => json_encode([
                ['id' => 'caption', 'name' => 'Caption', 'required' => true, 'order' => 1],
                ['id' => 'title', 'name' => 'Title', 'required' => true, 'order' => 2],
                ['id' => 'introduction', 'name' => 'Introduction', 'required' => true, 'order' => 3],
                ['id' => 'background', 'name' => 'Background', 'required' => true, 'order' => 4],
                ['id' => 'legal_standard', 'name' => 'Legal Standard', 'required' => true, 'order' => 5],
                ['id' => 'argument', 'name' => 'Argument', 'required' => true, 'order' => 6],
                ['id' => 'conclusion', 'name' => 'Conclusion', 'required' => true, 'order' => 7],
                ['id' => 'signature', 'name' => 'Signature Block', 'required' => true, 'order' => 8]
            ]),
            'default_content' => json_encode([
                'caption' => 'IN THE SUPERIOR COURT OF THE STATE OF [STATE]\nIN AND FOR THE COUNTY OF [COUNTY]\n\n[PLAINTIFF NAME],\nPlaintiff,\n\nvs.\n\n[DEFENDANT NAME],\nDefendant.',
                'title' => '[PARTY]\'S MOTION FOR [RELIEF REQUESTED]',
                'introduction' => 'COMES NOW the [PARTY], by and through undersigned counsel, and hereby moves this Court for an Order [RELIEF REQUESTED]. This Motion is based on the following Memorandum of Points and Authorities, the pleadings and papers on file herein, and such other evidence and argument as may be presented at the hearing on this matter.',
                'background' => 'BACKGROUND\n\n1. [BACKGROUND FACTS]',
                'legal_standard' => 'LEGAL STANDARD\n\n[LEGAL STANDARD FOR REQUESTED RELIEF]',
                'argument' => 'ARGUMENT\n\n[ARGUMENT SUPPORTING MOTION]',
                'conclusion' => 'CONCLUSION\n\nFor the foregoing reasons, [PARTY] respectfully requests that the Court grant this Motion and [RELIEF REQUESTED].',
                'signature' => 'Dated: [DATE]\n\nRespectfully submitted,\n\n_________________________\n[ATTORNEY NAME]\nAttorney for [PARTY]'
            ]),
            'ai_prompts' => json_encode([
                'caption' => 'Generate a caption for a motion in {jurisdiction} court with plaintiff {plaintiff_name} and defendant {defendant_name}.',
                'title' => 'Create a title for a motion for {relief_requested}.',
                'introduction' => 'Write an introduction paragraph for a motion for {relief_requested}.',
                'background' => 'Write the background section for a motion for {relief_requested} based on these facts: {case_facts}',
                'legal_standard' => 'Explain the legal standard for a motion for {relief_requested} in {jurisdiction}.',
                'argument' => 'Write arguments supporting a motion for {relief_requested} based on these facts: {case_facts}',
                'conclusion' => 'Write a conclusion for a motion for {relief_requested}.',
                'signature' => 'Create a signature block for {attorney_name}, attorney for the {party}.'
            ]),
            'metadata' => json_encode([
                'jurisdiction' => 'General',
                'court_type' => 'Superior Court',
                'document_category' => 'Motion',
                'formatting' => [
                    'font' => 'Times New Roman',
                    'font_size' => 12,
                    'line_spacing' => 2.0,
                    'margins' => [
                        'top' => 1.0,
                        'bottom' => 1.0,
                        'left' => 1.0,
                        'right' => 1.0
                    ]
                ]
            ]),
            'is_active' => true,
            'created_by' => null
        ]);
    }
}
