<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 sm:gap-2">
            <h2 class="text-xl font-semibold leading-tight text-base-content/80 flex items-center gap-2">
                <span class="text-2xl">⚖️</span>
                {{ __('strategy.dashboard.title') }}
            </h2>
            <div class="flex flex-wrap gap-2">
                <a href="{{ route('case-files.show', $caseFile) }}"
                   class="btn btn-ghost btn-sm w-full sm:w-auto">
                    ← {{ __('cases.actions.back_to_case') }}
                </a>
                <button onclick="document.getElementById('create-strategy-modal').showModal()"
                   class="btn btn-primary btn-sm w-full sm:w-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                    </svg>
                    {{ __('strategy.dashboard.create_new') }}
                </button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="mx-auto max-w-8xl sm:px-6 lg:px-8">
            <div class="bg-base-100 shadow-xl sm:rounded-lg overflow-hidden">
                <div class="p-6">

                    <h3 class="text-xl font-medium mb-4">{{ __('strategy.dashboard.strategies_for', ['case' => $caseFile->title]) }}</h3>

                    @if($strategies->isEmpty())
                        <div class="alert alert-info">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" class="stroke-current shrink-0 w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>{{ __('strategy.dashboard.no_strategies') }}</span>
                        </div>
                        <div class="mt-4 text-center">
                            <button onclick="document.getElementById('create-strategy-modal').showModal()" class="btn btn-primary">
                                {{ __('strategy.dashboard.create_first') }}
                            </button>
                        </div>
                    @else
                        <div>
                            <!-- Desktop Table (hidden on mobile) -->
                            <div class="hidden md:block overflow-x-auto">
                                <table class="table w-full">
                                    <thead>
                                        <tr>
                                            <th>{{ __('strategy.dashboard.version') }}</th>
                                            <th>{{ __('strategy.dashboard.strategy_title') }}</th>
                                            <th>{{ __('strategy.dashboard.created_at') }}</th>
                                            <th>{{ __('strategy.dashboard.updated_at') }}</th>
                                            <th>{{ __('strategy.dashboard.created_by') }}</th>
                                            <th>{{ __('strategy.dashboard.summary') }}</th>
                                            <th>{{ __('strategy.dashboard.actions') }}</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($strategies as $strategy)
                                            <tr>
                                                <td>{{ $strategy->version }}</td>
                                                <td>{{ $strategy->title }}</td>
                                                <td>{{ $strategy->created_at->format('M d, Y H:i') }}</td>
                                                <td>{{ $strategy->updated_at->format('M d, Y H:i') }}</td>
                                                <td>{{ $strategy->creator->name }}</td>
                                                <td class="max-w-md">
                                                    <div class="truncate">
                                                        {{ Str::limit($strategy->executive_summary, 100) }}
                                                    </div>
                                                </td>
                                                <td>
                                                    <div class="flex gap-2">
                                                        <button class="btn btn-sm btn-ghost" onclick="document.getElementById('strategy-modal-{{ $strategy->id }}').showModal()">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                            </svg>
                                                        </button>
                                                        <button class="btn btn-sm btn-ghost" onclick="openChatForStrategy({{ $caseFile->id }}, {{ $strategy->id }})">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                            </svg>
                                                        </button>
                                                        <button class="btn btn-sm btn-ghost text-error" onclick="document.getElementById('delete-strategy-modal-{{ $strategy->id }}').showModal()">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                            </svg>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>

                            <!-- Mobile Card Layout (hidden on desktop) -->
                            <div class="md:hidden space-y-4">
                                @foreach($strategies as $strategy)
                                    <div class="card bg-base-100 shadow-sm border border-base-200 hover:shadow-md transition-shadow duration-200">
                                        <div class="card-body p-4 gap-3">
                                            <!-- Header with Version and Actions -->
                                            <div class="flex justify-between items-start">
                                                <div>
                                                    <h3 class="font-medium text-lg">{{ __('strategy.dashboard.version') }} {{ $strategy->version }}</h3>
                                                    <p class="text-sm text-gray-500 mt-1">{{$strategy->title}}</p>
                                                    <div class="text-sm text-base-content/60 mt-1">
                                                        {{ $strategy->created_at->format('M d, Y H:i') }}
                                                    </div>
                                                </div>
                                                <div class="flex gap-1">
                                                    <button class="btn btn-ghost btn-xs" onclick="document.getElementById('strategy-modal-{{ $strategy->id }}').showModal()">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                        </svg>
                                                    </button>
                                                    <button class="btn btn-ghost btn-xs" onclick="openChatForStrategy({{ $caseFile->id }}, {{ $strategy->id }})">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                        </svg>
                                                    </button>
                                                    <button class="btn btn-ghost btn-xs text-error" onclick="document.getElementById('delete-strategy-modal-{{ $strategy->id }}').showModal()">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                                        </svg>
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Content Sections -->
                                            <div class="grid grid-cols-1 gap-3 text-sm">
                                                <!-- Created By Section -->
                                                <div class="space-y-1">
                                                    <div class="font-medium text-base-content/70">{{ __('strategy.dashboard.created_by') }}:</div>
                                                    <div>{{ $strategy->creator->name }}</div>
                                                </div>

                                                <!-- Summary Section -->
                                                <div class="space-y-1">
                                                    <div class="font-medium text-base-content/70">{{ __('strategy.dashboard.summary') }}:</div>
                                                    <div class="text-sm">{{ Str::limit($strategy->executive_summary, 150) }}</div>
                                                </div>
                                            </div>

                                            <!-- View Full Button -->
                                            <div class="mt-2">
                                                <button
                                                    onclick="document.getElementById('strategy-modal-{{ $strategy->id }}').showModal()"
                                                    class="btn btn-sm btn-outline w-full">
                                                    {{ __('View Details') }}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>

                            <!-- Pagination Links -->
                            <div class="mt-6">
                                {{ $strategies->links() }}
                            </div>
                        </div>

                        <!-- Strategy Modals -->
                        @foreach($strategies as $strategy)
                            <!-- Strategy Modal -->
                            <dialog id="strategy-modal-{{ $strategy->id }}" class="modal modal-bottom sm:modal-middle">
                                <div class="modal-box max-h-[90vh] overflow-y-auto max-w-full sm:max-w-xl md:max-w-2xl w-full p-4 sm:p-6">
                                    <h3 class="font-bold text-lg sm:text-xl">{{ __('strategy.dashboard.strategy_details') }}</h3>
                                    <div class="py-4 space-y-6 max-h-[70vh] overflow-y-auto">
                                        <div class="pb-2">
                                            <h4 class="font-semibold text-base sm:text-lg mb-2 text-primary">{{ __('strategy.fields.executive_summary') }}</h4>
                                            <div class="mt-2 p-3 sm:p-4 bg-base-200 rounded-lg markdown-body text-sm sm:text-base overflow-x-auto">
                                                <div id="executive-summary-{{ $strategy->id }}" class="break-words"></div>
                                            </div>
                                        </div>

                                        @if(!empty($strategy->strategy_data['content']))
                                            <div class="pb-2">
                                                <h4 class="font-semibold text-base sm:text-lg mb-2 text-primary">{{ __('strategy.fields.strategy_recommendations') }}</h4>
                                                <div class="mt-2 p-3 sm:p-4 bg-base-200 rounded-lg markdown-body text-sm sm:text-base overflow-x-auto">
                                                    <div id="strategy-recommendations-{{ $strategy->id }}" class="break-words"></div>
                                                </div>
                                            </div>
                                        @endif

                                        @if(!empty($strategy->legal_analysis['content']))
                                            <div class="pb-2">
                                                <h4 class="font-semibold text-base sm:text-lg mb-2 text-primary">{{ __('strategy.fields.legal_analysis') }}</h4>
                                                <div class="mt-2 p-3 sm:p-4 bg-base-200 rounded-lg markdown-body text-sm sm:text-base overflow-x-auto">
                                                    <div id="legal-analysis-{{ $strategy->id }}" class="break-words"></div>
                                                </div>
                                            </div>
                                        @endif

                                        @if(!empty($strategy->action_items) && is_array($strategy->action_items) && count($strategy->action_items) > 0)
                                            <div class="pb-2">
                                                <h4 class="font-semibold text-base sm:text-lg mb-2 text-primary">{{ __('strategy.fields.action_items') }}</h4>
                                                <div class="mt-2 p-3 sm:p-4 bg-base-200 rounded-lg text-sm sm:text-base overflow-x-auto">
                                                    <ul class="list-disc pl-5 space-y-2 break-words">
                                                        @foreach($strategy->action_items as $item)
                                                            <li class="pr-2">{{ $item }}</li>
                                                        @endforeach
                                                    </ul>
                                                </div>
                                            </div>
                                        @endif

                                        @if(!empty($strategy->notes))
                                            <div class="pb-2">
                                                <h4 class="font-semibold text-base sm:text-lg mb-2 text-primary">{{ __('strategy.fields.notes') }}</h4>
                                                <div class="mt-2 p-3 sm:p-4 bg-base-200 rounded-lg markdown-body text-sm sm:text-base overflow-x-auto">
                                                    <div id="notes-{{ $strategy->id }}" class="break-words"></div>
                                                </div>
                                            </div>
                                        @endif

                                        <style>
                                            /* Custom styles for markdown content in modals */
                                            .markdown-body pre {
                                                white-space: pre-wrap;
                                                word-wrap: break-word;
                                                max-width: 100%;
                                                overflow-x: auto;
                                            }
                                            .markdown-body img {
                                                max-width: 100%;
                                                height: auto;
                                            }
                                            .markdown-body table {
                                                display: block;
                                                width: 100%;
                                                overflow-x: auto;
                                            }
                                            .markdown-body code {
                                                white-space: pre-wrap;
                                                word-wrap: break-word;
                                            }
                                        </style>

                                        <script>
                                            document.addEventListener('DOMContentLoaded', function() {
                                                // Executive Summary
                                                const executiveSummary = {!! json_encode($strategy->executive_summary) !!};
                                                if (executiveSummary) {
                                                    document.getElementById('executive-summary-{{ $strategy->id }}').innerHTML = marked.parse(executiveSummary);
                                                }

                                                // Strategy Recommendations
                                                @if(!empty($strategy->strategy_data['content']))
                                                const strategyRecommendations = {!! json_encode($strategy->strategy_data['content']) !!};
                                                if (strategyRecommendations) {
                                                    document.getElementById('strategy-recommendations-{{ $strategy->id }}').innerHTML = marked.parse(strategyRecommendations);
                                                }
                                                @endif

                                                // Legal Analysis
                                                @if(!empty($strategy->legal_analysis['content']))
                                                const legalAnalysis = {!! json_encode($strategy->legal_analysis['content']) !!};
                                                if (legalAnalysis) {
                                                    document.getElementById('legal-analysis-{{ $strategy->id }}').innerHTML = marked.parse(legalAnalysis);
                                                }
                                                @endif

                                                // Notes
                                                @if(!empty($strategy->notes))
                                                const notes = {!! json_encode($strategy->notes) !!};
                                                if (notes) {
                                                    document.getElementById('notes-{{ $strategy->id }}').innerHTML = marked.parse(notes);
                                                }
                                                @endif
                                            });
                                        </script>
                                    </div>
                                    <div class="modal-action mt-6 sm:mt-8">
                                        <form method="dialog" class="w-full">
                                            <button class="btn btn-primary w-full sm:w-auto">{{ __('common.close') }}</button>
                                        </form>
                                    </div>
                                </div>
                            </dialog>

                            <!-- Delete Strategy Modal -->
                            <dialog id="delete-strategy-modal-{{ $strategy->id }}" class="modal modal-bottom sm:modal-middle">
                                <div class="modal-box max-w-full sm:max-w-lg w-full p-4 sm:p-6">
                                    <h3 class="font-bold text-lg sm:text-xl text-error">{{ __('Delete Strategy') }}</h3>
                                    <p class="py-4 text-sm sm:text-base">{{ __('Are you sure you want to delete this strategy? This action cannot be undone.') }}</p>
                                    <div class="modal-action flex flex-col sm:flex-row sm:justify-between gap-3 sm:gap-4">
                                        <form method="dialog" class="w-full">
                                            <button class="btn w-full">{{ __('Cancel') }}</button>
                                        </form>
                                        <form action="{{ route('case-files.strategies.destroy', ['caseFile' => $caseFile, 'strategy' => $strategy]) }}" method="POST" class="w-full">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit" class="btn btn-error w-full">{{ __('Delete') }}</button>
                                        </form>
                                    </div>
                                </div>
                            </dialog>
                        @endforeach
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Create Strategy Modal -->
    <dialog id="create-strategy-modal" class="modal modal-bottom sm:modal-middle">
        <div class="modal-box max-w-md">
            <h3 class="font-bold text-lg">{{ __('Create New Strategy') }}</h3>
            <form id="create-strategy-form" method="POST" action="{{ route('case-files.strategies.store', $caseFile) }}" class="py-4">
                @csrf
                <div class="form-control w-full">
                    <label class="label">
                        <span class="label-text">{{ __('Strategy Title') }}</span>
                    </label>
                    <input type="text" name="title" class="input input-bordered w-full" required
                           placeholder="{{ __('Enter a title for your strategy') }}" />
                </div>
                <div class="modal-action flex justify-between">
                    <button type="button" class="btn" onclick="document.getElementById('create-strategy-modal').close()">
                        {{ __('Cancel') }}
                    </button>
                    <button type="submit" class="btn btn-primary">
                        {{ __('Create & Open') }}
                    </button>
                </div>
            </form>
        </div>
    </dialog>

    <!-- Include the Chat Interface component -->
    <livewire:chat.chat-interface />

    <script>
        function openChatForStrategy(caseFileId, strategyId) {
            // Dispatch the Livewire events to open the chat interface for this strategy
            Livewire.dispatchTo('chat.chat-interface', 'set-strategy-mode',
                { strategyMode: true }
            );
            Livewire.dispatchTo('chat.chat-interface', 'set-strategy-id', {
                strategyId: strategyId
            });
            Livewire.dispatchTo('chat.chat-interface', 'openChatForCase', {
                caseFileId: caseFileId
            });
        }

        // Handle the create strategy form submission
        document.getElementById('create-strategy-form').addEventListener('submit', function(e) {
            e.preventDefault();
            const form = this;
            const formData = new FormData(form);

            fetch(form.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close the modal
                    document.getElementById('create-strategy-modal').close();

                    // Open the chat interface with the new strategy
                    openChatForStrategy({{ $caseFile->id }}, data.strategy_id);
                } else {
                    alert(data.message || 'An error occurred while creating the strategy.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while creating the strategy.');
            });
        });
    </script>
</x-app-layout>
