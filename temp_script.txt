                                // Listen for AI response events
                                channel.bind('ai.response.processed', function(data) {
                                    console.log('Received AI response from <PERSON>ush<PERSON>:', data);
                                    // The Livewire component will handle this event
                                    // Manually dispatch the event to Livewire
                                    @this.call('handleAiResponseProcessed', data);
                                });
                                
                                // Listen for section content generation events
                                channel.bind('section.content.generated', function(data) {
                                    console.log('Received section content from <PERSON>ush<PERSON>:', data);
                                    // The Livewire component will handle this event
                                    // Manually dispatch the event to Livewire
                                    @this.call('handleSectionContentGenerated', data);
                                });
