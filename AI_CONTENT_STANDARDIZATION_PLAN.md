# AI Content Standardization Plan for Legal Assistance App

## Overview

This document outlines a comprehensive plan for standardizing AI content generation in the legal assistance app. The goal is to ensure that AI-generated content is properly formatted for the Quill editor and can be correctly processed by the document generation service to create court-acceptable legal documents.

## Current System Components

1. **Quill Editor** - Rich text editor for document editing
2. **AI Content Generation** - Generates specific sections for legal documents via DocumentAiService
3. **Document Generation Service** - Formats content into downloadable Word documents with proper legal formatting

## Challenges to Address

1. Standardizing AI response format for consistent integration with Quill editor
2. Ensuring proper content structure without AI adding paragraph numbering
3. Handling complex formatting like nested lists in a court-acceptable manner
4. Maintaining consistency across different document types (pleadings, motions, etc.)
5. Ensuring proper conversion from Quill format to Word document format that meets court standards
6. Properly formatting section-specific content according to legal document conventions

## Solution Plan

### 1. Standardize AI Response Format

#### 1.1 Update DocumentPromptService

Modify prompts to explicitly instruct the AI on content structure without adding numbering:

```php
protected function getBasePrompt($sectionType, $action)
{
    $prompts = [
        'factual_allegations' => [
            'generate' => "Generate a facts section for a {{document_type}}. Present the relevant facts in a clear, chronological order. Focus on facts that are material to the legal issues in the case.

            FORMAT REQUIREMENTS:
            1. DO NOT add paragraph numbers or bullet points - these will be added automatically
            2. Each paragraph should contain one distinct factual allegation
            3. Indicate sub-points by starting with phrases like 'Specifically,' or 'In particular,'
            4. For citations, use proper legal citation format
            5. Return content in plain text with clear paragraph breaks

            Do not include a top level {{document_type}} title, just the substantive content",
            // ...
        ],
        // Other section types...
    ];

    // Rest of the method...
}
```

#### 1.2 Update DocumentAiService

Add specific formatting instructions to the AI prompts:

```php
protected function formatPromptWithContext($prompt, $context = [])
{
    // Start with the main prompt
    $formattedPrompt = $prompt;

    // Add specific instructions about formatting
    $formattedPrompt .= "\n\nIMPORTANT FORMATTING INSTRUCTIONS:";
    $formattedPrompt .= "\n1. DO NOT add paragraph numbers or bullet points - these will be added automatically";
    $formattedPrompt .= "\n2. Each paragraph should contain one distinct point";
    $formattedPrompt .= "\n3. Indicate sub-points by starting with phrases like 'Specifically,' or 'In particular,'";
    $formattedPrompt .= "\n4. Use clear paragraph breaks between points";

    // Add section-specific instructions
    if (isset($context['active_section'])) {
        $sectionType = $context['active_section'];
        $sectionInstructions = $this->getSectionSpecificInstructions($sectionType);
        if ($sectionInstructions) {
            $formattedPrompt .= "\n\n" . $sectionInstructions;
        }
    }

    // Rest of the method...

    return $formattedPrompt;
}
```

Add section-specific formatting instructions:

```php
protected function getSectionSpecificInstructions($sectionType)
{
    switch ($sectionType) {
        case 'factual_allegations':
            return "For factual allegations, present each fact as a separate paragraph in chronological order. Start sub-points with 'Specifically,' or similar phrases.";

        case 'causes_of_action':
            return "For causes of action, clearly label each cause with a heading (e.g., 'FIRST CAUSE OF ACTION: BREACH OF CONTRACT'). After each heading, include an introductory paragraph, followed by separate paragraphs for each element or allegation.";

        case 'prayer_for_relief':
            return "For prayer for relief, start with an introductory paragraph (e.g., 'WHEREFORE, Plaintiff prays for judgment as follows:'). Then list each prayer item as a separate paragraph.";

        default:
            return null;
    }
}
```

### 2. Enhance Content Structure Detection

#### 2.1 Update HtmlToDocumentConverter

Enhance the converter to detect paragraph hierarchy without relying on explicit numbering:

```php
protected function convertQuillDelta($content, $sectionType = null)
{
    try {
        $delta = json_decode($content, true);

        if (!isset($delta['ops']) || !is_array($delta['ops'])) {
            return null;
        }

        $result = [
            'paragraphs' => [],
            'lists' => []
        ];

        $currentParagraph = [
            'text' => '',
            'format' => [],
            'level' => 0 // Default to main level
        ];

        foreach ($delta['ops'] as $op) {
            if (isset($op['insert'])) {
                $text = $op['insert'];
                $attributes = $op['attributes'] ?? [];

                // Check for paragraph breaks
                if (strpos($text, "\n") !== false) {
                    // Handle paragraph completion

                    // Determine paragraph level based on content or attributes
                    if (isset($attributes['indent'])) {
                        $currentParagraph['level'] = $attributes['indent'];
                    } else {
                        $currentParagraph['level'] = $this->determineParagraphLevel($currentParagraph['text']);
                    }

                    // Add completed paragraph to result
                    if (!empty(trim($currentParagraph['text']))) {
                        $result['paragraphs'][] = $currentParagraph;
                    }

                    // Start a new paragraph
                    $currentParagraph = [
                        'text' => '',
                        'format' => [],
                        'level' => 0
                    ];
                } else {
                    // Add text to current paragraph
                    $currentParagraph['text'] .= $text;

                    // Apply formatting attributes
                    if (!empty($attributes)) {
                        foreach ($attributes as $key => $value) {
                            $currentParagraph['format'][$key] = $value;
                        }
                    }
                }
            }
        }

        // Add the last paragraph if not empty
        if (!empty(trim($currentParagraph['text']))) {
            $currentParagraph['level'] = $this->determineParagraphLevel($currentParagraph['text']);
            $result['paragraphs'][] = $currentParagraph;
        }

        // Process section-specific data
        if ($sectionType) {
            $this->processSectionSpecificData($result, $sectionType);
        }

        return $result;
    } catch (\Exception $e) {
        Log::error('Error converting Quill Delta', [
            'error' => $e->getMessage(),
            'content' => $content
        ]);
        return null;
    }
}

/**
 * Determine paragraph level based on content analysis
 */
protected function determineParagraphLevel($text)
{
    // Check for indicators of sub-points
    $subPointIndicators = [
        'specifically',
        'in particular',
        'for example',
        'such as',
        'including',
        'namely'
    ];

    foreach ($subPointIndicators as $indicator) {
        if (stripos($text, $indicator) === 0) {
            return 1; // Sub-point
        }
    }

    return 0; // Main point
}
```

### 3. Enhance Document Generation Service

#### 3.1 Update DocumentGenerationService for Court-Acceptable Formatting

Modify the service to handle paragraph numbering and formatting based on legal document standards:

```php
protected function processTextSection($section, $content, $heading, $convertedData = null)
{
    // Add section heading in proper legal format
    $section->addText(
        strtoupper($heading), // Legal headings are often in all caps
        ['bold' => true, 'size' => 14],
        ['alignment' => 'center', 'spaceBefore' => 240, 'spaceAfter' => 120]
    );

    // Process paragraphs with automatic numbering according to legal standards
    if ($convertedData && isset($convertedData['paragraphs'])) {
        $paragraphs = $convertedData['paragraphs'];

        // Create court-standard numbering style for this section
        $phpWord = $section->getPhpWord();
        $numberingStyleName = 'numbering' . Str::studly($heading);
        $phpWord->addNumberingStyle(
            $numberingStyleName,
            [
                'type' => 'multilevel',
                'levels' => [
                    [
                        'format' => 'decimal',
                        'text' => '%1.',
                        'left' => 360,
                        'hanging' => 360,
                        'tabPos' => 360
                    ],
                    [
                        'format' => 'lowerLetter',
                        'text' => '%2)',
                        'left' => 720,
                        'hanging' => 360,
                        'tabPos' => 720
                    ]
                ]
            ]
        );

        // Process each paragraph with appropriate legal numbering
        $currentLevel = 0;
        foreach ($paragraphs as $index => $paragraph) {
            $text = $paragraph['text'];
            $format = $paragraph['format'] ?? [];
            $level = $paragraph['level'] ?? $this->determineParagraphLevel($text);

            // Add the paragraph with court-standard numbering and formatting
            $section->addListItem(
                $text,
                $level,
                ['size' => 14], // Standard legal document font size
                $numberingStyleName,
                ['alignment' => 'both', 'lineHeight' => 2.0] // Double-spaced and justified as per court standards
            );
        }
    } else {
        // Fallback for plain text content with court-standard formatting
        // ...existing code...
    }
}

/**
 * Determine the paragraph level based on legal document structure analysis
 */
protected function determineParagraphLevel($text)
{
    // Check for indicators of sub-points in legal documents
    $subPointIndicators = [
        'specifically',
        'in particular',
        'for example',
        'such as',
        'including',
        'namely',
        'to wit',       // Common in legal documents
        'inter alia',   // Common in legal documents
        'including but not limited to'
    ];

    foreach ($subPointIndicators as $indicator) {
        if (stripos($text, $indicator) === 0) {
            return 1; // Sub-point (level 1)
        }
    }

    return 0; // Main point (level 0)
}
```

### 4. Section-Specific Formatting for Court Standards

Create specialized processing for different legal document sections according to court standards:

#### 4.1 Causes of Action

```php
protected function processCausesOfActionSection($section, $content, $draft, $convertedData = null)
{
    if ($convertedData && isset($convertedData['causes'])) {
        // Use the converted data
        $causes = $convertedData['causes'];

        foreach ($causes as $causeIndex => $cause) {
            // Add cause of action heading in proper legal format (all caps)
            $section->addText(
                strtoupper($cause['title']),
                ['bold' => true, 'size' => 14],
                ['alignment' => 'center', 'spaceBefore' => 240, 'spaceAfter' => 120]
            );

            // Create court-standard numbering style for this cause
            $phpWord = $section->getPhpWord();
            $numberingStyleName = 'numbering' . Str::studly('cause' . $causeIndex);
            $phpWord->addNumberingStyle(
                $numberingStyleName,
                [
                    'type' => 'multilevel',
                    'levels' => [
                        [
                            'format' => 'decimal',
                            'text' => '%1.',
                            'left' => 360,
                            'hanging' => 360,
                            'tabPos' => 360
                        ],
                        [
                            'format' => 'lowerLetter',
                            'text' => '%2)',
                            'left' => 720,
                            'hanging' => 360,
                            'tabPos' => 720
                        ]
                    ]
                ]
            );

            // Process paragraphs for this cause with court-standard formatting
            foreach ($cause['paragraphs'] as $paragraph) {
                $text = $paragraph['text'];
                $format = $paragraph['format'] ?? [];
                $level = $paragraph['level'] ?? $this->determineParagraphLevel($text);

                // Add the paragraph with court-standard numbering and formatting
                $section->addListItem(
                    $text,
                    $level,
                    ['size' => 14], // Standard legal document font size
                    $numberingStyleName,
                    ['alignment' => 'both', 'lineHeight' => 2.0] // Double-spaced and justified as per court standards
                );
            }
        }
    } else {
        // Fallback to generic text processing with court standards
        $this->processTextSection($section, $content, 'CAUSES OF ACTION', $convertedData);
    }
}
```

#### 4.2 Prayer for Relief

```php
protected function processPrayerForReliefSection($section, $content, $draft, $convertedData = null)
{
    // Add section heading in proper legal format (all caps)
    $section->addText(
        'PRAYER FOR RELIEF',
        ['bold' => true, 'size' => 14],
        ['alignment' => 'center', 'spaceBefore' => 240, 'spaceAfter' => 120]
    );

    if ($convertedData && isset($convertedData['prayerPoints'])) {
        // Add wherefore text if available - properly formatted for legal documents
        if (isset($convertedData['whereforeText']) && !empty($convertedData['whereforeText'])) {
            $section->addText(
                strtoupper($convertedData['whereforeText']), // WHEREFORE clause typically in all caps
                ['bold' => true, 'size' => 14],
                ['alignment' => 'left', 'spaceBefore' => 120, 'spaceAfter' => 120]
            );
        } else {
            // Default wherefore text in proper legal format
            $section->addText(
                'WHEREFORE, Plaintiff prays for judgment as follows:',
                ['bold' => true, 'size' => 14],
                ['alignment' => 'left', 'spaceBefore' => 120, 'spaceAfter' => 120]
            );
        }

        // Create court-standard numbering style for prayer points
        $phpWord = $section->getPhpWord();
        $numberingStyleName = 'numberingPrayerPoints';
        $phpWord->addNumberingStyle(
            $numberingStyleName,
            [
                'type' => 'multilevel',
                'levels' => [
                    [
                        'format' => 'decimal',
                        'text' => '%1.',
                        'left' => 360,
                        'hanging' => 360,
                        'tabPos' => 360
                    ],
                    [
                        'format' => 'lowerLetter',
                        'text' => '%2)',
                        'left' => 720,
                        'hanging' => 360,
                        'tabPos' => 720
                    ]
                ]
            ]
        );

        // Process prayer points with court-standard formatting
        foreach ($convertedData['prayerPoints'] as $point) {
            $text = $point['text'];
            $format = $point['format'] ?? [];
            $level = $point['level'] ?? $this->determineParagraphLevel($text);

            // Add the prayer point with court-standard numbering and formatting
            $section->addListItem(
                $text,
                $level,
                ['size' => 14], // Standard legal document font size
                $numberingStyleName,
                ['alignment' => 'both', 'lineHeight' => 2.0] // Double-spaced and justified as per court standards
            );
        }
    } else {
        // Fallback to generic text processing with court standards
        $this->processTextSection($section, $content, 'PRAYER FOR RELIEF', $convertedData);
    }
}
```

#### 4.3 Factual Allegations

```php
protected function processFactualAllegationsSection($section, $content, $draft, $convertedData = null)
{
    // Add section heading in proper legal format (all caps)
    $section->addText(
        'FACTUAL ALLEGATIONS',
        ['bold' => true, 'size' => 14],
        ['alignment' => 'center', 'spaceBefore' => 240, 'spaceAfter' => 120]
    );

    // Create court-standard numbering style for factual allegations
    $phpWord = $section->getPhpWord();
    $numberingStyleName = 'numberingFactualAllegations';
    $phpWord->addNumberingStyle(
        $numberingStyleName,
        [
            'type' => 'multilevel',
            'levels' => [
                [
                    'format' => 'decimal',
                    'text' => '%1.',
                    'left' => 360,
                    'hanging' => 360,
                    'tabPos' => 360
                ],
                [
                    'format' => 'lowerLetter',
                    'text' => '%2)',
                    'left' => 720,
                    'hanging' => 360,
                    'tabPos' => 720
                ]
            ]
        ]
    );

    // Process paragraphs with court-standard formatting
    if ($convertedData && isset($convertedData['paragraphs'])) {
        foreach ($convertedData['paragraphs'] as $paragraph) {
            $text = $paragraph['text'];
            $format = $paragraph['format'] ?? [];
            $level = $paragraph['level'] ?? $this->determineParagraphLevel($text);

            // Add the paragraph with court-standard numbering and formatting
            $section->addListItem(
                $text,
                $level,
                ['size' => 14], // Standard legal document font size
                $numberingStyleName,
                ['alignment' => 'both', 'lineHeight' => 2.0] // Double-spaced and justified as per court standards
            );
        }
    } else {
        // Fallback to generic text processing with court standards
        $this->processGenericSection($section, $content, 'FACTUAL ALLEGATIONS', $convertedData);
    }
}
```

## Implementation Steps

1. **Update DocumentPromptService**
   - Modify prompts to include clear formatting instructions
   - Add section-specific formatting guidelines

2. **Update DocumentAiService**
   - Add formatting instructions to AI prompts
   - Ensure consistent response format

3. **Enhance HtmlToDocumentConverter**
   - Add paragraph level detection
   - Improve structure detection for different section types

4. **Update DocumentGenerationService**
   - Implement automatic numbering based on content structure
   - Add section-specific formatting handlers

5. **Enhance Document Download Process**
   - Standardize the document generation and download flow
   - Ensure proper handling of all formatting in the final document

6. **Testing**
   - Test with various document types and sections
   - Verify proper formatting in both Quill editor and generated Word documents

## Example AI Output Format

For a factual allegations section, the AI should return content like:

```
Plaintiff John Doe entered into a contract with Defendant ABC Corp on January 15, 2023, for consulting services.

The contract required Defendant to pay Plaintiff $10,000 per month for six months of services.

Plaintiff performed all required services under the contract from January 15, 2023, through July 15, 2023.

Specifically, Plaintiff delivered weekly reports as required by Section 3.2 of the contract.

Specifically, Plaintiff attended all required meetings with Defendant's management team.

Defendant failed to pay Plaintiff for services rendered in June and July 2023, totaling $20,000.

Plaintiff sent a demand letter to Defendant on August 1, 2023, requesting payment of all outstanding amounts.

Defendant has not responded to Plaintiff's demand letter and continues to withhold payment.
```

The DocumentGenerationService would then format this with proper numbering in the Word document:

1. Plaintiff John Doe entered into a contract with Defendant ABC Corp on January 15, 2023, for consulting services.

2. The contract required Defendant to pay Plaintiff $10,000 per month for six months of services.

3. Plaintiff performed all required services under the contract from January 15, 2023, through July 15, 2023.
   a) Specifically, Plaintiff delivered weekly reports as required by Section 3.2 of the contract.
   b) Specifically, Plaintiff attended all required meetings with Defendant's management team.

4. Defendant failed to pay Plaintiff for services rendered in June and July 2023, totaling $20,000.

5. Plaintiff sent a demand letter to Defendant on August 1, 2023, requesting payment of all outstanding amounts.

6. Defendant has not responded to Plaintiff's demand letter and continues to withhold payment.

### 5. Document Generation and Download Process

Enhance the document generation and download process to ensure proper handling of the standardized content:

```php
/**
 * Generate and prepare a document for download
 */
public function generateDocumentForDownload(Draft $draft, array $options = [])
{
    // Create a new PhpWord instance
    $phpWord = new PhpWord();

    // Apply document settings
    $this->applyDocumentSettings($phpWord, $draft, $options);

    // Create the main section
    $section = $phpWord->addSection([
        'marginTop' => $options['marginTop'] ?? $this->defaultSettings['marginTop'],
        'marginBottom' => $options['marginBottom'] ?? $this->defaultSettings['marginBottom'],
        'marginLeft' => $options['marginLeft'] ?? $this->defaultSettings['marginLeft'],
        'marginRight' => $options['marginRight'] ?? $this->defaultSettings['marginRight'],
    ]);

    // Get the sections structure from the draft
    $sections = $draft->sections_structure ?? [];

    // Process each section based on its type
    foreach ($sections as $draftSection) {
        if (empty($draftSection['content'])) {
            continue;
        }

        $this->processSection($section, $draftSection, $draft);
    }

    // Generate a filename
    $filename = $this->generateFilename($draft);

    // Make sure the documents directory exists
    $directory = storage_path('app/public/documents');
    if (!file_exists($directory)) {
        mkdir($directory, 0755, true);
    }

    // Save the document
    $path = storage_path("app/public/documents/{$filename}");
    $objWriter = IOFactory::createWriter($phpWord, 'Word2007');
    $objWriter->save($path);

    // Save to S3 if configured
    if (config('filesystems.default') === 's3') {
        $s3Path = Storage::disk('s3')->putFileAs('documents', $path, $filename);
        // Clean up local file after S3 upload
        if (file_exists($path)) {
            unlink($path);
        }
        return $s3Path;
    }

    // Return the relative path for storage
    return "documents/{$filename}";
}

/**
 * Generate a download response for a document
 */
public function downloadDocument($documentPath, $filename = null)
{
    if (config('filesystems.default') === 's3') {
        // For S3 storage
        $tempPath = tempnam(sys_get_temp_dir(), 'doc');
        Storage::disk('s3')->get($documentPath, $tempPath);
        $path = $tempPath;
    } else {
        // For local storage
        $path = storage_path("app/public/{$documentPath}");
    }

    if (!file_exists($path)) {
        throw new \Exception("Document not found: {$path}");
    }

    $filename = $filename ?: basename($documentPath);

    // Return download response
    return response()->download($path, $filename, [
        'Content-Type' => 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ])->deleteFileAfterSend(true);
}
```

## Court-Acceptable Formatting Standards

To ensure documents meet court standards, the following formatting requirements must be implemented:

### 1. General Document Formatting

- **Font**: Times New Roman, 14-point
- **Line Spacing**: Double-spaced (2.0)
- **Margins**: 1 inch on all sides
- **Alignment**: Justified for body text, centered for headings
- **Paragraph Numbering**: Sequential, properly formatted according to legal conventions

### 2. Section-Specific Formatting

#### Caption
- Court name centered and in all caps
- Case information properly aligned
- Parties clearly identified
- Document title in all caps and centered

#### Factual Allegations
- Numbered paragraphs (1, 2, 3...)
- Each paragraph containing one distinct factual allegation
- Sub-paragraphs properly indented and formatted with letters (a, b, c...)
- Double-spaced throughout

#### Causes of Action
- Clear headings for each cause (e.g., "FIRST CAUSE OF ACTION: BREACH OF CONTRACT")
- Numbered paragraphs within each cause
- Elements of each cause properly formatted and indented

#### Prayer for Relief
- "WHEREFORE" statement properly formatted and emphasized
- Prayer points numbered and properly indented
- Proper spacing between prayer points

#### Signature Block
- Properly aligned and formatted
- Adequate space for signature
- Attorney/party information properly formatted

## Conclusion

This standardization plan ensures:

1. AI generates well-structured content without adding numbering
2. Content structure is preserved through the Quill editor
3. The DocumentGenerationService handles all numbering and formatting according to court standards
4. A consistent approach is maintained across different document types and sections
5. Complex legal documents with nested lists are properly formatted according to legal conventions
6. The final documents meet court formatting requirements and professional legal standards

By implementing these changes, the AI-assisted drafting feature will provide a seamless experience from content generation to final court-ready document output.
