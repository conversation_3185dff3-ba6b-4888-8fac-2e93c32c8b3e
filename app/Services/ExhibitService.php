<?php

namespace App\Services;

use App\Models\Exhibit;
use App\Models\Draft;

class ExhibitService
{
    /**
     * Format an exhibit reference for insertion into a document
     */
    public function formatExhibitReference(Exhibit $exhibit, $format = 'standard')
    {
        switch ($format) {
            case 'standard':
                return "Exhibit {$exhibit->label}";
            
            case 'detailed':
                return "Exhibit {$exhibit->label}: {$exhibit->description}";
            
            case 'legal':
                return "Exhibit \"{$exhibit->label}\" attached hereto and incorporated herein by reference";
            
            case 'parenthetical':
                return "(See Exhibit {$exhibit->label})";
            
            default:
                return "Exhibit {$exhibit->label}";
        }
    }

    /**
     * Parse exhibit references from text
     */
    public function parseExhibitReferences($text)
    {
        $references = [];
        $pattern = '/Exhibit\s+([A-Z0-9]+)/i';
        
        if (preg_match_all($pattern, $text, $matches, PREG_OFFSET_CAPTURE)) {
            foreach ($matches[1] as $index => $match) {
                $references[] = [
                    'label' => $match[0],
                    'position' => $matches[0][$index][1],
                    'full_match' => $matches[0][$index][0]
                ];
            }
        }
        
        return $references;
    }

    /**
     * Update exhibit references in a draft
     */
    public function updateReferencesInDraft(Draft $draft)
    {
        // Get all sections from the draft
        $sections = $draft->sections_structure ?? [];
        
        if (empty($sections)) {
            return false;
        }
        
        // Get all exhibits for this case file
        $exhibits = Exhibit::where('case_file_id', $draft->case_file_id)
            ->get()
            ->keyBy('label')
            ->toArray();
        
        $updated = false;
        
        // Process each section
        foreach ($sections as &$section) {
            if (empty($section['content'])) {
                continue;
            }
            
            // Find exhibit references
            $references = $this->parseExhibitReferences($section['content']);
            
            if (empty($references)) {
                continue;
            }
            
            // Check each reference
            foreach ($references as $reference) {
                $label = $reference['label'];
                
                // If exhibit no longer exists or label changed
                if (!isset($exhibits[$label])) {
                    // Mark as potentially broken reference
                    $newContent = str_replace(
                        $reference['full_match'],
                        "[MISSING EXHIBIT: {$reference['full_match']}]",
                        $section['content']
                    );
                    
                    $section['content'] = $newContent;
                    $updated = true;
                }
            }
        }
        
        // Save updated sections if changes were made
        if ($updated) {
            $draft->update([
                'sections_structure' => $sections
            ]);
            
            return true;
        }
        
        return false;
    }

    /**
     * Get the next available exhibit label
     */
    public function getNextExhibitLabel($caseFileId)
    {
        $lastExhibit = Exhibit::where('case_file_id', $caseFileId)
            ->orderBy('sort_order', 'desc')
            ->first();
            
        if (!$lastExhibit) {
            return 'A';
        }
        
        // If label is a letter, get next letter
        if (preg_match('/^[A-Z]$/', $lastExhibit->label)) {
            return chr(ord($lastExhibit->label) + 1);
        }
        
        // If label is a number, increment
        if (is_numeric($lastExhibit->label)) {
            return (int)$lastExhibit->label + 1;
        }
        
        // Default to next letter
        return 'A';
    }
}
