<?php

namespace App\Services\OpenAI;

use App\Models\CaseFile;
use App\Models\OpenAiProject;
use OpenAI\Laravel\Facades\OpenAI;
use Illuminate\Support\Facades\Log;
use Exception;

/**
 * Service for managing OpenAI assistants and vector stores for legal cases.
 *
 * This service handles the creation and configuration of OpenAI resources
 * required for each legal case, including an AI assistant and vector store
 * for document search capabilities.
 */
class CaseAssistantService
{
    /**
     * The OpenAI model to use for the assistant.
     */
    private const ASSISTANT_MODEL = 'gpt-4.1-mini';

    /**
     * Base instructions defining the assistant's role and responsibilities.
     */
    private const ASSISTANT_INSTRUCTIONS = <<<EOT
You are a dedicated legal assistant for this case. Your role is to:
1. Provide accurate legal information and context based on the uploaded documents in your records
2. Help the user make sense of their case if they need understanding.
3. Answer questions about the case using the provided documentation
4. Maintain strict confidentiality and professional ethics
5. Always clarify when legal advice is needed from a licensed attorney

EOT;

    /**
     * Gets a default OpenAI client using the least-used active project.
     *
     * @return void
     * @throws Exception When no active OpenAI projects are available
     */
    private function configureDefaultOpenAi(): void
    {
        // First try to get an active project
        $project = OpenAiProject::where('is_active', true)
            ->orderBy('storage_used')
            ->first();

        if ($project) {
            config([
                'openai.api_key' => $project->api_key,
                'openai.organization_id' => $project->organization_id
            ]);
            return;
        }

        // Fallback to .env configuration
        $envApiKey = config('openai.api_key');
        $envOrganization = config('openai.organization');

        if (!$envApiKey) {
            throw new Exception('No available OpenAI projects or default API key');
        }

        // Configuration already set from .env, no need to modify
    }

    /**
     * Configures OpenAI for the given case.
     *
     * Selects the least-used active OpenAI project if the case doesn't have one assigned.
     * Includes storage limit checks to prevent exceeding the 100GB per-project limit.
     *
     * @param CaseFile $case The case requiring OpenAI configuration
     * @throws Exception When no active OpenAI projects are available
     */
    private function configureOpenAi(CaseFile $case): void
    {
        if (!$case->openai_project_id) {
            // Check if user has a storage allocation
            $user = $case->user;
            $allocation = $user->storageAllocation;

            if ($allocation) {
                // User has allocated storage, use their assigned project
                $case->update(['openai_project_id' => $allocation->openai_project_id]);

                Log::info('Assigned case to user\'s allocated OpenAI project', [
                    'case_id' => $case->id,
                    'user_id' => $user->id,
                    'project_id' => $allocation->openai_project_id,
                    'user_tier' => $allocation->subscription_tier
                ]);
            } else {
                // Fallback to old system for users without allocations
                $storageLimit = 95 * 1024 * 1024 * 1024; // 95GB in bytes

                $project = OpenAiProject::where('is_active', true)
                    ->where('storage_used', '<', $storageLimit)
                    ->orderBy('storage_used')
                    ->first();

                if ($project) {
                    $case->update(['openai_project_id' => $project->id]);

                    Log::warning('Assigned case to project without user allocation (legacy mode)', [
                        'case_id' => $case->id,
                        'user_id' => $user->id,
                        'project_id' => $project->id,
                        'project_name' => $project->name
                    ]);
                } else {
                    // Fallback to .env configuration
                    $envApiKey = config('services.openai.api_key');
                    $envOrganization = config('services.openai.organization_id');

                    if (!$envApiKey) {
                        throw new Exception('No available OpenAI projects or default API key');
                    }

                    Log::warning('No OpenAI projects available, falling back to .env configuration', [
                        'case_id' => $case->id,
                        'user_id' => $user->id
                    ]);

                    return;
                }
            }
        }

        $project = $case->openAiProject;

        config([
            'openai.api_key' => $project->api_key,
            'openai.organization_id' => $project->organization_id
        ]);
    }

    /**
     * Sets up all required OpenAI resources for a case.
     *
     * Creates an AI assistant and vector store, then links them together.
     *
     * @param CaseFile $case The case requiring resource setup
     * @return bool True if setup was successful, false otherwise
     * @throws Exception When setup fails
     */
    public function setupCaseResources(CaseFile $case): bool
    {
        try {
            $this->configureOpenAi($case);

            $assistant = $this->createAssistant($case);
            $vectorStore = $this->createVectorStore($case);

            if ($assistant && $vectorStore) {
                $this->attachVectorStoreToAssistant($case);
                return true;
            }

            return false;
        } catch (Exception $e) {
            Log::error('Failed to setup case resources', [
                'case_id' => $case->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Creates an OpenAI assistant for the case.
     *
     * @param CaseFile $case The case requiring an assistant
     * @return bool True if creation was successful
     * @throws Exception When assistant creation fails
     */
    private function createAssistant(CaseFile $case): bool
    {
        try {
            $response = OpenAI::assistants()->create([
                'name' => "Case Agent: {$case->title}",
                'instructions' => self::ASSISTANT_INSTRUCTIONS,
                'model' => self::ASSISTANT_MODEL,
                'tools' => [
                    ['type' => 'file_search'],
                ]
            ]);

            $case->update(['openai_assistant_id' => $response->id]);
            return true;
        } catch (Exception $e) {
            Log::error('Failed to create assistant', [
                'case_id' => $case->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    public function modifyAssistant(CaseFile $case, string $systemPrompt): bool
    {
        try {
            $openAI = \OpenAI::client($case->openaiProject->api_key);
            $openAI->assistants()->modify($case->openai_assistant_id, [
                'instructions' => $systemPrompt
            ]);

            return true;

        } catch (Exception $e) {
            Log::error('Failed to modify assistant', [
                'case_id' => $case->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Reset the assistant to use the default system instructions.
     * If a case summary exists, it will be appended to provide additional context.
     *
     * @param CaseFile $case The case whose assistant needs to be reset
     * @return bool True if reset was successful
     * @throws Exception When reset fails
     */
    public function resetAssistantToDefault(CaseFile $case): bool
    {
        $instructions = self::ASSISTANT_INSTRUCTIONS;

        // Get the latest case summary if available
        $latestSummary = $case->summaries()->latest()->first();
        if ($latestSummary) {
            $instructions .= "\n\nCASE SUMMARY:\n" . $latestSummary->content;
        }

        return $this->modifyAssistant($case, $instructions);
    }

    /**
     * Creates a vector store for the case's documents.
     *
     * @param CaseFile $case The case requiring a vector store
     * @return bool True if creation was successful
     * @throws Exception When vector store creation fails
     */
    private function createVectorStore(CaseFile $case): bool
    {
        try {
            $response = OpenAI::vectorStores()->create([
                'name' => "Case #{$case->id} Vector Store: {$case->title}",
            ]);

            $case->update(['openai_vector_store_id' => $response->id]);
            return true;
        } catch (Exception $e) {
            Log::error('Failed to create vector store', [
                'case_id' => $case->id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Attaches the vector store to the assistant for document search capabilities.
     *
     * @param CaseFile $case The case whose resources need to be linked
     * @return bool True if attachment was successful
     * @throws Exception When attachment fails
     */
    private function attachVectorStoreToAssistant(CaseFile $case): bool
    {
        try {
            OpenAI::assistants()->modify($case->openai_assistant_id, [
                'tool_resources' => [
                    'file_search' =>
                    [
                        'vector_store_ids' => [$case->openai_vector_store_id]
                    ]
                ]
            ]);

            return true;
        } catch (Exception $e) {
            Log::error('Failed to attach vector store to assistant', [
                'case_id' => $case->id,
                'assistant_id' => $case->openai_assistant_id,
                'vector_store_id' => $case->openai_vector_store_id,
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Deletes an OpenAI assistant and its associated resources
     *
     * @param string $assistantId The OpenAI assistant ID to delete
     * @return void
     * @throws \Exception if deletion fails
     */
    public function deleteAssistant(string $assistantId): void
    {
        $this->configureDefaultOpenAi();
        OpenAI::assistants()->delete($assistantId);
    }

    /**
     * Deletes a vector store and its associated embeddings
     *
     * @param string $vectorStoreId The vector store ID to delete
     * @return void
     * @throws \Exception if deletion fails
     */
    public function deleteVectorStore(string $vectorStoreId): void
    {
        $this->configureDefaultOpenAi();
        OpenAI::files()->delete($vectorStoreId);
    }

    /**
     * Configure OpenAI credentials for a specific case.
     *
     * @param CaseFile $case
     * @return void
     * @throws Exception
     */
    public function configureCaseCredentials(CaseFile $case): void
    {
        $this->configureOpenAi($case);
    }
}
