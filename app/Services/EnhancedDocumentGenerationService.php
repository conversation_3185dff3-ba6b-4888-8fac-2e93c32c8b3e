<?php

namespace App\Services;

use App\Models\Draft;
use App\Models\Document;
use App\Utils\ExhibitDocGenerator;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Artisan;
use App\Services\DocumentGenerationService;

class EnhancedDocumentGenerationService
{
    /**
     * Original document generation service
     */
    protected $documentGenerationService;

    /**
     * Constructor
     */
    public function __construct(DocumentGenerationService $documentGenerationService)
    {
        $this->documentGenerationService = $documentGenerationService ?? new DocumentGenerationService();
    }

    /**
     * Generate a document from a draft and merge with exhibits if provided
     *
     * @param Draft $draft The draft to generate a document from
     * @param array $options Additional options for document generation
     * @param array $exhibitIds Optional array of exhibit document IDs to include
     * @return string The path to the generated document
     */
    public function generateDocument(Draft $draft, array $options = [], array $exhibitIds = [])
    {
        try {
            Log::info('Starting document generation process', [
                'draft_id' => $draft->id,
                'has_exhibits' => !empty($exhibitIds),
                'exhibit_count' => count($exhibitIds)
            ]);

            // Step 1: Generate the main document from the draft
            Log::info('Generating main document from draft');
            $docPath = $this->documentGenerationService->generateDocument($draft, $options, []);
            Log::info('Main doc path = ' . $docPath);


            // Step 2: If we have exhibits, process them separately to create a merged exhibit document
            if (!empty($exhibitIds)) {
                Log::info('Processing exhibits separately', [
                    'exhibit_count' => count($exhibitIds)
                ]);

                $title = $draft->document_name ?? $draft->description ?? 'Main Document';

                // Process only the exhibits (not including the main document)
                $this->runConvertDocxToPdfCommand($exhibitIds, $title, $draft->case_file_id, $draft->created_by);
            }

            // Clean up the temporary main document file since it's already saved to S3
            $mainDocPath = storage_path("app/public/{$docPath}");
            if (file_exists($mainDocPath)) {
                if(unlink($mainDocPath)){
                    Log::info('Deleted temporary main doc file');
                } else {
                    Log::info('Failed to delete temporary main doc file');
                }
            }

            return $docPath;

        } catch (\Exception $e) {
            Log::error('Error in document generation', [
                'draft_id' => $draft->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }



    /**
     * Run the ConvertDocxToPdf command to process exhibits only
     *
     * @param array $exhibitIds Array of exhibit document IDs to process
     * @param string|null $title Optional title for the merged document
     * @param int|null $caseFileId Case file ID for the merged document
     * @param int|null $createdBy User ID who created the document
     * @return void
     */
    protected function runConvertDocxToPdfCommand(array $exhibitIds, ?string $title = null, ?int $caseFileId = null, ?int $createdBy = null)
    {
        try {
            Log::info('Calling ExhibitDocGenerator with exhibit IDs only', [
                'exhibit_ids' => $exhibitIds,
                'title' => $title,
                'case_file_id' => $caseFileId,
                'created_by' => $createdBy
            ]);

            (new ExhibitDocGenerator)->handle($exhibitIds, $title, $caseFileId, $createdBy);
        } catch (\Exception $e) {
            Log::error('Failed to generate PDF exhibits', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            // Don't re-throw - let the main document generation succeed even if exhibits fail
            Log::warning('Exhibit generation failed, but main document was generated successfully');
        }
    }
}
