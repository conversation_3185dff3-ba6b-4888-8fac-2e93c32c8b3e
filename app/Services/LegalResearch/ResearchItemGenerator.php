<?php

namespace App\Services\LegalResearch;

use App\Models\AssistantMessage;
use App\Models\AssistantThread;
use App\Models\CaseFile;
use App\Models\CaseSummary;
use App\Models\LegalResearchItem;
use App\Models\User;
use App\Services\AssistantChatService;
use App\Services\OpenAI\CaseAssistantService;
use OpenAI\Laravel\Facades\OpenAI;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Exception;

class ResearchItemGenerator
{
    public function __construct(
        private CaseAssistantService $assistantService,
        private AssistantChatService $chatService
    ) {}

    /**
     * Generate legal research items based on a case summary
     *
     * @param CaseFile $caseFile
     * @param CaseSummary|null $caseSummary
     * @return array
     * @throws \Exception If no case summary is available or if OpenAI request fails
     */
    public function generateResearchItems(CaseFile $caseFile, ?CaseSummary $caseSummary = null): array
    {
        try {
            // Get the latest case summary if not provided
            if (!$caseSummary) {
                $caseSummary = $caseFile->summaries()->latest()->first();

                if (!$caseSummary) {
                    throw new Exception(__('research.no_case_summary'));
                }
            }

            // Configure OpenAI for this case
            $this->assistantService->configureCaseCredentials($caseFile);

            // Create a new thread for research
            $user = Auth::user() ?? User::find($caseFile->user_id);
            $thread = $this->chatService->createThread(
                $caseFile,
                $user,
                'Legal Research Items Generation',
                'Automatically generated research items based on case summary',
                'research',
                AssistantThread::TYPE_RESEARCH
            );

            // Prepare the prompt
            $prompt = $this->buildResearchItemPrompt($caseSummary);

            // Send to OpenAI
            $researchItems = $this->sendToOpenAI($caseFile, $thread->openai_thread_id, $prompt);

            // Parse and store the items
            return $this->parseAndStoreResearchItems($caseFile, $researchItems);

        } catch (Exception $e) {
            Log::error('Error generating research items: ' . $e->getMessage(), [
                'case_id' => $caseFile->id,
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Build the prompt for research item generation
     *
     * @param CaseSummary $caseSummary
     * @return string
     */
    private function buildResearchItemPrompt(CaseSummary $caseSummary): string
    {
        $analysisJson = $caseSummary->analysis_json ? json_encode($caseSummary->analysis_json) : '{}';

        return <<<PROMPT
Given the following case summary and analysis, identify specific legal research items that would be valuable for this case.

CASE SUMMARY:
{$caseSummary->content}

CASE ANALYSIS:
{$analysisJson}

For each research item, provide:
1. A clear title for the research item
2. A description of what needs to be researched and why it's relevant
3. The source type (case_law, statute, regulation, legal_article, legal_document, or other)
4. Any specific citations or references if known
5. A relevance score from 1-100 indicating how important this research is to the case

Format your response as a JSON array of research items with the following structure:
[
  {
    "title": "Title of research item",
    "description": "Description of what needs to be researched",
    "source_type": "case_law",
    "citation": "Any known citation or reference",
    "relevance_score": 85,
    "content_data": {
      "key_questions": ["Question 1", "Question 2"],
      "jurisdiction": "Relevant jurisdiction",
      "time_sensitivity": "high/medium/low"
    }
  }
]

Identify at least 3 and up to 5 distinct research items that would be most valuable for building a strong legal strategy for this case.
If applicable, at least one of these research items should include the court rules for the specific jurisdiction.
PROMPT;
    }

    /**
     * Send the prompt to OpenAI and get the response
     *
     * @param CaseFile $caseFile
     * @param string $threadId
     * @param string $prompt
     * @return string
     */
    private function sendToOpenAI(CaseFile $caseFile, string $threadId, string $prompt): string
    {
        $openAI = \OpenAI::client($caseFile->openaiProject->api_key);

        // Find the thread in our database
        $thread = AssistantThread::where('openai_thread_id', $threadId)->firstOrFail();

        // Create a message in our database
        $userMessage = AssistantMessage::create([
            'assistant_thread_id' => $thread->id,
            'role' => 'user',
            'content' => $prompt
        ]);

        // Add message to OpenAI thread
        $messageResponse = $openAI->threads()->messages()->create($threadId, [
            'role' => 'user',
            'content' => $prompt
        ]);

        // Update with the OpenAI message ID
        $userMessage->update(['openai_message_id' => $messageResponse->id]);

        // Run the assistant
        $run = $openAI->threads()->runs()->create($threadId, [
            'assistant_id' => $caseFile->openai_assistant_id
        ]);

        // Poll for completion
        $runId = $run->id;
        $status = $run->status;

        while ($status !== 'completed' && $status !== 'failed') {
            sleep(1);
            $run = $openAI->threads()->runs()->retrieve($threadId, $runId);
            $status = $run->status;
        }

        if ($status === 'failed') {
            throw new Exception('OpenAI assistant run failed');
        }

        // Get the assistant's response
        $messages = $openAI->threads()->messages()->list($threadId, [
            'limit' => 1,
            'order' => 'desc'
        ]);

        $assistantMessage = $messages->data[0];
        $content = $assistantMessage->content[0]->text->value;

        // Store the assistant's response
        AssistantMessage::create([
            'assistant_thread_id' => $thread->id,
            'role' => 'assistant',
            'content' => $content,
            'openai_message_id' => $assistantMessage->id,
        ]);

        return $content;
    }

    /**
     * Parse the OpenAI response and store research items
     *
     * @param CaseFile $caseFile
     * @param string $aiResponse
     * @return array
     */
    private function parseAndStoreResearchItems(CaseFile $caseFile, string $aiResponse): array
    {
        try {
            // Extract JSON from the response
            preg_match('/\[.*\]/s', $aiResponse, $matches);
            $jsonString = $matches[0] ?? $aiResponse;

            // Parse JSON
            $items = json_decode($jsonString, true);

            if (!is_array($items)) {
                throw new Exception('Failed to parse research items from AI response');
            }

            $storedItems = [];

            foreach ($items as $item) {
                $storedItems[] = LegalResearchItem::create([
                    'case_file_id' => $caseFile->id,
                    'title' => $item['title'],
                    'description' => $item['description'],
                    'source_type' => $item['source_type'],
                    'citation' => $item['citation'] ?? null,
                    'content_data' => $item['content_data'] ?? [],
                    'relevance_score' => $item['relevance_score'] ?? 50,
                    'status' => 'active'
                ]);
            }

            return $storedItems;

        } catch (Exception $e) {
            Log::error('Error parsing research items: ' . $e->getMessage(), [
                'case_id' => $caseFile->id,
                'response' => $aiResponse,
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }
}
