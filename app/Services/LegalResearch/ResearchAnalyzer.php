<?php

namespace App\Services\LegalResearch;

use App\Models\CaseFile;
use App\Models\ResearchSession;
use App\Services\OpenAI\OpenAIService;
use App\Services\CourtListener\CourtListenerService;
use App\Services\VectorStore\VectorStoreService;
use App\Services\LegalResearch\Enums\ResearchTier;
use App\Services\LegalResearch\DTOs\QueryAnalysis;

class ResearchAnalyzer
{
    public function __construct(
        protected OpenAIService $openAI,
        protected CourtListenerService $courtListener,
        protected VectorStoreService $vectorStore,
        protected QueryClassifier $queryClassifier
    ) {}

    public function analyze(array $params): array
    {
        $caseFile = CaseFile::findOrFail($params['case_file_id']);

        // Create research session
        $session = ResearchSession::create([
            'case_file_id' => $caseFile->id,
            'status' => 'researching',
            'search_parameters' => $params
        ]);

        try {
            // 1. Classify the query to determine research path
            $classification = $this->queryClassifier->classify(
                query: $params['question'],
                caseFile: $caseFile
            );

            // 2. Log the research query
            $session->researchQueries()->create([
                'query_text' => $params['question'],
                'source' => $classification->tier->value
            ]);

            // 3. Execute research based on classification
            $results = match($classification->tier) {
                ResearchTier::COURT_LISTENER => $this->handleCourtListenerResearch($params, $session),
                ResearchTier::STAGE_HAND => $this->handleStageHandResearch($params, $session),
                ResearchTier::GPT_RESEARCHER => $this->handleGPTResearch($params, $session),
            };

            // 4. Update session with results
            $session->update([
                'status' => 'completed',
                'research_results' => $results
            ]);

            return [
                'session_id' => $session->id,
                'classification' => $classification,
                'results' => $results
            ];

        } catch (\Exception $e) {
            $session->update([
                'status' => 'failed',
                'research_results' => ['error' => $e->getMessage()]
            ]);

            throw $e;
        }
    }

    protected function handleCourtListenerResearch(array $params, ResearchSession $session): array
    {
        $results = $this->courtListener->search($params);

        // Store citations found
        foreach ($results['citations'] ?? [] as $citation) {
            $session->legalCitations()->create([
                'source_type' => 'court_listener',
                'citation_text' => $citation['text'],
                'relevance_score' => $citation['relevance'] ?? 0.0,
                'metadata' => $citation
            ]);
        }

        return $results;
    }

    protected function handleStageHandResearch(array $params, ResearchSession $session): array
    {
        // Stage Hand typically handles targeted research with known sources
        $vectorResults = $this->vectorStore->search($params['question'], [
            'case_file_id' => $session->case_file_id
        ]);

        $enhancedParams = array_merge($params, [
            'vector_results' => $vectorResults,
            'session_id' => $session->id
        ]);

        return [
            'vector_results' => $vectorResults,
            'analysis' => $this->openAI->analyze($enhancedParams)
        ];
    }

    protected function handleGPTResearch(array $params, ResearchSession $session): array
    {
        // GPT Researcher handles complex analysis requiring deep research
        $initialContext = $this->vectorStore->search($params['question'], [
            'case_file_id' => $session->case_file_id,
            'limit' => 10
        ]);

        $researchPlan = $this->openAI->createResearchPlan([
            'question' => $params['question'],
            'initial_context' => $initialContext,
            'session_id' => $session->id
        ]);

        $research = [];
        foreach ($researchPlan['steps'] as $step) {
            $stepResults = $this->executeResearchStep($step, $params, $session);
            $research[] = [
                'step' => $step,
                'results' => $stepResults
            ];
        }

        return [
            'research_plan' => $researchPlan,
            'research_steps' => $research,
            'final_analysis' => $this->openAI->synthesizeResearch($research)
        ];
    }

    protected function executeResearchStep(array $step, array $params, ResearchSession $session): array
    {
        // Log the research query for this step
        $session->researchQueries()->create([
            'query_text' => $step['query'],
            'source' => $step['source']
        ]);

        // Execute the research step based on the source
        $results = match($step['source']) {
            'court_listener' => $this->courtListener->search(['question' => $step['query']]),
            'vector_store' => $this->vectorStore->search($step['query'], [
                'case_file_id' => $session->case_file_id
            ]),
            default => $this->openAI->analyze([
                'question' => $step['query'],
                'context' => $step['context'] ?? []
            ])
        };

        // Store any citations found
        if (isset($results['citations'])) {
            foreach ($results['citations'] as $citation) {
                $session->legalCitations()->create([
                    'source_type' => $step['source'],
                    'citation_text' => $citation['text'],
                    'relevance_score' => $citation['relevance'] ?? 0.0,
                    'metadata' => $citation
                ]);
            }
        }

        return $results;
    }
}
