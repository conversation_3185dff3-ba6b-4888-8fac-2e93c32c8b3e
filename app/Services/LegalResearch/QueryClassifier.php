<?php

namespace App\Services\LegalResearch;

use App\Models\CaseFile;
use App\Services\OpenAI\OpenAIService;
use App\Services\VectorStore\VectorStoreService;
use App\Services\LegalResearch\DTOs\ClassificationResult;
use App\Services\LegalResearch\DTOs\VectorSearchResult;
use App\Services\LegalResearch\DTOs\QueryAnalysis;
use App\Services\LegalResearch\Enums\ResearchTier;
use App\Services\LegalResearch\Enums\ConfidenceLevel;

class QueryClassifier
{
    public function __construct(
        protected VectorStoreService $vectorStore,
        protected OpenAIService $openAI
    ) {}

    public function classify(string $query, CaseFile $caseFile): ClassificationResult
    {
        // Step 1: Check vector store context
        $vectorResults = $this->checkVectorContext($query, $caseFile);

        // Step 2: Initial query analysis
        $analysis = $this->analyzeQuery($query, $vectorResults);

        // Step 3: Determine research path
        return $this->determineResearchPath($analysis);
    }

    protected function checkVectorContext(string $query, CaseFile $caseFile): VectorSearchResult
    {
        return $this->vectorStore->search($caseFile, $query);
    }

    protected function analyzeQuery(string $query, VectorSearchResult $vectorResults): QueryAnalysis
    {
        $prompt = $this->buildAnalysisPrompt($query, $vectorResults);

        $analysis = $this->openAI->analyze([
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a legal research assistant. Analyze the query and context to determine research requirements.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ]
        ]);

        return new QueryAnalysis(
            confidence: $this->calculateConfidence($vectorResults, $analysis),
            requiredContextTypes: $analysis['required_context_types'] ?? [],
            legalConcepts: $analysis['legal_concepts'] ?? [],
            timeSensitivity: $analysis['time_sensitivity'] ?? 'normal',
            jurisdictions: $analysis['jurisdictions'] ?? [],
            complexity: $analysis['complexity'] ?? 'medium'
        );
    }

    protected function determineResearchPath(QueryAnalysis $analysis): ClassificationResult
    {
        // Implement decision logic from query_classification.md

        // Tier 1: Direct API (Court Listener)
        if ($this->shouldUseTierOne($analysis)) {
            return new ClassificationResult(
                tier: ResearchTier::COURT_LISTENER,
                confidence: $analysis->confidence,
                expectedTime: 5, // seconds
                reasoning: 'High confidence match with simple verification needs'
            );
        }

        // Tier 2: Stage Hand
        if ($this->shouldUseTierTwo($analysis)) {
            return new ClassificationResult(
                tier: ResearchTier::STAGE_HAND,
                confidence: $analysis->confidence,
                expectedTime: 30, // seconds
                reasoning: 'Medium complexity with specific domain focus'
            );
        }

        // Tier 3: GPT Researcher (default for complex queries)
        return new ClassificationResult(
            tier: ResearchTier::GPT_RESEARCHER,
            confidence: $analysis->confidence,
            expectedTime: 300, // seconds
            reasoning: 'Complex analysis requiring deep research'
        );
    }

    private function shouldUseTierOne(QueryAnalysis $analysis): bool
    {
        return $analysis->confidence === ConfidenceLevel::HIGH
            && count($analysis->jurisdictions) <= 1
            && $analysis->complexity === 'low'
            && !$analysis->requiresStrategyFormation();
    }

    private function shouldUseTierTwo(QueryAnalysis $analysis): bool
    {
        return $analysis->confidence === ConfidenceLevel::MEDIUM
            && count($analysis->jurisdictions) <= 2
            && $analysis->complexity === 'medium'
            && !empty($analysis->requiredContextTypes)
            && !$analysis->requiresStrategyFormation();
    }

    private function buildAnalysisPrompt(string $query, VectorSearchResult $vectorResults): string
    {
        $contextSection = '';

        if ($vectorResults->hasResults()) {
            $contextSection = "Relevant Context:\n";
            foreach ($vectorResults->results as $result) {
                $contextSection .= "- {$result->content} (Similarity: {$result->similarity})\n";
            }
        }

        return <<<PROMPT
Query: {$query}

{$contextSection}

Analyze the following aspects:
1. Required context types (citations, definitions, precedents)
2. Legal concepts involved
3. Time sensitivity
4. Jurisdictional scope
5. Query complexity

Provide structured analysis in JSON format.
PROMPT;
    }

    private function calculateConfidence(VectorSearchResult $vectorResults, array $analysis): ConfidenceLevel
    {
        if ($vectorResults->isHighConfidence()) {
            return ConfidenceLevel::HIGH;
        }

        if ($vectorResults->isMediumConfidence()) {
            return ConfidenceLevel::MEDIUM;
        }

        return ConfidenceLevel::LOW;
    }
}
