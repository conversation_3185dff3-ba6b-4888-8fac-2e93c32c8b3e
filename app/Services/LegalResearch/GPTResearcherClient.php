<?php

namespace App\Services\LegalResearch;

use App\Models\LegalResearchItem;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class GPTResearcherClient
{
    private $apiUrl;

    public function __construct()
    {
        $this->apiUrl = config('services.gpt_researcher.url', 'http://localhost:8081');
    }

    /**
     * Create a new research request
     *
     * @param LegalResearchItem $item
     * @return array
     */
    public function createResearchRequest(LegalResearchItem $item): array
    {
        try {
            $url = $this->apiUrl . '/api/legal-research/';
            $payload = [
                'research_question' => $this->buildResearchQuestion($item),
                'case_id' => (string) $item->case_file_id,
                'research_item_id' => (string) $item->id
            ];

            // Log the request for debugging
            Log::debug('Creating research request', [
                'url' => $url,
                'payload' => $payload,
                'item_id' => $item->id
            ]);

            $response = Http::withHeaders([
                'X-API-Key' => env('LEGAL_RESEARCH_API_KEY')
            ])->post($url, $payload);

            if (!$response->successful()) {
                throw new \Exception('Failed to create research request: ' . $response->body());
            }

            return $response->json();
        } catch (\Exception $e) {
            Log::error('Error creating research request: ' . $e->getMessage(), [
                'item_id' => $item->id,
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Check the status of a research request
     *
     * @param string $researchId
     * @return array
     */
    public function checkResearchStatus(string $researchId): array
    {
        try {
            $url = $this->apiUrl . '/api/legal-research/status/' . $researchId;

            // Log the request URL for debugging
            Log::debug('Checking research status', [
                'url' => $url,
                'research_id' => $researchId
            ]);

            $response = Http::withHeaders([
                'X-API-Key' => env('LEGAL_RESEARCH_API_KEY')
            ])->get($url);

            if (!$response->successful()) {
                throw new \Exception('Failed to check research status: ' . $response->body());
            }

            return $response->json();
        } catch (\Exception $e) {
            Log::error('Error checking research status: ' . $e->getMessage(), [
                'research_id' => $researchId,
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Retry a failed research request
     *
     * @param string $researchId
     * @return array
     */
    public function retryResearch(string $researchId): array
    {
        try {
            $response = Http::withHeaders([
                'X-API-Key' => env('LEGAL_RESEARCH_API_KEY')
            ])->post($this->apiUrl . '/api/legal-research/retry/' . $researchId);

            if (!$response->successful()) {
                throw new \Exception('Failed to retry research: ' . $response->body());
            }

            return $response->json();
        } catch (\Exception $e) {
            Log::error('Error retrying research: ' . $e->getMessage(), [
                'research_id' => $researchId,
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Build a research question from a legal research item
     *
     * @param LegalResearchItem $item
     * @return string
     */
    private function buildResearchQuestion(LegalResearchItem $item): string
    {
        $question = $item->title . ': ' . $item->description;

        // Add key questions if available
        if (isset($item->content_data['key_questions']) && is_array($item->content_data['key_questions'])) {
            $question .= "\n\nKey questions to address:\n";
            foreach ($item->content_data['key_questions'] as $keyQuestion) {
                $question .= "- " . $keyQuestion . "\n";
            }
        }

        // Add citation if available
        if ($item->citation) {
            $question .= "\n\nRelevant citation: " . $item->citation;
        }

        // Add jurisdiction if available
        if (isset($item->content_data['jurisdiction'])) {
            $question .= "\n\nJurisdiction: " . $item->content_data['jurisdiction'];
        }

        return $question;
    }


}
