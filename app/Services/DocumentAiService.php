<?php

namespace App\Services;

use App\Models\Draft;
use App\Models\AssistantThread;
use App\Models\AssistantMessage;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class DocumentAiService
{
    protected $assistantChatService;
    protected $caseFile;
    protected $thread;

    public function __construct(AssistantChatService $assistantChatService)
    {
        $this->assistantChatService = $assistantChatService;
    }

    /**
     * Initialize the service for a specific draft
     */
    public function initializeForDraft(Draft $draft)
    {
        $this->caseFile = $draft->caseFile;

        // Get or create a dedicated thread for document editing
        $this->thread = AssistantThread::where('case_file_id', $this->caseFile->id)
            ->where('type', 'document_editor')
            ->where('metadata->draft_id', $draft->id)
            ->first();

        $systemPrompt = $this->getDocumentSystemPrompt($draft);

        if (!$this->thread) {
            // Create a new thread for this draft
            $this->thread = $this->assistantChatService->createThreadWithSystemPrompt(
                $this->caseFile,
                Auth::user(),
                "Document Editor: {$draft->draft_type}",
                "Thread for editing {$draft->draft_type} document",
                'document_editor',
                $systemPrompt,
                'document_editor'
            );

            // Store draft ID in thread metadata
            $metadata = $this->thread->metadata ?? [];
            $metadata['draft_id'] = $draft->id;
            $this->thread->update(['metadata' => $metadata]);
        } else {
            $assistantService = app(\App\Services\OpenAI\CaseAssistantService::class);
            $assistantService->modifyAssistant($this->caseFile, $systemPrompt);
        }


        return $this;
    }

    /**
     * Generate content using the AI assistant
     */
    public function generateContent($prompt, $context = [])
    {
        if (!$this->thread) {
            // If thread is not initialized, try to get it from the context
            if (isset($context['draft']) && $context['draft'] instanceof Draft) {
                $this->initializeForDraft($context['draft']);
            } else {
                throw new \Exception('Thread not initialized. Call initializeForDraft() first.');
            }
        }
        $userDisplayPrompt = null;
        if ($context['prompt_type'] == 'section_generation') {
            $activeSection = ucwords(str_replace('_', ' ', $context['active_section']));
            $userDisplayPrompt = "Generate content for the {$activeSection} section.";
        }

        // Create a message with the prompt and context
        // The user's prompt is stored in the message for display purposes,
        // but the details (formatPromptWithContext) and extra context will be added to the openai message (unseen)
        $message = AssistantMessage::create([
            'assistant_thread_id' => $this->thread->id,
            'role' => 'user',
            'content' => $userDisplayPrompt ?? $prompt,
        ]);

        $detailedPrompt = $this->formatPromptWithContext($prompt, $context);

        // Log the prompt type and content for debugging
        $promptType = isset($context['prompt_type']) ? $context['prompt_type'] : 'user_request';
        Log::info('Sending prompt to AI assistant', [
            'prompt_type' => $promptType,
            'original_prompt' => $prompt,
            'prompt_length' => strlen($detailedPrompt),
            'active_section' => $context['active_section'] ?? 'none',
            'draft_id' => isset($context['draft']) ? $context['draft']->id : 'unknown'
        ]);

        // For detailed debugging, log the full prompt (may be large)
        Log::debug('Full prompt sent to AI assistant', [
            'prompt_type' => $promptType,
            'detailed_prompt' => $detailedPrompt
        ]);

        // Send the message and get the response
        $response = $this->assistantChatService->sendMessageToAssistant($message, $this->thread, $detailedPrompt);
        Log::info('AI response received', ['response' => $response]);

        // Parse the response as JSON
        return $this->parseJsonResponse($response);
    }

    /**
     * Format the prompt with context information
     */
    protected function formatPromptWithContext($prompt, $context = [])
    {
        // Start with the main prompt
        $formattedPrompt = $prompt;

        // Add section-specific context if needed
        if (isset($context['active_section']) && isset($context['section_content'])) {
            $formattedPrompt .= "\n\nCurrent section content to modify (unless otherwise specified by the user):\n";
            $formattedPrompt .= $context['section_content'];
        }

        return $formattedPrompt;
    }

    /**
     * Parse the JSON response from the AI assistant
     */
    protected function parseJsonResponse($response)
    {
        // Try to parse the response as JSON
        $jsonStart = strpos($response, '{');
        $jsonEnd = strrpos($response, '}');

        if ($jsonStart !== false && $jsonEnd !== false) {
            $jsonString = substr($response, $jsonStart, $jsonEnd - $jsonStart + 1);
            $data = json_decode($jsonString, true);

            if (json_last_error() === JSON_ERROR_NONE) {
                return $data;
            }
        }

        dd($response);

        // If JSON parsing fails, return a formatted response
        return [
            'document_content' => $response,
            'changes_explanation' => 'The AI assistant provided a response, but it was not in the expected JSON format. The full response has been included as the document content.'
        ];
    }

    /**
     * Get the system prompt for the document assistant
     */
    protected function getDocumentSystemPrompt(Draft $draft)
    {
        $caseFile = $draft->caseFile;

        // Start with the basic role and purpose
        $systemPrompt = "## PERSONA\n";
        $systemPrompt .= "You are 'JusticeAI', an expert AI legal assistant. Your specialization is drafting and refining legal documents for litigation within the United States legal system. You are precise, formal, and your primary goal is to produce clear, accurate, and court-compliant content based on the provided case materials. You are helping to draft a {$draft->draft_type} for a legal case.\n";
        $systemPrompt .= "\n\n";

        // Add comprehensive information about parties
        $systemPrompt .= $this->getPartiesInformation($caseFile);

        // Add case information
        $systemPrompt .= "## CASE INFORMATION\n";
        $systemPrompt .= "**Case Title:** {$caseFile->title}\n";

        if (!empty($caseFile->initial_summary)) {
            $systemPrompt .= "**Case Summary:** {$caseFile->initial_summary}\n";
        }

        if (!empty($caseFile->case_number)) {
            $systemPrompt .= "**Case Number:** {$caseFile->case_number}\n";
        }

        if (!empty($caseFile->case_types) && is_array($caseFile->case_types)) {
            $systemPrompt .= "**Case Types:** " . implode(', ', $caseFile->case_types) . "\n";
        }

        if (!empty($caseFile->status)) {
            $systemPrompt .= "**Status:** {$caseFile->status}\n";
        }

        // Add case summary if available
        $latestSummary = $caseFile->summaries()->latest()->first();
        if ($latestSummary) {
            $systemPrompt .= "\n## CASE SUMMARY\n";
            $systemPrompt .= "{$latestSummary->content}\n";
        }

        // Add case strategy if one was selected
        if (!empty($draft->metadata) && !empty($draft->metadata['strategy_id'])) {
            $strategyId = $draft->metadata['strategy_id'];
            $strategy = \App\Models\CaseStrategy::find($strategyId);

            if ($strategy) {
                $systemPrompt .= "\n## CASE STRATEGY\n";
                $systemPrompt .= "**Strategy Title:** {$strategy->title}\n";

                if (!empty($strategy->executive_summary)) {
                    $systemPrompt .= "**Executive Summary:** {$strategy->executive_summary}\n";
                }

                if (!empty($strategy->legal_analysis) && is_array($strategy->legal_analysis)) {
                    $systemPrompt .= "\n**Legal Analysis:**\n";
                    foreach ($strategy->legal_analysis as $key => $analysis) {
                        if (is_string($analysis)) {
                            $systemPrompt .= "- {$analysis}\n";
                        }
                    }
                }

                if (!empty($strategy->action_items) && is_array($strategy->action_items)) {
                    $systemPrompt .= "\n**Action Items:**\n";
                    foreach ($strategy->action_items as $key => $item) {
                        if (is_string($item)) {
                            $systemPrompt .= "- {$item}\n";
                        }
                    }
                }
            }
        }

        // Add reference draft if one was selected
        if (!empty($draft->metadata) && !empty($draft->metadata['reference_draft_id'])) {
            $referenceDraftId = $draft->metadata['reference_draft_id'];
            $referenceDraft = \App\Models\Draft::find($referenceDraftId);

            if ($referenceDraft) {
                $systemPrompt .= "\n## REFERENCE DOCUMENT\n";
                $systemPrompt .= "**Reference Document Type:** {$referenceDraft->draft_type}\n";
                $systemPrompt .= "**Reference Document Name:** {$referenceDraft->document_name}\n";
                $systemPrompt .= "**Reference Document Description:** {$referenceDraft->description}\n\n";
                $systemPrompt .= "The following document content should be used as reference for style and tone, but do not copy its substance unless instructed:\n\n";

                // Add reference draft sections content
                if (!empty($referenceDraft->sections_structure) && is_array($referenceDraft->sections_structure)) {
                    foreach ($referenceDraft->sections_structure as $section) {
                        if (!empty($section['id']) && !empty($section['content']) && !empty($section['name'])) {
                            // Skip caption sections as they're usually JSON data
                            if ($section['id'] === 'caption') {
                                continue;
                            }

                            $sectionContent = $section['content'];
                            // If content is JSON (like from Quill editor), try to extract plain text
                            if (is_string($sectionContent) && $this->isJsonString($sectionContent)) {
                                try {
                                    $jsonContent = json_decode($sectionContent, true);
                                    if (isset($jsonContent['ops'])) {
                                        $plainText = '';
                                        foreach ($jsonContent['ops'] as $op) {
                                            if (isset($op['insert']) && is_string($op['insert'])) {
                                                $plainText .= $op['insert'];
                                            }
                                        }
                                        $sectionContent = $plainText;
                                    }
                                } catch (\Exception $e) {
                                    // If JSON parsing fails, use the original content
                                }
                            }

                            $systemPrompt .= "### {$section['name']}\n";
                            $systemPrompt .= $sectionContent . "\n\n";
                        }
                    }
                }
            }
        }

        // Add information about document type and structure
        $systemPrompt .= "\n## DOCUMENT INFORMATION\n";
        $systemPrompt .= "**Document Type:** {$draft->draft_type}\n";
        if (!empty($draft->description)) {
            $systemPrompt .= "**Document Description:** {$draft->description}\n";
        }

        // Add information about document sections
        if (!empty($draft->sections_structure)) {
            $systemPrompt .= "\n**Document Structure and Section Keys:**\n";
            foreach ($draft->sections_structure as $section) {
                $systemPrompt .= "- (section name) `{$section['name']}` | (section key) `{$section['id']}`\n";
            }
        }

        $systemPrompt .= "\n## INFORMATION HIERARCHY\n";
        $systemPrompt .= "When drafting, adhere to the following hierarchy of information:\n";
        $systemPrompt .= "1. **Exhibits & Factual Record:** These are the primary source of truth for facts.\n";
        $systemPrompt .= "2. **Case Strategy:** The selected strategy guides the overall narrative and legal arguments.\n";
        $systemPrompt .= "3. **Reference Document:** Use this for style, tone, and structure, but do not copy its substance unless instructed.\n";
        $systemPrompt .= "4. **General Case Information:** Use this for background context.\n";
        $systemPrompt .= "If you encounter a conflict, prioritize the source higher on this list.\n";

        // Add vector store and research reminder
        $systemPrompt .= "\n## AVAILABLE RESOURCES\n";
        $systemPrompt .= " - You have access to the case's exhibits and research reports through the vector store. ";
        $systemPrompt .= " - Use this information to create accurate, well-supported document content. ";
        $systemPrompt .= " - When drafting content, incorporate relevant facts from exhibits and legal principles from research reports. ";
        $systemPrompt .= " - If you need specific information that might be in an exhibit or research report, mention it in your explanation.\n";

        // Add detailed list of all exhibits in the case file
        $exhibits = $caseFile->exhibits()->with('document')->get();
        if ($exhibits && $exhibits->count() > 0) {
            $systemPrompt .= "\n### DETAILED EXHIBIT LIST\n";
            $systemPrompt .= "The following exhibits are available in this case file:\n";
            foreach ($exhibits as $exhibit) {
                $document = $exhibit->document;
                $systemPrompt .= "- **Exhibit {$exhibit->label}**: {$document->description}\n";
                $systemPrompt .= "  - Title: {$document->title}\n";
                $systemPrompt .= "  - Document Type: {$document->document_type}\n";
                if (!empty($document->metadata)) {
                    $systemPrompt .= "  - Additional Metadata: " . json_encode($document->metadata) . "\n";
                }
                $systemPrompt .= "\n";
            }
        }

        $systemPrompt .= "\n## OUTPUT FORMAT INSTRUCTIONS\n";
        $systemPrompt .= "Your response MUST be a single, valid JSON object. Do not include any text or markdown formatting before or after the JSON object.\n";
        $systemPrompt .= "The JSON object must have the following structure:\n";
        $systemPrompt .= "```json\n";
        $systemPrompt .= "{\n";
        $systemPrompt .= "  \"content_section\": \"The section key you are updating (e.g., 'introduction'). This MUST match a key from the 'Document Structure and Section Keys' list.\",\n";
        $systemPrompt .= "  \"document_content\": { /* A valid Quill Delta JSON object */ },\n";
        $systemPrompt .= "  \"changes_explanation\": \"A user-facing explanation of the changes you made.\",\n";
        $systemPrompt .= "  \"conversational_response\": \"A conversational response ONLY for non-document-related queries. This field is mutually exclusive with 'document_content'.\"\n";
        $systemPrompt .= "}\n";
        $systemPrompt .= "```\n";
        $systemPrompt .= "**CRITICAL RULES:**\n";
        $systemPrompt .= "1.  The `document_content` and `conversational_response` fields are **mutually exclusive**. If you are generating document content, `conversational_response` must be `null`. If you are having a conversation, `document_content` must be `null`.\n";
        $systemPrompt .= "2.  The `document_content` field **MUST** be a valid Quill Delta JSON object. Do not use plain text or HTML.\n";
        $systemPrompt .= "3.  The `changes_explanation` will be shown in the chat interface. The `document_content` will be used to update the editor.\n";
        $systemPrompt .= "4.  if you are ever asked to produce a full document with all sections, direct the user to click the 'Generate Full Document' button instead of doing this on your own.\n";
        $systemPrompt .= "5.  if you are ever asked to modify multiple sections at a time, inform the user that you can only do one section at a time.\n";


        $systemPrompt .= "\n### QUILL DELTA FORMATTING GUIDANCE\n";
        $systemPrompt .= "The `document_content` field must be a Quill Delta object. Follow these guidelines:\n";
        $systemPrompt .= "- Use header formatting for section titles and subtitles with appropriate levels (e.g., `{\"insert\": \"Header Text\\n\", \"attributes\": {\"header\": 1}}`).\n";
        $systemPrompt .= "- Use list formatting for ordered or bulleted lists (e.g., `{\"insert\": \"List Item\\n\", \"attributes\": {\"list\": \"bullet\"}}`).\n";
        $systemPrompt .= "- Use the blockquote attribute for quotations (e.g., `{\"insert\": \"Quoted Text\\n\", \"attributes\": {\"blockquote\": true}}`).\n";
        $systemPrompt .= "- For legal citations, use plain text formatting within the Delta structure.\n";
        $systemPrompt .= "- Each paragraph, list item, or heading should be a separate `insert` operation ending with a newline (`\\n`).\n";

        $systemPrompt .= "\n### CAPTION SECTION FORMATTING\n";
        $systemPrompt .= "If you are asked to generate content for the `caption` section, the `document_content` field must contain a specific JSON object representing the caption data, NOT a Quill Delta. It should have the following structure:\n";
        $systemPrompt .= "```json\n";
        $systemPrompt .= "{\n";
        $systemPrompt .= "  \"courtName\": \"...\",\n";
        $systemPrompt .= "  \"division\": \"...\",\n";
        $systemPrompt .= "  \"caseNumber\": \"...\",\n";
        $systemPrompt .= "  \"judgeName\": \"...\",\n";
        $systemPrompt .= "  \"documentTitle\": \"...\",\n";
        $systemPrompt .= "  \"plaintiffs\": [{\"name\": \"...\", \"role\": \"Plaintiff\"}],\n";
        $systemPrompt .= "  \"defendants\": [{\"name\": \"...\", \"role\": \"Defendant\"}]\n";
        $systemPrompt .= "}\n";
        $systemPrompt .= "```\n";

        $systemPrompt .= "\n## MANAGING UNCLEAR USER REQUESTS\n";
        $systemPrompt .= " - The current interface has 2 generation buttons: 'Generate Current Section' and 'Generate Full Document'.\n";
        $systemPrompt .= " - If you are uncertain about the user's intent or your own ability to fulfill the request, you may direct the user to use one of these buttons.\n";


        return $systemPrompt;
    }

    /**
     * Get comprehensive parties information from structured interview data and Party models
     */
    protected function getPartiesInformation($caseFile)
    {
        $partiesInfo = "";
        $allParties = [];

        // Get parties from structured interview data
        $structuredData = $caseFile->structured_interview_data;
        if (!empty($structuredData) && isset($structuredData['parties'])) {
            // Get all party IDs from the structured data
            $partyIds = array_keys($structuredData['parties']);

            // Fetch the actual Party models using the IDs
            $dbParties = \App\Models\Party::whereIn('id', $partyIds)->get()->keyBy('id');

            foreach ($structuredData['parties'] as $partyId => $partyData) {
                // Start with interview data
                $partyInfo = [
                    'id' => $partyData['id'] ?? $partyId,
                    'name' => $partyData['name'] ?? 'Unknown',
                    'interview_relationship' => $partyData['relationship'] ?? null,
                    'source' => 'interview'
                ];

                // If we have the full Party model, add complete details
                if (isset($dbParties[$partyId])) {
                    $party = $dbParties[$partyId];
                    $partyInfo = array_merge($partyInfo, [
                        'name' => $party->name, // Use database name as authoritative
                        'relationship' => $party->relationship,
                        'address_line1' => $party->address_line1,
                        'address_line2' => $party->address_line2,
                        'city' => $party->city,
                        'state' => $party->state,
                        'zip' => $party->zip,
                        'email' => $party->email,
                        'phone' => $party->phone,
                        'source' => 'database_and_interview'
                    ]);
                }

                $allParties[$partyId] = $partyInfo;
            }
        }

        if (!empty($allParties)) {
            $partiesInfo .= "\n## PARTIES INVOLVED + CONTACT INFORMATION\n";

            foreach ($allParties as $party) {
                $partiesInfo .= "**{$party['name']}**\n";

                // Add relationship information
                if (!empty($party['relationship'])) {
                    $partiesInfo .= "General Relationship: {$party['relationship']}\n";
                }
                if (!empty($party['interview_relationship']) && $party['interview_relationship'] !== ($party['relationship'] ?? null)) {
                    $partiesInfo .= "Interview Relationship: {$party['interview_relationship']}\n";
                }

                // Add address information if available
                $address = [];
                if (!empty($party['address_line1'])) {
                    $address[] = $party['address_line1'];
                }
                if (!empty($party['address_line2'])) {
                    $address[] = $party['address_line2'];
                }
                if (!empty($party['city'])) {
                    $address[] = $party['city'];
                }
                if (!empty($party['state'])) {
                    $address[] = $party['state'];
                }
                if (!empty($party['zip'])) {
                    $address[] = $party['zip'];
                }

                if (!empty($address)) {
                    $partiesInfo .= "Address: " . implode(', ', $address) . "\n";
                }

                // Add contact information if available
                if (!empty($party['email'])) {
                    $partiesInfo .= "Email: {$party['email']}\n";
                }
                if (!empty($party['phone'])) {
                    $partiesInfo .= "Phone: {$party['phone']}\n";
                }

                $partiesInfo .= "\n";
            }
        }

        return $partiesInfo;
    }

    /**
     * Check if a string is valid JSON
     */
    protected function isJsonString($string)
    {
        if (!is_string($string)) {
            return false;
        }

        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }
}
