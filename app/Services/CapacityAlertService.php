<?php

namespace App\Services;

use App\Models\OpenAiProject;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Mail;

class CapacityAlertService
{
    /**
     * Check capacity and send alerts if needed.
     * Called automatically when users sign up or projects fill up.
     */
    public function checkCapacityAndAlert(): array
    {
        $alerts = [];
        
        // Check if we've already sent alerts recently (prevent spam)
        $lastAlertTime = Cache::get('capacity_alert_last_sent');
        if ($lastAlertTime && now()->diffInMinutes($lastAlertTime) < 60) {
            return []; // Don't send alerts more than once per hour
        }

        $projects = OpenAiProject::where('is_active', true)->with('capacity')->get();
        $acceptingProjects = $projects->filter(fn($p) => $p->capacity && $p->capacity->accepts_new_users);

        // Critical: No capacity for new users
        if ($acceptingProjects->count() === 0) {
            $alert = [
                'level' => 'critical',
                'title' => 'URGENT: No OpenAI Project Capacity',
                'message' => 'All OpenAI projects are at capacity. New user registrations will fail.',
                'action' => 'Add new OpenAI project immediately',
                'impact' => 'New users cannot sign up',
                'projects_available' => 0
            ];
            
            $this->sendAlert($alert);
            $alerts[] = $alert;
        }
        // Warning: Very low capacity
        elseif ($acceptingProjects->count() === 1) {
            $project = $acceptingProjects->first();
            $capacityInfo = $project->capacity->getCapacityInfo();
            
            $alert = [
                'level' => 'warning',
                'title' => 'Low OpenAI Project Capacity',
                'message' => "Only 1 project accepting new users: {$project->name}",
                'action' => 'Add new OpenAI project within 1 week',
                'impact' => 'Risk of running out of capacity soon',
                'projects_available' => 1,
                'remaining_capacity_gb' => $capacityInfo['available_gb']
            ];
            
            $this->sendAlert($alert);
            $alerts[] = $alert;
        }

        // Check individual project storage levels
        foreach ($projects as $project) {
            $storageInfo = $project->getStorageInfo();
            
            if ($storageInfo['percentage'] >= 95) {
                $alert = [
                    'level' => 'critical',
                    'title' => 'Project Storage Critical',
                    'message' => "Project {$project->name} is at {$storageInfo['percentage']}% storage capacity",
                    'action' => 'Monitor closely, may need cleanup or new project',
                    'impact' => 'Project may stop accepting uploads soon',
                    'project_name' => $project->name,
                    'storage_percentage' => $storageInfo['percentage']
                ];
                
                $this->sendAlert($alert);
                $alerts[] = $alert;
            }
        }

        if (!empty($alerts)) {
            Cache::put('capacity_alert_last_sent', now(), 3600); // 1 hour cache
        }

        return $alerts;
    }

    /**
     * Get current capacity status for dashboard display.
     */
    public function getCapacityStatus(): array
    {
        $projects = OpenAiProject::where('is_active', true)->with('capacity')->get();
        $acceptingProjects = $projects->filter(fn($p) => $p->capacity && $p->capacity->accepts_new_users);

        $totalCapacity = [
            'basic' => 0,
            'standard' => 0,
            'pro' => 0
        ];

        $totalAvailableGB = 0;

        foreach ($acceptingProjects as $project) {
            if (!$project->capacity) continue;
            
            $available = $project->capacity->getAvailableCapacity();
            $totalAvailableGB += $available / (1024 * 1024 * 1024);

            // Calculate user capacity
            $totalCapacity['basic'] += floor($available / (1 * 1024 * 1024 * 1024));
            $totalCapacity['standard'] += floor($available / (5 * 1024 * 1024 * 1024));
            $totalCapacity['pro'] += floor($available / (10 * 1024 * 1024 * 1024));
        }

        // Determine overall status
        $status = 'good';
        $statusMessage = 'Capacity is healthy';
        
        if ($acceptingProjects->count() === 0) {
            $status = 'critical';
            $statusMessage = 'No capacity for new users - ADD PROJECT NOW!';
        } elseif ($acceptingProjects->count() === 1) {
            $status = 'warning';
            $statusMessage = 'Low capacity - add project soon';
        } elseif ($totalCapacity['basic'] < 50) {
            $status = 'warning';
            $statusMessage = 'Capacity getting low';
        }

        return [
            'status' => $status,
            'message' => $statusMessage,
            'projects_total' => $projects->count(),
            'projects_accepting' => $acceptingProjects->count(),
            'available_storage_gb' => round($totalAvailableGB, 1),
            'new_user_capacity' => $totalCapacity,
            'last_checked' => now()->toISOString()
        ];
    }

    /**
     * Check if a specific user tier can be accommodated.
     */
    public function canAccommodateNewUser(string $tier): bool
    {
        $projects = OpenAiProject::where('is_active', true)
            ->with('capacity')
            ->get()
            ->filter(fn($p) => $p->capacity && $p->capacity->accepts_new_users);

        foreach ($projects as $project) {
            if ($project->capacity->canAccommodateUser($tier)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Send alert through various channels.
     */
    private function sendAlert(array $alert): void
    {
        // Log the alert
        Log::log($alert['level'], $alert['title'], [
            'message' => $alert['message'],
            'action' => $alert['action'],
            'impact' => $alert['impact'],
            'alert_type' => 'capacity_alert',
            'timestamp' => now()->toISOString(),
            'alert_data' => $alert
        ]);

        // You can add additional alert channels here:
        // - Email notifications
        // - Slack webhooks
        // - SMS alerts
        // - Dashboard notifications
        
        // Example email alert (uncomment and configure as needed):
        /*
        if ($alert['level'] === 'critical') {
            Mail::to(config('app.admin_email'))->send(
                new CapacityAlert($alert)
            );
        }
        */
    }

    /**
     * Get capacity trends for analytics.
     */
    public function getCapacityTrends(int $days = 7): array
    {
        // This could be enhanced to track historical capacity data
        // For now, return current snapshot
        
        $projects = OpenAiProject::where('is_active', true)->with('capacity')->get();
        
        $trends = [
            'period_days' => $days,
            'current_date' => now()->toDateString(),
            'projects' => []
        ];

        foreach ($projects as $project) {
            $storageInfo = $project->getStorageInfo();
            $capacityInfo = $project->capacity ? $project->capacity->getCapacityInfo() : null;

            $trends['projects'][] = [
                'name' => $project->name,
                'storage_percentage' => $storageInfo['percentage'],
                'allocated_percentage' => $capacityInfo ? $capacityInfo['allocation_percentage'] : 0,
                'accepts_new_users' => $capacityInfo ? $capacityInfo['accepts_new_users'] : false,
                'user_count' => $capacityInfo ? $capacityInfo['total_users'] : 0
            ];
        }

        return $trends;
    }
}
