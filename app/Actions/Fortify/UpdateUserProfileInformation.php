<?php

namespace App\Actions\Fortify;

use App\Models\User;
use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Laravel\Fortify\Contracts\UpdatesUserProfileInformation;

class UpdateUserProfileInformation implements UpdatesUserProfileInformation
{
    /**
     * Validate and update the given user's profile information.
     *
     * @param  array<string, mixed>  $input
     */
    public function update(User $user, array $input): void
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'username' => ['required', 'string', 'alpha_dash', 'min:3', 'max:25', Rule::unique('users')->ignore($user->id)],
            'email' => ['required', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'photo' => ['nullable', 'mimes:jpg,jpeg,png', 'max:1024'],
            'is_attorney' => ['boolean'],
            'zip_code' => ['required', 'string', 'min:5', 'max:10'],
        ];

        // Add bar card number validation if user is an attorney
        if (isset($input['is_attorney']) && $input['is_attorney']) {
            $rules['bar_card_number'] = ['required', 'string', 'min:4', 'max:20'];
        }

        Validator::make($input, $rules)->validateWithBag('updateProfileInformation');

        if (isset($input['photo'])) {
            $user->updateProfilePhoto($input['photo']);
        }

        if ($input['email'] !== $user->email &&
            $user instanceof MustVerifyEmail) {
            $this->updateVerifiedUser($user, $input);
        } else {
            $userData = [
                'name' => $input['name'],
                'username' => $input['username'],
                'email' => $input['email'],
                'is_attorney' => isset($input['is_attorney']) ? $input['is_attorney'] : false,
                'zip_code' => $input['zip_code'],
            ];

            // Update coordinates if provided
            if (isset($input['latitude']) && isset($input['longitude'])) {
                $userData['latitude'] = $input['latitude'];
                $userData['longitude'] = $input['longitude'];
            }

            // Add bar card number if user is an attorney
            if (isset($input['is_attorney']) && $input['is_attorney'] && isset($input['bar_card_number'])) {
                $userData['bar_card_number'] = $input['bar_card_number'];
            } elseif (!isset($input['is_attorney']) || !$input['is_attorney']) {
                $userData['bar_card_number'] = null; // Clear bar card number if not an attorney
            }

            $user->forceFill($userData)->save();
        }
    }

    /**
     * Update the given verified user's profile information.
     *
     * @param  array<string, string>  $input
     */
    protected function updateVerifiedUser(User $user, array $input): void
    {
        $userData = [
            'name' => $input['name'],
            'username' => $input['username'],
            'email' => $input['email'],
            'email_verified_at' => null,
            'is_attorney' => isset($input['is_attorney']) ? $input['is_attorney'] : false,
            'zip_code' => $input['zip_code'],
        ];

        // Update coordinates if provided
        if (isset($input['latitude']) && isset($input['longitude'])) {
            $userData['latitude'] = $input['latitude'];
            $userData['longitude'] = $input['longitude'];
        }

        // Add bar card number if user is an attorney
        if (isset($input['is_attorney']) && $input['is_attorney'] && isset($input['bar_card_number'])) {
            $userData['bar_card_number'] = $input['bar_card_number'];
        } elseif (!isset($input['is_attorney']) || !$input['is_attorney']) {
            $userData['bar_card_number'] = null; // Clear bar card number if not an attorney
        }

        $user->forceFill($userData)->save();

        $user->sendEmailVerificationNotification();
    }
}
