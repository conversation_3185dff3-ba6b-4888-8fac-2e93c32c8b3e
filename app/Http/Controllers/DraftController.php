<?php

namespace App\Http\Controllers;

use App\Models\Draft;
use App\Models\CaseFile;
use Illuminate\Http\Request;

class DraftController extends Controller
{
    public function index(CaseFile $caseFile)
    {
        $drafts = $caseFile->drafts()->latest()->get();

        return view('case-files.drafts.index', compact('caseFile', 'drafts'));
    }

    public function create(CaseFile $caseFile)
    {
        return view('case-files.drafts.create', compact('caseFile'));
    }

    public function store(Request $request, CaseFile $caseFile)
    {
        $validated = $request->validate([
            'draft_type' => 'required|string',
            'document_name' => 'required|string|max:255',
            'description' => 'required|string',
            'structured_context' => 'nullable|json',
            'strategy_id' => 'nullable|exists:case_strategies,id',
            'reference_draft_id' => 'nullable|exists:drafts,id',
        ]);

        // Create the draft
        $draft = $caseFile->drafts()->create($validated);

        // Initialize metadata array
        $metadata = $draft->metadata ?? [];

        // If a strategy was selected, store it in the draft's metadata
        if (!empty($validated['strategy_id'])) {
            $metadata['strategy_id'] = $validated['strategy_id'];
        }

        // If a reference draft was selected, store it in the draft's metadata
        if (!empty($request->reference_draft_id)) {
            $metadata['reference_draft_id'] = $request->reference_draft_id;
        }

        // Update the draft with the metadata if any changes were made
        if (!empty($metadata)) {
            $draft->update(['metadata' => $metadata]);
        }

        return redirect()->route('case-files.drafts.show', [
            'caseFile' => $caseFile,
            'draft' => $draft->id
        ])->with('success', 'Draft created successfully.');
    }

    public function show(CaseFile $caseFile, Draft $draft)
    {
        return view('case-files.drafts.details', compact('caseFile', 'draft'));
    }

    public function edit(CaseFile $caseFile, Draft $draft)
    {
        return view('case-files.drafts.edit', compact('caseFile', 'draft'));
    }

    public function update(Request $request, CaseFile $caseFile, Draft $draft)
    {
        $validated = $request->validate([
            'draft_type' => 'required|string',
            'document_name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'status' => 'nullable|string',
            'structured_context' => 'nullable|json',
            'strategy_id' => 'nullable|exists:case_strategies,id',
        ]);

        // Update the draft with the validated data
        $draft->update($validated);

        // Handle strategy_id separately since it goes into metadata
        $metadata = $draft->metadata ?? [];

        if (!empty($validated['strategy_id'])) {
            // Add or update strategy_id in metadata
            $metadata['strategy_id'] = $validated['strategy_id'];
        } else {
            // Remove strategy_id from metadata if it was set to empty
            if (isset($metadata['strategy_id'])) {
                unset($metadata['strategy_id']);
            }
        }

        // Update the metadata
        $draft->update(['metadata' => $metadata]);

        return redirect()->route('case-files.drafts.show', [$caseFile, $draft])->with('success', 'Draft updated successfully.');
    }

    public function destroy(CaseFile $caseFile, Draft $draft)
    {
        $draft->delete();

        return redirect()->route('case-files.drafts.index', $caseFile);
    }
}
