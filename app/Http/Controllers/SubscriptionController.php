<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use App\Services\CreditService;
use Lara<PERSON>\Cashier\Subscription;
use Stripe\StripeClient;

class SubscriptionController extends Controller
{
    /**
     * Display the subscription plans.
     */
    public function index()
    {
        $user = Auth::user();

        // Ensure the user is a Stripe customer before creating a setup intent
        if (!$user->stripe_id) {
            $user->createAsStripeCustomer();
        }

        return view('subscriptions.index', [
            'intent' => $user->createSetupIntent(),
        ]);
    }

    /**
     * Process a subscription.
     */
    protected $creditService;
    protected $stripe;

    public function __construct(CreditService $creditService)
    {
        $this->creditService = $creditService;
        $this->stripe = new StripeClient(config('cashier.secret'));
    }

    public function store(Request $request)
    {
        $request->validate([
            'plan' => 'required|string',
            'payment_method' => 'required|string',
        ]);

        $user = Auth::user();
        $paymentMethod = $request->payment_method;
        $planId = $request->plan;

        // Map plan names to Stripe price IDs
        if(config('app.env') === 'production'){
            $planMap = [
                'pro_se_basic_upfront' => 'price_1RRf3IBsbrUnlZyq6s7fS5t6',
                'pro_se_basic' => 'price_1RDSjpRwVr4O26ZHlYSBm1gB',
                'pro_se_standard' => 'price_1RDSulRwVr4O26ZHalkMPhUz',
                'pro_se_plus' => 'price_1RDSv0RwVr4O26ZHTKcXN457',
                'attorney_basic' => 'price_1RDT1zRwVr4O26ZHFvaptwJP',
                'attorney_pro' => 'price_1RDT2iRwVr4O26ZHULovYU7a',
                'attorney_enterprise' => 'price_1RDT3FRwVr4O26ZHpV8Qejaa',
            ];
        } else {
        $planMap = [
            'pro_se_basic_upfront' => 'price_1RRf3IBsbrUnlZyq6s7fS5t6',
            'pro_se_basic' => 'price_1RRfBPBsbrUnlZyqjajXvgxb',
            'pro_se_standard' => 'price_1RRfC8BsbrUnlZyqKcBZW1RZ',
            'pro_se_plus' => 'price_1RRfDUBsbrUnlZyqsExqkeKV',
            'attorney_basic' => 'price_1RY86eBsbrUnlZyqzWCEEcct',
            'attorney_pro' => 'price_1RDT2iRwVr4O26ZHULovYU7a',
            'attorney_enterprise' => 'price_1RDT3FRwVr4O26ZHpV8Qejaa',
        ];
        }

        if (!isset($planMap[$planId])) {
            return back()->withErrors(['plan' => __('subscription.invalid_plan')]);
        }

        $stripePriceId = $planMap[$planId];
        $plans = config('stripe.plans', []);
        $planName = $plans[$stripePriceId]['name'] ?? __('subscription.unknown_plan');
        $planCredits = $plans[$stripePriceId]['credits'] ?? 0;

        // Handle Pro Se Basic 3-month commitment special case
        if ($planId === 'pro_se_basic_upfront') {
            return $this->handleProSeBasicCommitment($user, $paymentMethod, $stripePriceId, $planName, $planCredits, $planMap);
        }

        try {
            // Ensure the user is a Stripe customer before proceeding
            if (!$user->stripe_id) {
                $user->createAsStripeCustomer();
            }

            // Add payment method
            $user->updateDefaultPaymentMethod($paymentMethod);

            // Create or update subscription
            if ($user->subscribed('default')) {
                // User is already subscribed, update their plan
                $user->subscription('default')->swap($stripePriceId);
                $message = __('subscription.subscription_updated', ['plan' => $planName]);

                // Add credits for the updated subscription
                if ($planCredits > 0) {
                    $this->creditService->addCredits(
                        $user,
                        $planCredits,
                        __('subscription.subscription_updated_credits'),
                        [
                            'plan_id' => $planId,
                            'stripe_price_id' => $stripePriceId
                        ]
                    );

                    Log::info('Added credits for updated subscription', [
                        'user_id' => $user->id,
                        'credits' => $planCredits,
                        'plan' => $planName
                    ]);
                }
            } else {
                // Create a new subscription
                $user->newSubscription('default', $stripePriceId)->create($paymentMethod);
                $message = __('subscription.subscription_created', ['plan' => $planName]);

                // Add credits for the new subscription
                if ($planCredits > 0) {
                    $this->creditService->addCredits(
                        $user,
                        $planCredits,
                        __('subscription.new_subscription'),
                        [
                            'plan_id' => $planId,
                            'stripe_price_id' => $stripePriceId
                        ]
                    );

                    Log::info('Added credits for new subscription', [
                        'user_id' => $user->id,
                        'credits' => $planCredits,
                        'plan' => $planName
                    ]);
                }
            }

            // Verify subscription status directly with Stripe as a fallback
            // This is now just for data consistency, not for adding credits
            $this->verifyAndUpdateSubscription($user, $planCredits);

            return redirect()->route('credits.index')->with('success', $message);
        } catch (\Exception $e) {
            Log::error('Subscription failed: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'plan_id' => $planId,
                'stripe_price_id' => $stripePriceId,
            ]);
            return back()->withErrors(['error' => __('subscription.subscription_failed', ['message' => $e->getMessage()])]);
        }
    }

    /**
     * Display the subscription management page.
     */
    public function show()
    {
        $user = Auth::user();

        // Verify subscription status with Stripe before showing details
        $this->verifySubscriptionStatus($user);

        if (!$user->subscribed('default')) {
            return redirect()->route('subscriptions.index')
                ->with('info', __('subscription.not_subscribed'));
        }

        return view('subscriptions.show', [
            'subscription' => $user->subscription('default'),
            'invoices' => $user->invoices(),
            'paymentMethod' => $user->defaultPaymentMethod(),
        ]);
    }

    /**
     * Cancel the user's subscription.
     */
    public function destroy()
    {
        $user = Auth::user();

        // Verify subscription status with Stripe before cancelling
        $this->verifySubscriptionStatus($user);

        if ($user->subscribed('default')) {
            $subscription = $user->subscription('default');
            if (!$subscription->canceled()) {
                $subscription->cancel();
                return back()->with('success', __('subscription.subscription_cancelled'));
            } else {
                return back()->with('info', __('subscription.already_cancelled'));
            }
        }

        return back()->with('info', __('subscription.not_subscribed'));
    }

    /**
     * Verify subscription status with Stripe and update local database if needed
     * This serves as a fallback in case webhooks fail
     *
     * @param \App\Models\User $user
     * @param int $planCredits
     * @return void
     */
    protected function verifyAndUpdateSubscription($user, $planCredits)
    {
        try {
            // Ensure the user is a Stripe customer
            if (!$user->stripe_id) {
                $user->createAsStripeCustomer();
                Log::info('Created Stripe customer for user during subscription verification', ['user_id' => $user->id]);
                // Since this is a new customer, they won't have any subscriptions yet
                return;
            }

            // Get the user's Stripe customer ID
            $stripeId = $user->stripe_id;

            // Get the customer's subscriptions directly from Stripe
            $stripeSubscriptions = $this->stripe->subscriptions->all([
                'customer' => $stripeId,
                'status' => 'active',
                'limit' => 1,
            ]);

            if (count($stripeSubscriptions->data) === 0) {
                Log::info('No active subscriptions found in Stripe for user', ['user_id' => $user->id]);
                return;
            }

            $stripeSubscription = $stripeSubscriptions->data[0];
            $stripePriceId = $stripeSubscription->items->data[0]->price->id;

            // Check if the subscription exists in our database
            $localSubscription = Subscription::where('user_id', $user->id)
                ->where('stripe_id', $stripeSubscription->id)
                ->first();

            if (!$localSubscription) {
                // Subscription exists in Stripe but not in our database, create it
                Log::info('Creating local subscription record from Stripe data', [
                    'user_id' => $user->id,
                    'stripe_subscription_id' => $stripeSubscription->id,
                ]);

                $localSubscription = new Subscription();
                $localSubscription->user_id = $user->id;
                $localSubscription->name = 'default';
                $localSubscription->stripe_id = $stripeSubscription->id;
                $localSubscription->stripe_status = $stripeSubscription->status;
                $localSubscription->stripe_price = $stripePriceId;
                $localSubscription->quantity = 1;
                $localSubscription->save();

                // Add credits for the new subscription
                if ($planCredits > 0) {
                    $this->creditService->addCredits(
                        $user,
                        $planCredits,
                        __('subscription.subscription_created_credits'),
                        "Credits from {$stripePriceId} subscription"
                    );

                    Log::info('Added subscription credits', [
                        'user_id' => $user->id,
                        'credits' => $planCredits,
                        'stripe_price_id' => $stripePriceId,
                    ]);
                }
            } else if ($localSubscription->stripe_status !== $stripeSubscription->status) {
                // Update the status if it's different
                Log::info('Updating local subscription status from Stripe data', [
                    'user_id' => $user->id,
                    'stripe_subscription_id' => $stripeSubscription->id,
                    'old_status' => $localSubscription->stripe_status,
                    'new_status' => $stripeSubscription->status,
                ]);

                $localSubscription->stripe_status = $stripeSubscription->status;
                $localSubscription->save();
            }
        } catch (\Exception $e) {
            Log::error('Error verifying subscription with Stripe: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'exception' => $e,
            ]);
        }
    }

    /**
     * Resume a cancelled subscription.
     */
    public function resume()
    {
        $user = Auth::user();

        // Verify subscription status with Stripe before resuming
        $this->verifySubscriptionStatus($user);

        if ($user->subscription('default')->canceled()) {
            $user->subscription('default')->resume();
            return back()->with('success', __('subscription.subscription_resumed'));
        }

        return back()->with('info', __('subscription.not_cancelled'));
    }

    /**
     * Verify subscription status with Stripe
     * This is used when viewing subscription details to ensure the data is up-to-date
     *
     * @param \App\Models\User $user
     * @return void
     */
    protected function verifySubscriptionStatus($user)
    {
        try {
            // Ensure the user is a Stripe customer
            if (!$user->stripe_id) {
                $user->createAsStripeCustomer();
                Log::info('Created Stripe customer for user during subscription status verification', ['user_id' => $user->id]);
                // Since this is a new customer, they won't have any subscriptions yet
                return;
            }

            // Get the user's Stripe customer ID
            $stripeId = $user->stripe_id;

            // Get all subscriptions from our database for this user
            $localSubscriptions = Subscription::where('user_id', $user->id)->get();

            if ($localSubscriptions->isEmpty()) {
                // No local subscriptions, check if there are any in Stripe
                $stripeSubscriptions = $this->stripe->subscriptions->all([
                    'customer' => $stripeId,
                    'status' => 'active',
                ]);

                if (count($stripeSubscriptions->data) > 0) {
                    // Found active subscriptions in Stripe but none in our database
                    // For each active subscription in Stripe, create a local record
                    foreach ($stripeSubscriptions->data as $stripeSubscription) {
                        $stripePriceId = $stripeSubscription->items->data[0]->price->id;
                        $plans = config('stripe.plans', []);
                        $planCredits = $plans[$stripePriceId]['credits'] ?? 0;

                        // Create local subscription record
                        $localSubscription = new Subscription();
                        $localSubscription->user_id = $user->id;
                        $localSubscription->name = 'default';
                        $localSubscription->stripe_id = $stripeSubscription->id;
                        $localSubscription->stripe_status = $stripeSubscription->status;
                        $localSubscription->stripe_price = $stripePriceId;
                        $localSubscription->quantity = 1;
                        $localSubscription->save();

                        Log::info('Created local subscription record from Stripe data during status check', [
                            'user_id' => $user->id,
                            'stripe_subscription_id' => $stripeSubscription->id,
                        ]);

                        // Add credits for the subscription if applicable
                        if ($planCredits > 0) {
                            $this->creditService->addCredits(
                                $user,
                                $planCredits,
                                __('subscription.subscription_verified_credits'),
                                "Credits from {$stripePriceId} subscription (verified)"
                            );
                        }
                    }
                }
            } else {
                // We have local subscriptions, verify their status with Stripe
                foreach ($localSubscriptions as $localSubscription) {
                    try {
                        // Get the subscription from Stripe
                        $stripeSubscription = $this->stripe->subscriptions->retrieve($localSubscription->stripe_id);

                        // Update local status if different
                        if ($localSubscription->stripe_status !== $stripeSubscription->status) {
                            Log::info('Updating subscription status during verification', [
                                'user_id' => $user->id,
                                'stripe_subscription_id' => $stripeSubscription->id,
                                'old_status' => $localSubscription->stripe_status,
                                'new_status' => $stripeSubscription->status,
                            ]);

                            $localSubscription->stripe_status = $stripeSubscription->status;
                            $localSubscription->save();
                        }
                    } catch (\Exception $e) {
                        // If the subscription doesn't exist in Stripe anymore, mark it as cancelled
                        if (strpos($e->getMessage(), 'No such subscription') !== false) {
                            Log::warning('Subscription exists locally but not in Stripe, marking as cancelled', [
                                'user_id' => $user->id,
                                'stripe_subscription_id' => $localSubscription->stripe_id,
                            ]);

                            if ($localSubscription->stripe_status !== 'canceled') {
                                $localSubscription->stripe_status = 'canceled';
                                $localSubscription->ends_at = now();
                                $localSubscription->save();
                            }
                        } else {
                            Log::error('Error retrieving subscription from Stripe: ' . $e->getMessage(), [
                                'user_id' => $user->id,
                                'stripe_subscription_id' => $localSubscription->stripe_id,
                            ]);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            Log::error('Error verifying subscription status with Stripe: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'exception' => $e,
            ]);
        }
    }

    /**
     * Handle Pro Se Basic 3-month commitment subscription
     */
    protected function handleProSeBasicCommitment($user, $paymentMethod, $upfrontPriceId, $planName, $planCredits, $planMap)
    {
        try {
            // Ensure the user is a Stripe customer before proceeding
            if (!$user->stripe_id) {
                $user->createAsStripeCustomer();
            }

            // Add payment method
            $user->updateDefaultPaymentMethod($paymentMethod);

            // Step 1: Create one-time payment for $57 upfront
            $paymentIntent = $this->stripe->paymentIntents->create([
                'amount' => 5700, // $57.00 in cents
                'currency' => 'usd',
                'customer' => $user->stripe_id,
                'payment_method' => $paymentMethod,
                'confirm' => true,
                'return_url' => route('credits.index'),
                'metadata' => [
                    'user_id' => $user->id,
                    'plan_type' => 'pro_se_basic_commitment',
                    'commitment_months' => 3,
                ],
            ]);

            if ($paymentIntent->status !== 'succeeded') {
                throw new \Exception('Payment failed: ' . $paymentIntent->status);
            }

            // Step 2: Create subscription with trial period (but user already paid, so it's not really a trial)
            $monthlyPriceId = $planMap['pro_se_basic']; // Get the regular monthly price
            $trialEnd = now()->addMonths(3);

            if ($user->subscribed('default')) {
                // User is already subscribed, cancel existing and create new
                $existingSubscription = $user->subscription('default');
                if (!$existingSubscription->canceled()) {
                    $existingSubscription->cancelNow(); // Cancel immediately instead of at period end
                }
            }

            // Create subscription with trial period (user already paid upfront, so this is just delayed billing)
            $subscription = $user->newSubscription('default', $monthlyPriceId)
                ->trialUntil($trialEnd)
                ->create($paymentMethod);

            // Add metadata to track this as a commitment subscription
            $this->stripe->subscriptions->update($subscription->stripe_id, [
                'metadata' => [
                    'commitment_type' => 'pro_se_basic_3_month',
                    'upfront_payment_intent' => $paymentIntent->id,
                    'commitment_start' => now()->toDateString(),
                    'commitment_end' => now()->addMonths(3)->toDateString(),
                    'upfront_paid' => 'true', // Mark that upfront payment was made
                ],
            ]);

            // Update local subscription record with commitment information
            // Refresh the user to get the latest subscription data
            $user->refresh();
            $localSubscription = $user->subscription('default');

            if ($localSubscription) {
                $localSubscription->update([
                    'commitment_type' => 'pro_se_basic_3_month',
                    'commitment_start' => now(),
                    'commitment_end' => now()->addMonths(3),
                    'upfront_payment_intent_id' => $paymentIntent->id,
                ]);
            }

            // Add initial credits for the 3-month commitment
            $this->creditService->addCredits(
                $user,
                $planCredits,
                'Pro Se Basic 3-month commitment - Initial credits',
                [
                    'plan_type' => 'pro_se_basic_commitment',
                    'payment_intent_id' => $paymentIntent->id,
                    'subscription_id' => $subscription->id,
                ]
            );

            Log::info('Pro Se Basic commitment subscription created', [
                'user_id' => $user->id,
                'payment_intent_id' => $paymentIntent->id,
                'subscription_id' => $subscription->stripe_id,
                'trial_end' => $trialEnd->toDateTimeString(),
            ]);

            return redirect()->route('credits.index')->with('success',
                'Welcome to Pro Se Basic! Your 3-month commitment is active. Monthly billing of $19 will begin automatically in 3 months.'
            );

        } catch (\Exception $e) {
            Log::error('Pro Se Basic commitment subscription failed: ' . $e->getMessage(), [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);
            return back()->withErrors(['error' => 'Subscription failed: ' . $e->getMessage()]);
        }
    }

    /**
     * Update the payment method.
     */
    public function updatePaymentMethod(Request $request)
    {
        $request->validate([
            'payment_method' => 'required|string',
        ]);

        $user = Auth::user();
        $paymentMethod = $request->payment_method;

        // Verify subscription status with Stripe before updating payment method
        $this->verifySubscriptionStatus($user);

        try {
            // Ensure the user is a Stripe customer before proceeding
            if (!$user->stripe_id) {
                $user->createAsStripeCustomer();
            }

            $user->updateDefaultPaymentMethod($paymentMethod);
            return back()->with('success', __('subscription.payment_method_updated'));
        } catch (\Exception $e) {
            Log::error('Failed to update payment method: ' . $e->getMessage(), [
                'user_id' => $user->id,
            ]);
            return back()->withErrors(['error' => __('subscription.payment_update_failed', ['message' => $e->getMessage()])]);
        }
    }
}
