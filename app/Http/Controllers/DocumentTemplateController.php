<?php

namespace App\Http\Controllers;

use App\Models\DocumentTemplate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DocumentTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $templates = DocumentTemplate::orderBy('name')->get();
        return view('document-templates.index', compact('templates'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $documentTypes = DocumentTemplate::getDocumentTypes();
        return view('document-templates.create', compact('documentTypes'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'document_type' => 'required|string|max:255',
            'description' => 'nullable|string',
            'structure' => 'required|json',
            'default_content' => 'nullable|json',
            'ai_prompts' => 'nullable|json',
            'metadata' => 'nullable|json',
            'is_active' => 'boolean'
        ]);

        // Add the current user as creator
        $validated['created_by'] = Auth::id();

        $template = DocumentTemplate::create($validated);

        return redirect()->route('document-templates.show', $template)
            ->with('success', 'Template created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(DocumentTemplate $documentTemplate)
    {
        return view('document-templates.show', ['template' => $documentTemplate]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DocumentTemplate $documentTemplate)
    {
        $documentTypes = DocumentTemplate::getDocumentTypes();
        return view('document-templates.edit', [
            'template' => $documentTemplate,
            'documentTypes' => $documentTypes
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, DocumentTemplate $documentTemplate)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'document_type' => 'required|string|max:255',
            'description' => 'nullable|string',
            'structure' => 'required|json',
            'default_content' => 'nullable|json',
            'ai_prompts' => 'nullable|json',
            'metadata' => 'nullable|json',
            'is_active' => 'boolean'
        ]);

        $documentTemplate->update($validated);

        return redirect()->route('document-templates.show', $documentTemplate)
            ->with('success', 'Template updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DocumentTemplate $documentTemplate)
    {
        // Check if template is in use by any drafts
        if ($documentTemplate->drafts()->count() > 0) {
            return redirect()->route('document-templates.index')
                ->with('error', 'Cannot delete template that is in use by drafts.');
        }

        $documentTemplate->delete();

        return redirect()->route('document-templates.index')
            ->with('success', 'Template deleted successfully.');
    }

    /**
     * Use a template to create a new draft.
     */
    public function createDraft(Request $request, DocumentTemplate $documentTemplate)
    {
        $validated = $request->validate([
            'case_file_id' => 'required|exists:case_files,id',
            'document_name' => 'nullable|string|max:255',
            'description' => 'nullable|string',
            'strategy_id' => 'nullable|exists:case_strategies,id',
            'reference_draft_id' => 'nullable|exists:drafts,id'
        ]);

        // Prepare metadata
        $metadata = $documentTemplate->metadata ?? [];

        // If a strategy was selected, add it to the metadata
        if (!empty($validated['strategy_id'])) {
            $metadata['strategy_id'] = $validated['strategy_id'];
        }

        // If a reference draft was selected, add it to the metadata
        if (!empty($request->reference_draft_id)) {
            $metadata['reference_draft_id'] = $request->reference_draft_id;
        }

        // Create a new draft using the template
        $draft = new \App\Models\Draft([
            'case_file_id' => $validated['case_file_id'],
            'template_id' => $documentTemplate->id,
            'draft_type' => $documentTemplate->document_type,
            'document_name' => $validated['document_name'] ?? $documentTemplate->name,
            'description' => $validated['description'] ?? $documentTemplate->name,
            'status' => 'draft',
            'version' => 1,
            'last_edited_by' => Auth::id(),
            'structured_context' => ['content' => ''],
            'sections_structure' => $documentTemplate->structure,
            'ai_generation_prompts' => $documentTemplate->ai_prompts,
            'metadata' => $metadata
        ]);

        $draft->save();

        return redirect()->route('case-files.drafts.show', [
            'case_file' => $validated['case_file_id'],
            'draft' => $draft->id
        ])->with('success', 'Draft created from template successfully.');
    }
}
