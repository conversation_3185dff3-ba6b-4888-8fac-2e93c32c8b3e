<?php

namespace App\Jobs;

use App\Models\Document;
use App\Models\LegalResearchItem;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use OpenAI\Laravel\Facades\OpenAI;

class ProcessResearchReportJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $researchItem;
    protected $openAIClient;

    /**
     * Create a new job instance.
     */
    public function __construct(LegalResearchItem $researchItem)
    {
        $this->researchItem = $researchItem;
        $caseFile = $researchItem->caseFile;

    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        // Skip if no markdown content
        if (!$this->researchItem->research_markdown_content) {
            Log::warning('No markdown content to process', [
                'research_item_id' => $this->researchItem->id
            ]);
            return;
        }

        // Skip if already processed and has a document
        if ($this->researchItem->document_id) {
            Log::info('Research report already processed', [
                'research_item_id' => $this->researchItem->id,
                'document_id' => $this->researchItem->document_id
            ]);
            return;
        }

        $markdownFilePath = null;
        $s3Path = null;

        try {
            // Get the markdown content (already translated if needed)
            $markdownContent = $this->researchItem->research_markdown_content;

            // Ensure temp directory exists
            $tempDir = storage_path('app/temp');
            if (!file_exists($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            // Create temporary file
            $markdownFilePath = storage_path('app/temp/' . uniqid() . '.md');
            file_put_contents($markdownFilePath, $markdownContent);

            // Generate a filename for S3
            $filename = 'research-report-' . $this->researchItem->id . '.md';
            $s3Path = 'research-reports/' . $this->researchItem->case_file_id . '/' . $filename;

            // Upload to S3
            Log::info('Uploading research report to S3', [
                'research_item_id' => $this->researchItem->id,
                'file_size' => filesize($markdownFilePath),
                's3_path' => $s3Path
            ]);

            Storage::disk('s3')->put($s3Path, file_get_contents($markdownFilePath));

            // Create a new Document record
            $document = Document::create([
                'case_file_id' => $this->researchItem->case_file_id,
                'storage_path' => $s3Path,
                'original_filename' => $filename,
                'mime_type' => 'text/markdown',
                'file_size' => filesize($markdownFilePath),
                'title' => $this->researchItem->title,
                'description' => $this->researchItem->description,
                'ingestion_status' => Document::STATUS_PENDING,
                'document_type' => 'research_report'
            ]);

            // Update the research item with the document ID
            $this->researchItem->update(['document_id' => $document->id]);

            Log::info('Successfully created document for research report', [
                'research_item_id' => $this->researchItem->id,
                'document_id' => $document->id
            ]);

            // Dispatch the ProcessExhibitJob to handle the document
            ProcessExhibitJob::dispatch($document);

        } catch (\Exception $e) {
            Log::error('Failed to process research report', [
                'research_item_id' => $this->researchItem->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        } finally {
            // Clean up temporary file
            if ($markdownFilePath && file_exists($markdownFilePath)) {
                unlink($markdownFilePath);
            }
        }
    }
}
