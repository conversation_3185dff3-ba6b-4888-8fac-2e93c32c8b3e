<?php

namespace App\Jobs;

use App\Models\Draft;
use App\Models\Document;
use App\Models\User;
use App\Notifications\DocumentGenerationCompleted;
use App\Services\EnhancedDocumentGenerationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class GenerateDocumentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The draft to generate a document from
     */
    protected $draft;

    /**
     * The options for document generation
     */
    protected $options;

    /**
     * The exhibit document IDs to include
     */
    protected $exhibitIds;

    /**
     * The user who initiated the document generation
     */
    protected $userId;

    /**
     * Optional fields for document creation
     */
    protected $optionalFields;

    /**
     * Create a new job instance.
     *
     * @param Draft $draft The draft to generate a document from
     * @param array $options The options for document generation
     * @param array $exhibitIds The exhibit document IDs to include
     * @param int $userId The ID of the user who initiated the document generation
     * @param array $optionalFields Optional fields for document creation
     */
    public function __construct(Draft $draft, array $options, array $exhibitIds, int $userId, array $optionalFields = [])
    {
        $this->draft = $draft;
        $this->options = $options;
        $this->exhibitIds = $exhibitIds;
        $this->userId = $userId;
        $this->optionalFields = $optionalFields;
    }

    /**
     * Execute the job.
     */
    public function handle(EnhancedDocumentGenerationService $documentGenerationService)
    {
        try {
            // Generate the document with ?exhibits
            Log::info('Starting document generation job', [
                'draft_id' => $this->draft->id,
                'exhibit_count' => count($this->exhibitIds),
                'user_id' => $this->userId
            ]);

            $filePath = $documentGenerationService->generateDocument($this->draft, $this->options, $this->exhibitIds);

            // Determine the mime type based on the file extension
            $mimeType = str_ends_with($filePath, '.pdf') ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
            $extension = str_ends_with($filePath, '.pdf') ? '.pdf' : '.docx';

            // Create a document record for the generated document
            $documentData = [
                'case_file_id' => $this->draft->case_file_id,
                'title' => $this->draft->document_name ?? "{$this->draft->description}",
                'description' => "Generated from {$this->draft->draft_type} draft",
                'mime_type' => $mimeType,
                'original_filename' => str_replace(' ', '_', $this->draft->document_name ?? "{$this->draft->draft_type}_Document") . $extension,
                'storage_path' => $filePath, // Store without 'public/' prefix
                'file_size' => file_exists(storage_path("app/public/{$filePath}")) ? filesize(storage_path("app/public/{$filePath}")) : 0,
                'ingestion_status' => 'indexed', // Mark as indexed since it's a generated document
                'created_by' => $this->userId,
            ];

            // Try to create the document with all fields
            try {
                $document = Document::create(array_merge($documentData, $this->optionalFields));
            } catch (\Exception $e) {
                // If that fails, try with just the required fields
                Log::warning('Error creating document with all fields. Trying with required fields only.', [
                    'error' => $e->getMessage()
                ]);
                $document = Document::create($documentData);
            }

            // Update the draft with the document ID
            $this->draft->document_id = $document->id;
            $this->draft->save();

            // Notify the user that the document has been generated
            $user = User::find($this->userId);
            if ($user) {
                $user->notify(new DocumentGenerationCompleted($document, $this->draft));
            }

            Log::info('Document generation completed successfully', [
                'document_id' => $document->id,
                'draft_id' => $this->draft->id,
                'user_id' => $this->userId
            ]);
        } catch (\Exception $e) {
            Log::error('Error in document generation job', [
                'draft_id' => $this->draft->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // Notify the user about the failure
            $user = User::find($this->userId);
            if ($user) {
                Notification::send($user, new DocumentGenerationCompleted(null, $this->draft, $e->getMessage()));
            }
        }
    }
}
