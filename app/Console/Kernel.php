<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Run the vector store refresh command daily to prevent expiration
        $schedule->command('openai:refresh-vector-stores')->daily();

        // Check the status of pending research questions every 5 minutes
        $schedule->command('research:check-status')->everyFiveMinutes();

        // OpenAI Capacity Monitoring
        $schedule->command('openai:monitor-storage --alert')
                 ->hourly()
                 ->withoutOverlapping()
                 ->runInBackground();

        // Daily capacity planning report
        $schedule->command('openai:capacity-planning --alert')
                 ->dailyAt('09:00')
                 ->withoutOverlapping();

        // Weekly detailed capacity analysis
        $schedule->command('openai:capacity-planning --forecast-days=60 --alert')
                 ->weeklyOn(1, '09:00') // Monday at 9 AM
                 ->withoutOverlapping();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
