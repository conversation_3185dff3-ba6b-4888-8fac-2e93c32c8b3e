<?php

namespace App\Console\Commands;

use App\Models\OpenAiProject;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class OpenAiCapacityPlanning extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'openai:capacity-planning 
                            {--forecast-days=30 : Days to forecast ahead}
                            {--alert : Send alerts if capacity issues detected}';

    /**
     * The console command description.
     */
    protected $description = 'Analyze current capacity and forecast when new projects will be needed';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $forecastDays = (int) $this->option('forecast-days');
        $sendAlerts = $this->option('alert');

        $this->info("🔮 OpenAI Capacity Planning & Forecasting ({$forecastDays} days)");
        $this->newLine();

        // Current capacity analysis
        $this->analyzeCurrentCapacity();
        
        // Growth analysis
        $this->analyzeGrowthTrends($forecastDays);
        
        // Recommendations
        $recommendations = $this->generateRecommendations($forecastDays);
        
        if ($sendAlerts && !empty($recommendations['alerts'])) {
            $this->sendCapacityAlerts($recommendations);
        }

        return 0;
    }

    /**
     * Analyze current capacity across all projects.
     */
    private function analyzeCurrentCapacity(): void
    {
        $this->line('📊 Current Capacity Analysis:');
        $this->newLine();

        $projects = OpenAiProject::where('is_active', true)->with('capacity')->get();
        
        $totalCapacity = [
            'projects' => $projects->count(),
            'total_gb' => $projects->count() * 100,
            'allocated_gb' => 0,
            'used_gb' => 0,
            'available_gb' => 0,
            'users' => ['basic' => 0, 'standard' => 0, 'pro' => 0],
            'accepting_new' => 0
        ];

        foreach ($projects as $project) {
            $storageInfo = $project->getStorageInfo();
            $capacity = $project->capacity;
            
            $totalCapacity['used_gb'] += $storageInfo['used_gb'];
            
            if ($capacity) {
                $capacityInfo = $capacity->getCapacityInfo();
                $totalCapacity['allocated_gb'] += $capacityInfo['allocated_gb'];
                $totalCapacity['available_gb'] += $capacityInfo['available_gb'];
                $totalCapacity['users']['basic'] += $capacityInfo['basic_users'];
                $totalCapacity['users']['standard'] += $capacityInfo['standard_users'];
                $totalCapacity['users']['pro'] += $capacityInfo['pro_users'];
                
                if ($capacityInfo['accepts_new_users']) {
                    $totalCapacity['accepting_new']++;
                }
            }
        }

        // Display summary
        $this->table(
            ['Metric', 'Value'],
            [
                ['Total Projects', $totalCapacity['projects']],
                ['Projects Accepting New Users', $totalCapacity['accepting_new']],
                ['Total Capacity', $totalCapacity['total_gb'] . ' GB'],
                ['Allocated Storage', $totalCapacity['allocated_gb'] . ' GB'],
                ['Used Storage', $totalCapacity['used_gb'] . ' GB'],
                ['Available Storage', $totalCapacity['available_gb'] . ' GB'],
                ['Basic Users', $totalCapacity['users']['basic']],
                ['Standard Users', $totalCapacity['users']['standard']],
                ['Pro Users', $totalCapacity['users']['pro']],
                ['Total Users', array_sum($totalCapacity['users'])],
            ]
        );

        // Capacity for new users
        $this->newLine();
        $this->line('🎯 New User Capacity:');
        
        $availableBytes = $totalCapacity['available_gb'] * 1024 * 1024 * 1024;
        $newBasicUsers = floor($availableBytes / (1 * 1024 * 1024 * 1024));
        $newStandardUsers = floor($availableBytes / (5 * 1024 * 1024 * 1024));
        $newProUsers = floor($availableBytes / (10 * 1024 * 1024 * 1024));

        $this->line("Can accommodate:");
        $this->line("  • {$newBasicUsers} new Basic users (1GB each)");
        $this->line("  • {$newStandardUsers} new Standard users (5GB each)");
        $this->line("  • {$newProUsers} new Pro users (10GB each)");
    }

    /**
     * Analyze growth trends to forecast capacity needs.
     */
    private function analyzeGrowthTrends(int $forecastDays): void
    {
        $this->newLine();
        $this->line('📈 Growth Trend Analysis:');
        $this->newLine();

        // Analyze user growth over last 30 days
        $recentUsers = User::where('created_at', '>=', now()->subDays(30))->count();
        $dailyGrowthRate = $recentUsers / 30;

        // Analyze storage growth
        $projects = OpenAiProject::where('is_active', true)->get();
        $currentUsedStorage = $projects->sum('storage_used');
        
        // Simple growth projection (you could make this more sophisticated)
        $projectedNewUsers = $dailyGrowthRate * $forecastDays;
        $usersWithStorage = User::whereHas('storageAllocation')->count();
        $avgStoragePerUser = ($currentUsedStorage > 0 && $usersWithStorage > 0) ?
            $currentUsedStorage / $usersWithStorage :
            2 * 1024 * 1024 * 1024; // 2GB default estimate

        $projectedStorageGrowth = $projectedNewUsers * $avgStoragePerUser;
        $projectedStorageGB = round($projectedStorageGrowth / (1024 * 1024 * 1024), 1);

        $this->table(
            ['Metric', 'Current', 'Projected (' . $forecastDays . ' days)'],
            [
                ['New Users (last 30 days)', $recentUsers, round($projectedNewUsers)],
                ['Daily Growth Rate', round($dailyGrowthRate, 1) . ' users/day', '-'],
                ['Storage Growth', '-', $projectedStorageGB . ' GB'],
                ['Avg Storage/User', round($avgStoragePerUser / (1024 * 1024 * 1024), 2) . ' GB', '-'],
            ]
        );
    }

    /**
     * Generate recommendations based on analysis.
     */
    private function generateRecommendations(int $forecastDays): array
    {
        $this->newLine();
        $this->line('💡 Recommendations:');
        $this->newLine();

        $projects = OpenAiProject::where('is_active', true)->with('capacity')->get();
        $acceptingProjects = $projects->filter(fn($p) => $p->capacity && $p->capacity->accepts_new_users);
        
        $recommendations = [
            'alerts' => [],
            'actions' => []
        ];

        // Critical: No projects accepting users
        if ($acceptingProjects->count() === 0) {
            $message = '🚨 CRITICAL: Add new OpenAI project IMMEDIATELY - no capacity for new users!';
            $this->error($message);
            $recommendations['alerts'][] = [
                'level' => 'critical',
                'message' => $message,
                'action' => 'add_project_immediately'
            ];
        }
        // Warning: Only one project accepting users
        elseif ($acceptingProjects->count() === 1) {
            $message = '⚠️  WARNING: Only 1 project accepting users - add new project within 1 week';
            $this->warn($message);
            $recommendations['alerts'][] = [
                'level' => 'warning',
                'message' => $message,
                'action' => 'add_project_soon'
            ];
        }
        // Low capacity warning
        elseif ($acceptingProjects->count() <= 2) {
            $message = '📋 NOTICE: Low project capacity - consider adding project within 2 weeks';
            $this->line($message);
            $recommendations['alerts'][] = [
                'level' => 'notice',
                'message' => $message,
                'action' => 'plan_new_project'
            ];
        }

        // Storage-based recommendations
        $totalAvailableGB = $acceptingProjects->sum(fn($p) => $p->capacity->getAvailableCapacity()) / (1024 * 1024 * 1024);
        
        if ($totalAvailableGB < 50) {
            $message = "⚠️  Low available storage: {$totalAvailableGB}GB remaining across all projects";
            $this->warn($message);
            $recommendations['alerts'][] = [
                'level' => 'warning',
                'message' => $message,
                'action' => 'monitor_storage_closely'
            ];
        }

        // Specific actions
        $this->line('📋 Recommended Actions:');
        $this->line('1. Monitor capacity daily with: php artisan openai:monitor-storage --alert');
        $this->line('2. Set up automated alerts (see scheduling section)');
        $this->line('3. Prepare OpenAI project creation process');
        
        if ($acceptingProjects->count() <= 2) {
            $this->line('4. 🎯 CREATE NEW OPENAI PROJECT - capacity is low!');
        }

        return $recommendations;
    }

    /**
     * Send capacity alerts to monitoring systems.
     */
    private function sendCapacityAlerts(array $recommendations): void
    {
        foreach ($recommendations['alerts'] as $alert) {
            Log::log($alert['level'], 'OpenAI capacity planning alert', [
                'message' => $alert['message'],
                'recommended_action' => $alert['action'],
                'alert_type' => 'capacity_planning',
                'forecast_date' => now()->toDateString(),
                'timestamp' => now()->toISOString()
            ]);
        }

        $this->info('📧 Capacity alerts have been logged for monitoring systems.');
    }
}
