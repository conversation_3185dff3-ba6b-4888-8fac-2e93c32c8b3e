<?php

namespace App\Console\Commands;

use App\Models\OpenAiProject;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;

class ManageOpenAiProjects extends Command
{
    protected $signature = 'openai:projects {action=sync : Action to perform (sync/list)}';
    protected $description = 'Manage OpenAI projects';

    public function handle()
    {
        $action = $this->argument('action');

        if ($action === 'list') {
            $this->listProjects();
            return;
        }

        if ($action === 'sync') {
            $this->syncProjects();
            return;
        }

        $this->error("Unknown action: {$action}");
    }

    private function listProjects()
    {
        $projects = OpenAiProject::all();

        if ($projects->isEmpty()) {
            $this->warn('No OpenAI projects found.');
            return;
        }

        $this->table(
            ['Name', 'API Key', 'Organization ID', 'Active', 'Storage Used (GB)', 'Percentage', 'Cases'],
            $projects->map(function($p) {
                $storageInfo = $p->getStorageInfo();
                $caseCount = $p->caseFiles()->count();

                return [
                    $p->name,
                    substr($p->api_key, 0, 7) . '...',
                    $p->organization_id ?: 'N/A',
                    $p->is_active ? '✅ Yes' : '❌ No',
                    $storageInfo['used_gb'],
                    number_format($storageInfo['percentage'], 1) . '%',
                    $caseCount
                ];
            })
        );

        // Show summary
        $activeProjects = $projects->where('is_active', true);
        $nearCapacity = $activeProjects->filter(fn($p) => $p->isNearingCapacity());

        $this->newLine();
        $this->info("Summary: {$projects->count()} total projects, {$activeProjects->count()} active");

        if ($nearCapacity->count() > 0) {
            $this->warn("⚠️  {$nearCapacity->count()} project(s) nearing capacity (>90%)");
        }
    }

    private function syncProjects()
    {
        $this->call('db:seed', [
            '--class' => 'OpenAiProjectSeeder'
        ]);
    }
}