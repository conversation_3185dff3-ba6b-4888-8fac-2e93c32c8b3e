<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class TestLegalResearchCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:legal-research
                            {question? : The legal research question to ask}
                            {--jurisdiction= : Optional jurisdiction for the question}
                            {--case-id= : Optional case ID for context}
                            {--api-url= : API URL (defaults to https://research.justicequest.pro/api/legal-research)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the legal research API by sending a question and displaying the response';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get the API key from environment
        $apiKey = env('LEGAL_RESEARCH_API_KEY');

        if (!$apiKey) {
            $this->error('LEGAL_RESEARCH_API_KEY is not set in your .env file');
            return 1;
        }

        // Get the question from argument or prompt
        $question = $this->argument('question');
        if (!$question) {
            $question = $this->ask('What legal question would you like to research?');
        }

        // Get optional parameters
        $jurisdiction = $this->option('jurisdiction');
        $caseId = $this->option('case-id');

        // Prepare the API URL
        $apiUrl = $this->option('api-url') ?: 'https://research.justicequest.pro/api/legal-research';

        // Prepare the request payload
        $payload = [
            'research_question' => $question,
            'case_id' => $caseId ?: 'test-case-' . time(),
            'research_item_id' => 'test-item-' . time(),
        ];

        if ($jurisdiction) {
            $payload['jurisdiction'] = $jurisdiction;
        }

        // Display what we're about to do
        $this->info('Sending legal research query:');
        $this->line('Research Question: ' . $question);
        if ($jurisdiction) {
            $this->line('Jurisdiction: ' . $jurisdiction);
        }

        $this->line('Case ID: ' . $payload['case_id']);
        $this->line('Research Item ID: ' . $payload['research_item_id']);
        $this->line('API URL: ' . $apiUrl);
        $this->newLine();

        // Show a spinner while waiting for the response
        $this->output->write('Researching');
        $spinner = ['⠋', '⠙', '⠹', '⠸', '⠼', '⠴', '⠦', '⠧', '⠇', '⠏'];
        $i = 0;

        // Start a background process to show the spinner
        $spinnerActive = true;
        $spinnerThread = function() use (&$spinnerActive, $spinner, &$i) {
            while ($spinnerActive) {
                $this->output->write("\r" . 'Researching ' . $spinner[$i % count($spinner)]);
                usleep(100000); // 100ms
                $i++;
            }
            $this->output->write("\r"); // Clear the spinner line
        };

        // Make the API request
        try {
            $startTime = microtime(true);

            $response = Http::withHeaders([
                'X-API-Key' => $apiKey,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->post($apiUrl, $payload);

            $spinnerActive = false;
            $endTime = microtime(true);
            $duration = round($endTime - $startTime, 2);

            $this->newLine(2);
            $this->info('Response received in ' . $duration . ' seconds:');
            $this->newLine();

            if ($response->successful()) {
                $data = $response->json();

                // Pretty print the JSON response
                $this->line(json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));

                // Display research status information in a more readable format
                $this->newLine();
                $this->info('Research Status:');

                if (isset($data['status'])) {
                    $this->line('<fg=yellow>Status:</> ' . strtoupper($data['status']));
                }

                if (isset($data['message'])) {
                    $this->line('<fg=yellow>Message:</> ' . $data['message']);
                }

                if (isset($data['research_id'])) {
                    $this->line('<fg=yellow>Research ID:</> ' . $data['research_id']);
                }

                if (isset($data['public_report_url'])) {
                    $this->newLine();
                    $this->line('<fg=green>Report URL:</> ' . $data['public_report_url']);
                }

                if (isset($data['error']) && $data['error']) {
                    $this->newLine();
                    $this->line('<fg=red>Error:</> ' . $data['error']);
                }

                return 0;
            } else {
                $this->error('Error: ' . $response->status());
                $this->line($response->body());
                return 1;
            }
        } catch (\Exception $e) {
            $spinnerActive = false;
            $this->newLine(2);
            $this->error('Exception: ' . $e->getMessage());
            Log::error('Error in test:legal-research command', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            return 1;
        }
    }
}
