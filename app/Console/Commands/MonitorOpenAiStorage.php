<?php

namespace App\Console\Commands;

use App\Models\OpenAiProject;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MonitorOpenAiStorage extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'openai:monitor-storage
                            {--threshold=90 : Storage percentage threshold for warnings}
                            {--alert : Send alerts for projects nearing capacity}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Monitor OpenAI project storage usage and alert on capacity issues';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $threshold = (float) $this->option('threshold');
        $sendAlerts = $this->option('alert');

        $this->info("Monitoring OpenAI project storage usage (threshold: {$threshold}%)");
        $this->newLine();

        $projects = OpenAiProject::where('is_active', true)->with('capacity')->get();

        if ($projects->isEmpty()) {
            $this->warn('No active OpenAI projects found.');
            return;
        }

        $headers = ['Project', 'Storage Used (GB)', 'Allocated (GB)', 'Users', 'Status', 'Accepts New'];
        $rows = [];
        $warningProjects = [];
        $fullProjects = [];
        $needsNewProject = false;

        foreach ($projects as $project) {
            $storageInfo = $project->getStorageInfo();
            $capacity = $project->capacity;
            $capacityInfo = $capacity ? $capacity->getCapacityInfo() : null;

            $status = $this->getStatusText($storageInfo['percentage'], $threshold);
            $acceptsNew = $capacityInfo ? ($capacityInfo['accepts_new_users'] ? '✅' : '❌') : 'N/A';

            if ($storageInfo['percentage'] > $threshold) {
                $warningProjects[] = $project;
            }

            if ($capacityInfo && !$capacityInfo['accepts_new_users']) {
                $fullProjects[] = $project;
            }

            $userSummary = $capacityInfo ?
                "{$capacityInfo['total_users']} ({$capacityInfo['basic_users']}B+{$capacityInfo['standard_users']}S+{$capacityInfo['pro_users']}P)" :
                'N/A';

            $allocatedGB = $capacityInfo ? $capacityInfo['allocated_gb'] : 'N/A';

            $rows[] = [
                $project->name,
                $storageInfo['used_gb'],
                $allocatedGB,
                $userSummary,
                $status,
                $acceptsNew
            ];
        }

        $this->table($headers, $rows);

        // Check if we need new projects
        $activeProjects = $projects->where('is_active', true);
        $availableProjects = $activeProjects->filter(function($project) {
            return $project->capacity && $project->capacity->accepts_new_users;
        });

        $this->newLine();
        $this->info('📊 Capacity Analysis:');
        $this->line("Total active projects: {$activeProjects->count()}");
        $this->line("Projects accepting new users: {$availableProjects->count()}");
        $this->line("Projects over {$threshold}% storage: " . count($warningProjects));
        $this->line("Projects at capacity: " . count($fullProjects));

        // Critical alerts
        if ($availableProjects->count() === 0) {
            $this->newLine();
            $this->error('🚨 CRITICAL: NO PROJECTS ACCEPTING NEW USERS!');
            $this->error('   Action Required: Add new OpenAI project immediately');
            $needsNewProject = true;
        } elseif ($availableProjects->count() === 1) {
            $this->newLine();
            $this->warn('⚠️  WARNING: Only 1 project accepting new users');
            $this->warn('   Recommendation: Add new OpenAI project soon');
        }

        // Storage warnings
        if (!empty($warningProjects)) {
            $this->newLine();
            $this->warn('⚠️  Projects nearing storage capacity:');

            foreach ($warningProjects as $project) {
                $storageInfo = $project->getStorageInfo();
                $this->line("  • {$project->name}: {$storageInfo['used_gb']}GB ({$storageInfo['percentage']}%)");

                if ($sendAlerts) {
                    $this->logStorageAlert($project, $storageInfo);
                }
            }
        }

        // Capacity planning
        $this->showCapacityPlanning($availableProjects);

        if ($sendAlerts && ($needsNewProject || !empty($warningProjects))) {
            $this->logCapacityAlert($availableProjects->count(), count($warningProjects), $needsNewProject);
        }

        return 0;
    }

    /**
     * Get status text based on storage percentage.
     */
    private function getStatusText(float $percentage, float $threshold): string
    {
        if ($percentage >= 95) {
            return '🔴 Critical';
        } elseif ($percentage >= $threshold) {
            return '🟡 Warning';
        } else {
            return '🟢 OK';
        }
    }

    /**
     * Show capacity planning information.
     */
    private function showCapacityPlanning($availableProjects): void
    {
        $this->newLine();
        $this->info('📈 Capacity Planning:');

        if ($availableProjects->isEmpty()) {
            $this->error('   No capacity for new users - ADD NEW PROJECT NOW!');
            return;
        }

        $totalCapacity = [
            'basic' => 0,
            'standard' => 0,
            'pro' => 0
        ];

        foreach ($availableProjects as $project) {
            if (!$project->capacity) continue;

            $capacity = $project->capacity;
            $available = $capacity->getAvailableCapacity();
            $availableGB = round($available / (1024 * 1024 * 1024), 1);

            // Calculate how many users of each tier can fit
            $basicCapacity = floor($available / (1 * 1024 * 1024 * 1024)); // 1GB each
            $standardCapacity = floor($available / (5 * 1024 * 1024 * 1024)); // 5GB each
            $proCapacity = floor($available / (10 * 1024 * 1024 * 1024)); // 10GB each

            $totalCapacity['basic'] += $basicCapacity;
            $totalCapacity['standard'] += $standardCapacity;
            $totalCapacity['pro'] += $proCapacity;

            $this->line("   {$project->name}: {$availableGB}GB available");
            $this->line("     → {$basicCapacity} Basic OR {$standardCapacity} Standard OR {$proCapacity} Pro users");
        }

        $this->newLine();
        $this->line("🎯 Total New User Capacity:");
        $this->line("   Basic users: {$totalCapacity['basic']}");
        $this->line("   Standard users: {$totalCapacity['standard']}");
        $this->line("   Pro users: {$totalCapacity['pro']}");

        // Recommendations
        if ($totalCapacity['basic'] < 50) {
            $this->warn("   ⚠️  Low capacity for Basic users (< 50)");
        }
        if ($totalCapacity['standard'] < 10) {
            $this->warn("   ⚠️  Low capacity for Standard users (< 10)");
        }
        if ($totalCapacity['pro'] < 5) {
            $this->warn("   ⚠️  Low capacity for Pro users (< 5)");
        }
    }

    /**
     * Log capacity alert for monitoring systems.
     */
    private function logCapacityAlert(int $availableProjects, int $warningProjects, bool $needsNewProject): void
    {
        $alertLevel = $needsNewProject ? 'critical' : 'warning';

        Log::log($alertLevel, 'OpenAI project capacity alert', [
            'available_projects' => $availableProjects,
            'warning_projects' => $warningProjects,
            'needs_new_project' => $needsNewProject,
            'alert_type' => 'capacity_planning',
            'action_required' => $needsNewProject ? 'add_new_project_immediately' : 'monitor_closely',
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Log storage alert for monitoring systems.
     */
    private function logStorageAlert(OpenAiProject $project, array $storageInfo): void
    {
        Log::warning('OpenAI project nearing storage capacity', [
            'project_id' => $project->id,
            'project_name' => $project->name,
            'storage_used_gb' => $storageInfo['used_gb'],
            'storage_percentage' => $storageInfo['percentage'],
            'remaining_gb' => $storageInfo['remaining_gb'],
            'case_count' => $project->caseFiles()->count(),
            'alert_type' => 'storage_capacity_warning'
        ]);
    }
}
