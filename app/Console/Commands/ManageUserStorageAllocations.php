<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\OpenAiProject;
use App\Models\OpenAiProjectCapacity;
use App\Services\UserStorageAllocationService;
use Illuminate\Console\Command;

class ManageUserStorageAllocations extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'storage:manage-allocations 
                            {action : Action to perform (allocate|status|migrate)}
                            {--user= : Specific user ID for allocation}
                            {--tier= : Subscription tier for allocation}
                            {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     */
    protected $description = 'Manage user storage allocations across OpenAI projects';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $action = $this->argument('action');

        match($action) {
            'allocate' => $this->allocateStorage(),
            'status' => $this->showStatus(),
            'migrate' => $this->migrateExistingUsers(),
            default => $this->error("Unknown action: {$action}")
        };

        return 0;
    }

    /**
     * Allocate storage for a specific user.
     */
    private function allocateStorage(): void
    {
        $userId = $this->option('user');
        $tier = $this->option('tier');

        if (!$userId || !$tier) {
            $this->error('Both --user and --tier options are required for allocation');
            return;
        }

        $user = User::find($userId);
        if (!$user) {
            $this->error("User {$userId} not found");
            return;
        }

        if ($this->option('dry-run')) {
            $this->info("DRY RUN: Would allocate {$tier} storage for user {$user->name} (ID: {$userId})");
            return;
        }

        $service = app(UserStorageAllocationService::class);
        
        try {
            $allocation = $service->allocateStorageForUser($user, $tier);
            $this->info("✅ Storage allocated successfully");
            $this->line("User: {$user->name} (ID: {$userId})");
            $this->line("Tier: {$tier}");
            $this->line("Project: {$allocation->openaiProject->name}");
            $this->line("Guaranteed Storage: {$allocation->getStorageInfo()['guaranteed_gb']} GB");
        } catch (\Exception $e) {
            $this->error("Failed to allocate storage: {$e->getMessage()}");
        }
    }

    /**
     * Show storage allocation status.
     */
    private function showStatus(): void
    {
        $this->info('Storage Allocation Status');
        $this->newLine();

        // Project capacity overview
        $this->line('📊 Project Capacity Overview:');
        $projects = OpenAiProject::with('capacity')->where('is_active', true)->get();

        $headers = ['Project', 'Total (GB)', 'Allocated (GB)', 'Used (GB)', 'Available (GB)', 'Users', 'Status'];
        $rows = [];

        foreach ($projects as $project) {
            $capacity = $project->capacity;
            if (!$capacity) {
                continue;
            }

            $info = $capacity->getCapacityInfo();
            $status = $info['accepts_new_users'] ? '🟢 Open' : '🔴 Full';

            $rows[] = [
                $project->name,
                $info['total_gb'],
                $info['allocated_gb'],
                $info['used_gb'],
                $info['available_gb'],
                "{$info['basic_users']}B + {$info['standard_users']}S + {$info['pro_users']}P",
                $status
            ];
        }

        $this->table($headers, $rows);

        // User allocation summary
        $this->newLine();
        $this->line('👥 User Allocation Summary:');
        
        $totalUsers = User::count();
        $allocatedUsers = User::whereHas('storageAllocation')->count();
        $unallocatedUsers = $totalUsers - $allocatedUsers;

        $this->line("Total Users: {$totalUsers}");
        $this->line("Allocated: {$allocatedUsers}");
        $this->line("Unallocated: {$unallocatedUsers}");

        if ($unallocatedUsers > 0) {
            $this->newLine();
            $this->warn("⚠️  {$unallocatedUsers} users need storage allocation");
            $this->line("Run: php artisan storage:manage-allocations migrate");
        }
    }

    /**
     * Migrate existing users to the new allocation system.
     */
    private function migrateExistingUsers(): void
    {
        $this->info('Migrating existing users to storage allocation system...');

        $unallocatedUsers = User::whereDoesntHave('storageAllocation')
            ->whereHas('subscriptions', function($query) {
                $query->where('stripe_status', 'active');
            })
            ->with('subscriptions')
            ->get();

        if ($unallocatedUsers->isEmpty()) {
            $this->info('✅ All users already have storage allocations');
            return;
        }

        $this->line("Found {$unallocatedUsers->count()} users to migrate");

        if ($this->option('dry-run')) {
            $this->info('DRY RUN: Would migrate the following users:');
            foreach ($unallocatedUsers as $user) {
                $tier = $this->getUserTier($user);
                $this->line("- {$user->name} (ID: {$user->id}) -> {$tier}");
            }
            return;
        }

        $service = app(UserStorageAllocationService::class);
        $successCount = 0;
        $errorCount = 0;

        $progressBar = $this->output->createProgressBar($unallocatedUsers->count());
        $progressBar->start();

        foreach ($unallocatedUsers as $user) {
            try {
                $tier = $this->getUserTier($user);
                $service->allocateStorageForUser($user, $tier);
                $successCount++;
            } catch (\Exception $e) {
                $this->newLine();
                $this->error("Failed to allocate storage for user {$user->id}: {$e->getMessage()}");
                $errorCount++;
            }
            
            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();
        $this->newLine();

        $this->info("Migration completed:");
        $this->line("✅ Success: {$successCount}");
        if ($errorCount > 0) {
            $this->line("❌ Errors: {$errorCount}");
        }
    }

    /**
     * Determine user's subscription tier.
     */
    private function getUserTier(User $user): string
    {
        $subscription = $user->subscriptions()->where('stripe_status', 'active')->first();
        
        if (!$subscription) {
            return 'pro_se_basic'; // Default tier
        }

        // Map Stripe price IDs to tiers (you'll need to update these based on your actual price IDs)
        $tierMap = [
            'price_1RRf3IBsbrUnlZyq6s7fS5t6' => 'pro_se_basic',     // Basic Upfront
            'price_1RRfBPBsbrUnlZyqjajXvgxb' => 'pro_se_basic',     // Basic
            'price_1RRfCoBsbrUnlZyqVQGJhzJy' => 'pro_se_standard',  // Standard
            'price_1RRfDoBsbrUnlZyqkGJhzJy' => 'pro_se_plus',      // Plus
            // Add attorney tiers as needed
        ];

        return $tierMap[$subscription->stripe_price] ?? 'pro_se_basic';
    }
}
