<?php

namespace App\Console\Commands;

use App\Models\AssistantThread;
use App\Models\AssistantMessage;
use App\Models\CaseFile;
use Illuminate\Console\Command;
use OpenAI;

class TruncateAssistantThreads extends Command
{
    protected $signature = 'assistant:truncate-threads {--force : Force truncation without confirmation}';
    protected $description = 'Truncate all assistant threads, messages, and associated OpenAI resources';

    public function handle()
    {
        if (!$this->option('force') && !$this->confirm('This will delete ALL assistant threads and messages. Continue?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        $this->info('Starting truncation process...');
        
        // Get all threads with their OpenAI IDs before deletion
        $threads = AssistantThread::select('id', 'openai_thread_id', 'case_file_id')->get();
        $count = $threads->count();
        
        $this->info("Found {$count} threads to delete.");
        
        $bar = $this->output->createProgressBar($count);
        $bar->start();
        
        $openaiDeleteErrors = 0;
        
        foreach ($threads as $thread) {
            try {
                // Delete from OpenAI if thread ID exists
                if ($thread->openai_thread_id) {
                    try {
                        $caseFile = CaseFile::find($thread->case_file_id);
                        if ($caseFile && $caseFile->openaiProject) {
                            $openAI = OpenAI::client($caseFile->openaiProject->api_key);
                            $openAI->threads()->delete($thread->openai_thread_id);
                        }
                    } catch (\Exception $e) {
                        $openaiDeleteErrors++;
                        \Log::error('Failed to delete OpenAI thread: ' . $e->getMessage());
                    }
                }
                
                // Delete associated messages first
                AssistantMessage::where('assistant_thread_id', $thread->id)->delete();
                
                // Delete the thread
                $thread->delete();
                
            } catch (\Exception $e) {
                \Log::error('Error deleting thread: ' . $e->getMessage());
            }
            
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine(2);
        
        // Final cleanup with direct SQL for any orphaned records
        $this->info('Performing final cleanup...');
        \DB::statement('DELETE FROM assistant_messages');
        \DB::statement('DELETE FROM assistant_threads');
        
        $this->info('Truncation complete!');
        if ($openaiDeleteErrors > 0) {
            $this->warn("{$openaiDeleteErrors} OpenAI threads could not be deleted. Check logs for details.");
        }
        
        return 0;
    }
}