<?php

namespace App\Console\Commands;

use App\Models\OpenAiProject;
use App\Models\OpenAiProjectCapacity;
use Illuminate\Console\Command;

class InitializeProjectCapacity extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'openai:initialize-capacity 
                            {--dry-run : Show what would be done without making changes}';

    /**
     * The console command description.
     */
    protected $description = 'Initialize capacity tracking for existing OpenAI projects';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        
        $this->info('Initializing OpenAI project capacity tracking...');
        $this->newLine();

        $projects = OpenAiProject::whereDoesntHave('capacity')->get();

        if ($projects->isEmpty()) {
            $this->info('✅ All projects already have capacity tracking initialized.');
            return 0;
        }

        $this->line("Found {$projects->count()} projects without capacity tracking:");
        $this->newLine();

        foreach ($projects as $project) {
            if ($dryRun) {
                $this->line("DRY RUN: Would initialize capacity for: {$project->name}");
            } else {
                $capacity = OpenAiProjectCapacity::create([
                    'openai_project_id' => $project->id,
                    'total_capacity_bytes' => 100 * 1024 * 1024 * 1024, // 100GB
                    'allocated_bytes' => 0,
                    'used_bytes' => $project->storage_used ?? 0,
                    'reserved_bytes' => 5 * 1024 * 1024 * 1024, // 5GB buffer
                    'basic_users' => 0,
                    'standard_users' => 0,
                    'pro_users' => 0,
                    'accepts_new_users' => $project->is_active,
                ]);

                $this->info("✅ Initialized capacity for: {$project->name}");
                $this->line("   - Total Capacity: 100GB");
                $this->line("   - Used Storage: " . round($project->storage_used / (1024 * 1024 * 1024), 2) . "GB");
                $this->line("   - Accepts New Users: " . ($project->is_active ? 'Yes' : 'No'));
                $this->newLine();
            }
        }

        if ($dryRun) {
            $this->newLine();
            $this->info('Run without --dry-run to actually initialize capacity tracking.');
        } else {
            $this->newLine();
            $this->info("✅ Successfully initialized capacity tracking for {$projects->count()} projects.");
            $this->line('You can now run: php artisan openai:monitor-storage');
        }

        return 0;
    }
}
