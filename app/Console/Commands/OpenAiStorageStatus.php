<?php

namespace App\Console\Commands;

use App\Models\OpenAiProject;
use Illuminate\Console\Command;

class OpenAiStorageStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'openai:storage-status {--json : Output as JSON}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Display current OpenAI project storage status';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $projects = OpenAiProject::all();

        if ($projects->isEmpty()) {
            $this->warn('No OpenAI projects found.');
            return;
        }

        $data = [];

        foreach ($projects as $project) {
            $storageInfo = $project->getStorageInfo();
            $caseCount = $project->caseFiles()->count();
            
            $projectData = [
                'id' => $project->id,
                'name' => $project->name,
                'is_active' => $project->is_active,
                'storage_used_bytes' => $storageInfo['used_bytes'],
                'storage_used_gb' => $storageInfo['used_gb'],
                'storage_percentage' => $storageInfo['percentage'],
                'remaining_gb' => $storageInfo['remaining_gb'],
                'is_nearing_capacity' => $storageInfo['is_nearing_capacity'],
                'case_count' => $caseCount,
                'status' => $this->getProjectStatus($storageInfo['percentage'], $project->is_active)
            ];

            $data[] = $projectData;
        }

        if ($this->option('json')) {
            $this->line(json_encode($data, JSON_PRETTY_PRINT));
        } else {
            $this->displayTable($data);
        }

        return 0;
    }

    /**
     * Display data in table format.
     */
    private function displayTable(array $data): void
    {
        $this->info('OpenAI Project Storage Status');
        $this->newLine();

        $headers = ['ID', 'Name', 'Active', 'Used (GB)', 'Percentage', 'Cases', 'Status'];
        $rows = [];

        foreach ($data as $project) {
            $rows[] = [
                $project['id'],
                $project['name'],
                $project['is_active'] ? '✅' : '❌',
                $project['storage_used_gb'],
                number_format($project['storage_percentage'], 1) . '%',
                $project['case_count'],
                $project['status']
            ];
        }

        $this->table($headers, $rows);

        // Summary
        $totalProjects = count($data);
        $activeProjects = count(array_filter($data, fn($p) => $p['is_active']));
        $nearCapacity = count(array_filter($data, fn($p) => $p['is_nearing_capacity']));

        $this->newLine();
        $this->info("Summary: {$totalProjects} total projects, {$activeProjects} active, {$nearCapacity} nearing capacity");
    }

    /**
     * Get project status based on storage and activity.
     */
    private function getProjectStatus(float $percentage, bool $isActive): string
    {
        if (!$isActive) {
            return '⚫ Inactive';
        }

        if ($percentage >= 95) {
            return '🔴 Critical';
        } elseif ($percentage >= 90) {
            return '🟡 Warning';
        } else {
            return '🟢 OK';
        }
    }
}
