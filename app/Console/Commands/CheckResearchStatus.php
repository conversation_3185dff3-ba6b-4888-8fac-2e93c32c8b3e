<?php

namespace App\Console\Commands;

use App\Models\UserResearchQuestion;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class CheckResearchStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'research:check-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check the status of pending research questions';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Checking status of pending research questions...');

        // Get all in-progress research questions
        $pendingQuestions = UserResearchQuestion::whereIn('status', ['pending', 'in_progress'])
            ->whereNotNull('research_id')
            ->get();

        $this->info("Found {$pendingQuestions->count()} pending research questions.");

        foreach ($pendingQuestions as $question) {
            $this->info("Checking status for question ID: {$question->id}, Research ID: {$question->research_id}");

            try {
                if ($question->research_type === 'regular') {
                    $this->checkRegularResearchStatus($question);
                } else {
                    $this->checkDeepResearchStatus($question);
                }
            } catch (\Exception $e) {
                $this->error("Error checking status for question ID: {$question->id}: {$e->getMessage()}");
                Log::error('Error checking research status', [
                    'question_id' => $question->id,
                    'research_id' => $question->research_id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);
            }
        }

        $this->info('Finished checking research status.');

        return 0;
    }

    /**
     * Check the status of a regular research question.
     *
     * @param UserResearchQuestion $question
     * @return void
     */
    protected function checkRegularResearchStatus(UserResearchQuestion $question): void
    {
        $response = Http::withHeaders([
            'X-API-Key' => env('LEGAL_RESEARCH_API_KEY'),
            'Accept' => 'application/json',
        ])->get(config('services.gpt_researcher.url') . '/api/legal-research/status/' . $question->research_id);

        if ($response->successful()) {
            $data = $response->json();
            $status = $data['status'] ?? 'unknown';

            $this->info("Research status: {$status}");

            // Update the question status based on the research status
            if (in_array($status, ['COMPLETED', 'DONE'])) {
                $question->update([
                    'status' => 'completed',
                    'research_completed_at' => now(),
                    'research_report_url' => $data['public_report_url'] ?? null,
                    'research_markdown_content' => $data['markdown_content'] ?? null,
                ]);
                $this->info("Updated question ID: {$question->id} to completed.");
            } elseif (in_array($status, ['FAILED', 'ERROR'])) {
                $question->update([
                    'status' => 'failed',
                    'research_error' => $data['error'] ?? 'Unknown error',
                ]);
                $this->error("Research failed for question ID: {$question->id}.");
            } else {
                // Still in progress, update the status if needed
                if ($question->status !== 'in_progress') {
                    $question->update(['status' => 'in_progress']);
                }
                $this->info("Research still in progress for question ID: {$question->id}.");
            }
        } else {
            $this->error("Failed to check status for question ID: {$question->id}. API returned: {$response->status()}");
            Log::error('Failed to check regular research status', [
                'question_id' => $question->id,
                'research_id' => $question->research_id,
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
        }
    }

    /**
     * Check the status of a deep research question.
     *
     * @param UserResearchQuestion $question
     * @return void
     */
    protected function checkDeepResearchStatus(UserResearchQuestion $question): void
    {
        $response = Http::withHeaders([
            'X-API-Key' => env('LEGAL_RESEARCH_API_KEY'),
            'Accept' => 'application/json',
        ])->get(config('services.gpt_researcher.url') . '/api/multi-research/status/' . $question->research_id);

        if ($response->successful()) {
            $data = $response->json();
            $status = $data['status'] ?? 'unknown';

            $this->info("Deep research status: {$status}");

            // Update the question status based on the research status
            if (in_array($status, ['COMPLETED', 'DONE'])) {
                // For deep research, we might need to get the content separately
                $contentResponse = Http::withHeaders([
                    'X-API-Key' => env('LEGAL_RESEARCH_API_KEY'),
                    'Accept' => 'application/json',
                ])->get(config('services.gpt_researcher.url') . '/api/multi-research/content/' . $question->research_id);

                $markdownContent = null;
                if ($contentResponse->successful()) {
                    $contentData = $contentResponse->json();
                    $markdownContent = $contentData['content'] ?? null;
                }

                $question->update([
                    'status' => 'completed',
                    'research_completed_at' => now(),
                    'research_report_url' => $data['report_url'] ?? null,
                    'research_markdown_content' => $markdownContent,
                ]);
                $this->info("Updated deep research question ID: {$question->id} to completed.");
            } elseif (in_array($status, ['FAILED', 'ERROR'])) {
                $question->update([
                    'status' => 'failed',
                    'research_error' => $data['error'] ?? 'Unknown error',
                ]);
                $this->error("Deep research failed for question ID: {$question->id}.");
            } else {
                // Still in progress, update the status if needed
                if ($question->status !== 'in_progress') {
                    $question->update(['status' => 'in_progress']);
                }
                $this->info("Deep research still in progress for question ID: {$question->id}.");
            }
        } else {
            $this->error("Failed to check status for deep research question ID: {$question->id}. API returned: {$response->status()}");
            Log::error('Failed to check deep research status', [
                'question_id' => $question->id,
                'research_id' => $question->research_id,
                'status' => $response->status(),
                'body' => $response->body(),
            ]);
        }
    }
}
