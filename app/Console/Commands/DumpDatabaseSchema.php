<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Schema;

class DumpDatabaseSchema extends Command
{
    protected $signature = 'db:schema:dump {--output=confluence/database/schema.json : Output file path}';
    protected $description = 'Dump the entire database schema into a JSON file';

    public function handle()
    {
        $outputPath = $this->option('output');
        $fullPath = base_path($outputPath);
        
        $this->info("Dumping database schema to {$fullPath}...");
        
        $schema = $this->generateSchema();
        
        File::put($fullPath, json_encode($schema, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
        
        $this->info("Schema successfully dumped to {$fullPath}");
        
        return 0;
    }
    
    private function generateSchema()
    {
        $tables = $this->getTables();
        $schema = [];
        
        foreach ($tables as $table) {
            $schema[$table] = [
                'columns' => $this->getColumns($table),
                'indexes' => $this->getIndexes($table),
                'foreign_keys' => $this->getForeignKeys($table),
            ];
        }
        
        return $schema;
    }
    
    private function getTables()
    {
        // Get all tables from the database
        $tables = [];
        
        // For MySQL/MariaDB
        if (DB::connection()->getDriverName() === 'mysql') {
            $dbName = DB::connection()->getDatabaseName();
            $results = DB::select("SHOW TABLES FROM `{$dbName}`");
            
            $columnName = "Tables_in_{$dbName}";
            foreach ($results as $result) {
                $tables[] = $result->$columnName;
            }
        } 
        // For SQLite
        elseif (DB::connection()->getDriverName() === 'sqlite') {
            $results = DB::select("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'");
            foreach ($results as $result) {
                $tables[] = $result->name;
            }
        }
        // For PostgreSQL
        elseif (DB::connection()->getDriverName() === 'pgsql') {
            $results = DB::select("SELECT tablename FROM pg_catalog.pg_tables WHERE schemaname = 'public'");
            foreach ($results as $result) {
                $tables[] = $result->tablename;
            }
        }
        
        return $tables;
    }
    
    private function getColumns($table)
    {
        $columns = [];
        $columnList = Schema::getColumnListing($table);
        
        foreach ($columnList as $column) {
            // For MySQL/MariaDB
            if (DB::connection()->getDriverName() === 'mysql') {
                $columnInfo = DB::selectOne("SHOW COLUMNS FROM `{$table}` WHERE Field = ?", [$column]);
                
                $columns[$column] = [
                    'type' => $columnInfo->Type,
                    'nullable' => $columnInfo->Null === 'YES',
                    'default' => $columnInfo->Default,
                    'autoIncrement' => strpos($columnInfo->Extra, 'auto_increment') !== false,
                ];
            } 
            // For SQLite and PostgreSQL, use a more generic approach
            else {
                $columns[$column] = [
                    'type' => DB::getSchemaBuilder()->getColumnType($table, $column),
                    'nullable' => !Schema::hasColumn($table, $column) ? true : !DB::getSchemaBuilder()->getConnection()->getDoctrineColumn($table, $column)->getNotnull(),
                    'default' => Schema::hasColumn($table, $column) ? DB::getSchemaBuilder()->getConnection()->getDoctrineColumn($table, $column)->getDefault() : null,
                ];
            }
        }
        
        return $columns;
    }
    
    private function getIndexes($table)
    {
        $indexes = [];
        
        // For MySQL/MariaDB
        if (DB::connection()->getDriverName() === 'mysql') {
            $indexList = DB::select("SHOW INDEXES FROM `{$table}`");
            
            foreach ($indexList as $index) {
                $indexName = $index->Key_name;
                
                if (!isset($indexes[$indexName])) {
                    $indexes[$indexName] = [
                        'columns' => [],
                        'primary' => $indexName === 'PRIMARY',
                        'unique' => $index->Non_unique == 0,
                    ];
                }
                
                $indexes[$indexName]['columns'][] = $index->Column_name;
            }
        }
        // For SQLite
        elseif (DB::connection()->getDriverName() === 'sqlite') {
            $indexList = DB::select("PRAGMA index_list({$table})");
            
            foreach ($indexList as $index) {
                $indexName = $index->name;
                $indexInfo = DB::select("PRAGMA index_info({$indexName})");
                
                $columns = [];
                foreach ($indexInfo as $column) {
                    $columns[] = $column->name;
                }
                
                $indexes[$indexName] = [
                    'columns' => $columns,
                    'primary' => $index->origin === 'pk',
                    'unique' => (bool) $index->unique,
                ];
            }
        }
        // For PostgreSQL
        elseif (DB::connection()->getDriverName() === 'pgsql') {
            $indexList = DB::select("
                SELECT
                    i.relname as index_name,
                    a.attname as column_name,
                    ix.indisunique as is_unique,
                    ix.indisprimary as is_primary
                FROM
                    pg_class t,
                    pg_class i,
                    pg_index ix,
                    pg_attribute a
                WHERE
                    t.oid = ix.indrelid
                    AND i.oid = ix.indexrelid
                    AND a.attrelid = t.oid
                    AND a.attnum = ANY(ix.indkey)
                    AND t.relkind = 'r'
                    AND t.relname = ?
                ORDER BY
                    i.relname
            ", [$table]);
            
            foreach ($indexList as $index) {
                $indexName = $index->index_name;
                
                if (!isset($indexes[$indexName])) {
                    $indexes[$indexName] = [
                        'columns' => [],
                        'primary' => (bool) $index->is_primary,
                        'unique' => (bool) $index->is_unique,
                    ];
                }
                
                $indexes[$indexName]['columns'][] = $index->column_name;
            }
        }
        
        return $indexes;
    }
    
    private function getForeignKeys($table)
    {
        $foreignKeys = [];
        
        try {
            // For MySQL/MariaDB
            if (DB::connection()->getDriverName() === 'mysql') {
                $dbName = DB::connection()->getDatabaseName();
                
                // Query information_schema for foreign keys
                $fkList = DB::select("
                    SELECT
                        CONSTRAINT_NAME as constraint_name,
                        COLUMN_NAME as column_name,
                        REFERENCED_TABLE_NAME as referenced_table_name,
                        REFERENCED_COLUMN_NAME as referenced_column_name
                    FROM
                        information_schema.KEY_COLUMN_USAGE
                    WHERE
                        TABLE_SCHEMA = ? AND
                        TABLE_NAME = ? AND
                        REFERENCED_TABLE_NAME IS NOT NULL
                ", [$dbName, $table]);
                
                foreach ($fkList as $fk) {
                    $constraintName = $fk->constraint_name;
                    
                    if (!isset($foreignKeys[$constraintName])) {
                        $foreignKeys[$constraintName] = [
                            'local_columns' => [],
                            'foreign_table' => $fk->referenced_table_name,
                            'foreign_columns' => [],
                            'on_delete' => null,
                            'on_update' => null,
                        ];
                    }
                    
                    $foreignKeys[$constraintName]['local_columns'][] = $fk->column_name;
                    $foreignKeys[$constraintName]['foreign_columns'][] = $fk->referenced_column_name;
                }
                
                // Get the ON DELETE and ON UPDATE actions
                if (!empty($foreignKeys)) {
                    $referentialConstraints = DB::select("
                        SELECT
                            CONSTRAINT_NAME as constraint_name,
                            DELETE_RULE as delete_rule,
                            UPDATE_RULE as update_rule
                        FROM
                            information_schema.REFERENTIAL_CONSTRAINTS
                        WHERE
                            CONSTRAINT_SCHEMA = ? AND
                            TABLE_NAME = ?
                    ", [$dbName, $table]);
                    
                    foreach ($referentialConstraints as $constraint) {
                        if (isset($foreignKeys[$constraint->constraint_name])) {
                            $foreignKeys[$constraint->constraint_name]['on_delete'] = $constraint->delete_rule;
                            $foreignKeys[$constraint->constraint_name]['on_update'] = $constraint->update_rule;
                        }
                    }
                }
            }
            // For SQLite
            elseif (DB::connection()->getDriverName() === 'sqlite') {
                $fkList = DB::select("PRAGMA foreign_key_list({$table})");
                
                foreach ($fkList as $fk) {
                    $constraintName = "fk_{$table}_{$fk->id}";
                    
                    if (!isset($foreignKeys[$constraintName])) {
                        $foreignKeys[$constraintName] = [
                            'local_columns' => [],
                            'foreign_table' => $fk->table,
                            'foreign_columns' => [],
                            'on_delete' => $fk->on_delete,
                            'on_update' => $fk->on_update,
                        ];
                    }
                    
                    $foreignKeys[$constraintName]['local_columns'][] = $fk->from;
                    $foreignKeys[$constraintName]['foreign_columns'][] = $fk->to;
                }
            }
            // For PostgreSQL
            elseif (DB::connection()->getDriverName() === 'pgsql') {
                $fkList = DB::select("
                    SELECT
                        tc.constraint_name,
                        kcu.column_name,
                        ccu.table_name AS foreign_table_name,
                        ccu.column_name AS foreign_column_name,
                        rc.delete_rule,
                        rc.update_rule
                    FROM
                        information_schema.table_constraints AS tc
                        JOIN information_schema.key_column_usage AS kcu
                          ON tc.constraint_name = kcu.constraint_name
                        JOIN information_schema.constraint_column_usage AS ccu
                          ON ccu.constraint_name = tc.constraint_name
                        JOIN information_schema.referential_constraints AS rc
                          ON rc.constraint_name = tc.constraint_name
                    WHERE tc.constraint_type = 'FOREIGN KEY' AND tc.table_name = ?
                ", [$table]);
                
                foreach ($fkList as $fk) {
                    $constraintName = $fk->constraint_name;
                    
                    if (!isset($foreignKeys[$constraintName])) {
                        $foreignKeys[$constraintName] = [
                            'local_columns' => [],
                            'foreign_table' => $fk->foreign_table_name,
                            'foreign_columns' => [],
                            'on_delete' => $fk->delete_rule,
                            'on_update' => $fk->update_rule,
                        ];
                    }
                    
                    $foreignKeys[$constraintName]['local_columns'][] = $fk->column_name;
                    $foreignKeys[$constraintName]['foreign_columns'][] = $fk->foreign_column_name;
                }
            }
        } catch (\Exception $e) {
            $this->warn("Error getting foreign keys for table {$table}: " . $e->getMessage());
        }
        
        return $foreignKeys;
    }
}
