<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;
use OpenAI\Laravel\Facades\OpenAI;

class TranslateLanguageFiles extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'translate:language-files
                            {--language= : Specific language code to translate (e.g., es, fr)}
                            {--file= : Specific file to translate (e.g., general.php)}
                            {--batch-size=10 : Number of strings to translate in a single API call}
                            {--delay=1 : Delay in seconds between API calls to avoid rate limiting}
                            {--dry-run : Run without making actual changes}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Translate language files using OpenAI';

    /**
     * The base path for language files
     *
     * @var string
     */
    protected $langPath = 'resources/lang';

    /**
     * The OpenAI model to use for translations
     *
     * @var string
     */
    protected $model = 'gpt-4.1-nano';

    /**
     * Available languages from config
     *
     * @var array
     */
    protected $availableLanguages = [];

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->availableLanguages = config('language.available', []);

        // Get options
        $specificLanguage = $this->option('language');
        $specificFile = $this->option('file');
        $batchSize = (int) $this->option('batch-size');
        $delay = (int) $this->option('delay');
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        // Validate language if specified
        if ($specificLanguage && !isset($this->availableLanguages[$specificLanguage])) {
            $this->error("Language '{$specificLanguage}' is not configured in config/language.php");
            return 1;
        }

        // Get all language directories except 'en' (source language)
        $langDirs = collect(File::directories(base_path($this->langPath)))
            ->map(fn($dir) => basename($dir))
            ->filter(fn($dir) => $dir !== 'en')
            ->when($specificLanguage, fn($dirs) => $dirs->filter(fn($dir) => $dir === $specificLanguage))
            ->values()
            ->all();

        // Override file selection to only process landing.php if no specific file is provided
        if (!$specificFile) {
            $specificFile = 'welcome.php';
            $this->info("Focusing only on welcome.php file");
        }

        if (empty($langDirs)) {
            $this->error('No language directories found to translate');
            return 1;
        }

        // Show summary of what will be translated
        $this->info('The following languages will be translated:');
        foreach ($langDirs as $langDir) {
            $langName = $this->availableLanguages[$langDir]['name'] ?? $langDir;
            $this->line(" - {$langName} ({$langDir})");
        }

        // Confirm unless --force is used
        if (!$force && !$this->confirm('Do you want to continue?')) {
            $this->info('Operation cancelled.');
            return 0;
        }

        // Process each language directory
        foreach ($langDirs as $langDir) {
            $this->info("Processing language: {$langDir}");

            // Get all PHP files in the language directory
            $langFiles = collect(File::files(base_path("{$this->langPath}/{$langDir}")))
                ->filter(fn($file) => $file->getExtension() === 'php')
                ->map(fn($file) => $file->getFilename())
                ->when($specificFile, fn($files) => $files->filter(fn($file) => $file === $specificFile))
                ->values()
                ->all();

            if (empty($langFiles)) {
                $this->warn("No PHP files found in {$langDir} directory");
                continue;
            }

            // Process each file
            foreach ($langFiles as $file) {
                $this->info("  Processing file: {$file}");

                // Get the English (source) translations
                $enFilePath = base_path("{$this->langPath}/en/{$file}");
                if (!File::exists($enFilePath)) {
                    $this->warn("    English source file not found for {$file}, skipping");
                    continue;
                }

                $enTranslations = require $enFilePath;

                // Get the target language translations
                $targetFilePath = base_path("{$this->langPath}/{$langDir}/{$file}");
                $targetTranslations = File::exists($targetFilePath) ? require $targetFilePath : [];

                // Translate the file
                $translatedContent = $this->translateFile(
                    $enTranslations,
                    $targetTranslations,
                    $langDir,
                    $file,
                    $batchSize,
                    $delay,
                    $dryRun
                );

                // Save the translated content if not in dry run mode
                if (!$dryRun && $translatedContent) {
                    $this->saveTranslations($targetFilePath, $translatedContent);
                    $this->info("    Saved translations to {$targetFilePath}");
                }
            }
        }

        $this->info('Translation process completed!');
        return 0;
    }

    /**
     * Translate a file's content
     */
    protected function translateFile($enTranslations, $targetTranslations, $langCode, $fileName, $batchSize, $delay, $dryRun)
    {
        // Special handling for Spanish files that might have French content
        $forceTranslate = ($langCode === 'es' && $fileName === 'general.php');
        if ($forceTranslate) {
            $this->info("    Special handling for Spanish general.php file (might contain French)");
        }

        // Flatten both arrays for comparison
        $flatEn = Arr::dot($enTranslations);
        $flatTarget = Arr::dot($targetTranslations);

        // Find keys that need translation (missing or potentially incorrect)
        $keysToTranslate = [];
        foreach ($flatEn as $key => $value) {
            // Skip if the key exists in target and has a non-empty value
            if (isset($flatTarget[$key]) && !empty($flatTarget[$key])) {
                // Check if the target value is identical to English or if we're forcing translation
                if ($flatTarget[$key] === $value || $forceTranslate) {
                    $keysToTranslate[$key] = $value;
                }
            } else {
                // Key is missing or empty in target
                $keysToTranslate[$key] = $value;
            }
        }

        if (empty($keysToTranslate)) {
            $this->info("    No translations needed for {$fileName}");
            return null;
        }

        $this->info("    Found " . count($keysToTranslate) . " strings to translate");

        if ($dryRun) {
            $this->warn("    Dry run mode - not performing actual translations");
            return null;
        }

        // Process translations in batches
        $batches = array_chunk($keysToTranslate, $batchSize, true);
        $translatedValues = [];

        $bar = $this->output->createProgressBar(count($batches));
        $bar->start();

        foreach ($batches as $batch) {
            // Translate the batch
            $translatedBatch = $this->translateBatch($batch, $langCode);

            // Merge the translated values
            $translatedValues = array_merge($translatedValues, $translatedBatch);

            // Delay between batches
            if ($delay > 0 && next($batches) !== false) {
                sleep($delay);
            }

            $bar->advance();
        }

        $bar->finish();
        $this->newLine();

        // Merge the translated values with the existing translations
        foreach ($translatedValues as $key => $value) {
            Arr::set($targetTranslations, $key, $value);
        }

        return $targetTranslations;
    }

    /**
     * Translate a batch of strings
     */
    protected function translateBatch($batch, $langCode)
    {
        $languageName = $this->availableLanguages[$langCode]['name'] ?? $langCode;
        $nativeName = $this->availableLanguages[$langCode]['native_name'] ?? $languageName;

        // Prepare the data for the API call
        $data = [
            'keys' => array_keys($batch),
            'values' => array_values($batch),
            'language_code' => $langCode,
            'language_name' => $languageName,
            'native_name' => $nativeName
        ];

        try {
            // Make the API call to OpenAI
            $response = $this->callOpenAI($data);

            // Process and return the translated values
            $translatedValues = [];
            foreach ($response as $key => $value) {
                $translatedValues[$key] = $value;
            }

            return $translatedValues;
        } catch (\Exception $e) {
            $this->error("    Translation error: " . $e->getMessage());
            Log::error('Translation error', [
                'message' => $e->getMessage(),
                'language' => $langCode,
                'batch' => $batch
            ]);

            // Return empty array on error
            return [];
        }
    }

    /**
     * Call OpenAI API to translate strings
     */
    protected function callOpenAI($data)
    {
        $langCode = $data['language_code'];
        $languageName = $data['language_name'];
        $nativeName = $data['native_name'];

        // Check for API key in various locations
        $apiKey = $this->getOpenAIApiKey();
        if (!$apiKey) {
            throw new \Exception('OpenAI API key not found. Please set OPENAI_API_KEY in your .env file or use --api-key option.');
        }

        // Configure OpenAI with the API key
        config(['openai.api_key' => $apiKey]);

        // Prepare the system message
        $systemMessage = "You are a professional translator specializing in software localization. Translate the given strings from English to {$languageName} ({$nativeName}). Follow these rules strictly:
1. Maintain the same meaning and tone as the original text
2. Keep any placeholders like :placeholder, {placeholder}, %placeholder%, or {{placeholder}} intact - do not translate them
3. Preserve any HTML tags and formatting
4. Ensure translations are culturally appropriate for {$languageName} speakers
5. For UI elements, keep translations concise
6. Return only the translated text without explanations
7. If the source language appears to be French instead of English, translate directly from French to {$languageName}";

        // Prepare the user message with the strings to translate
        $userMessage = "Translate the following strings to {$languageName}. Return a JSON object with the original keys and the translated values:\n\n";
        $userMessage .= json_encode(array_combine($data['keys'], $data['values']), JSON_PRETTY_PRINT);

        try {
            // Make the API call using the OpenAI facade
            $response = OpenAI::chat()->create([
                'model' => $this->model,
                'messages' => [
                    ['role' => 'system', 'content' => $systemMessage],
                    ['role' => 'user', 'content' => $userMessage],
                ],
                'temperature' => 0.3,
                'response_format' => ['type' => 'json_object'],
            ]);

            // Get the content from the response
            $content = $response->choices[0]->message->content;

            if (!$content) {
                throw new \Exception('Empty response from OpenAI API');
            }

            // Parse the JSON response
            $translations = json_decode($content, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Invalid JSON response from OpenAI API: ' . json_last_error_msg());
            }

            return $translations;
        } catch (\Exception $e) {
            throw new \Exception('OpenAI API request failed: ' . $e->getMessage());
        }
    }

    /**
     * Get the OpenAI API key from various sources
     */
    protected function getOpenAIApiKey()
    {
        // Check for API key in config
        $apiKey = config('openai.api_key');
        if ($apiKey) {
            return $apiKey;
        }

        // Check for API key in services config
        $apiKey = config('services.openai.api_key');
        if ($apiKey) {
            return $apiKey;
        }

        // Check for API key in environment
        $apiKey = env('OPENAI_API_KEY');
        if ($apiKey) {
            return $apiKey;
        }

        // Check if we have an active OpenAI project
        try {
            if (class_exists('\App\Models\OpenAiProject')) {
                $project = \App\Models\OpenAiProject::where('is_active', true)
                    ->orderBy('storage_used')
                    ->first();

                if ($project && $project->api_key) {
                    return $project->api_key;
                }
            }
        } catch (\Exception $e) {
            // Ignore errors when checking for OpenAI projects
        }

        return null;
    }

    /**
     * Save translations to a file
     */
    protected function saveTranslations($filePath, $translations)
    {
        // Create the PHP code
        $code = "<?php\n\nreturn " . $this->varExport($translations) . ";\n";

        // Ensure the directory exists
        $directory = dirname($filePath);
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        // Write the file
        File::put($filePath, $code);
    }

    /**
     * Custom var_export with proper formatting
     */
    protected function varExport($var, $indent = '')
    {
        switch (gettype($var)) {
            case 'string':
                return "'" . addcslashes($var, "'\\\0..\37") . "'";
            case 'array':
                $indexed = array_keys($var) === range(0, count($var) - 1);
                $r = [];
                foreach ($var as $key => $value) {
                    $r[] = "$indent    "
                         . ($indexed ? "" : $this->varExport($key) . " => ")
                         . $this->varExport($value, "$indent    ");
                }
                return "[\n" . implode(",\n", $r) . "\n" . $indent . "]";
            case 'boolean':
                return $var ? 'true' : 'false';
            case 'NULL':
                return 'null';
            case 'integer':
            case 'double':
            default:
                return (string) $var;
        }
    }
}
