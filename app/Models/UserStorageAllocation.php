<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserStorageAllocation extends Model
{
    protected $fillable = [
        'user_id',
        'openai_project_id',
        'guaranteed_bytes',
        'used_bytes',
        'subscription_tier',
        'allocated_at',
    ];

    protected $casts = [
        'guaranteed_bytes' => 'integer',
        'used_bytes' => 'integer',
        'allocated_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function openaiProject(): BelongsTo
    {
        return $this->belongsTo(OpenAiProject::class, 'openai_project_id');
    }

    /**
     * Get storage usage as percentage of guaranteed allocation.
     */
    public function getUsagePercentage(): float
    {
        if ($this->guaranteed_bytes === 0) {
            return 0;
        }
        
        return ($this->used_bytes / $this->guaranteed_bytes) * 100;
    }

    /**
     * Get remaining storage in bytes.
     */
    public function getRemainingBytes(): int
    {
        return max(0, $this->guaranteed_bytes - $this->used_bytes);
    }

    /**
     * Get human-readable storage information.
     */
    public function getStorageInfo(): array
    {
        $guaranteedGB = round($this->guaranteed_bytes / (1024 * 1024 * 1024), 2);
        $usedGB = round($this->used_bytes / (1024 * 1024 * 1024), 2);
        $percentage = $this->getUsagePercentage();

        return [
            'guaranteed_bytes' => $this->guaranteed_bytes,
            'used_bytes' => $this->used_bytes,
            'guaranteed_gb' => $guaranteedGB,
            'used_gb' => $usedGB,
            'percentage' => round($percentage, 2),
            'remaining_gb' => round($guaranteedGB - $usedGB, 2),
            'is_near_limit' => $percentage > 90,
            'subscription_tier' => $this->subscription_tier
        ];
    }

    /**
     * Check if user can upload a file of given size.
     */
    public function canAccommodateFile(int $fileSize): bool
    {
        return ($this->used_bytes + $fileSize) <= $this->guaranteed_bytes;
    }
}
