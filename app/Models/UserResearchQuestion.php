<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserResearchQuestion extends Model
{
    use HasFactory;

    // Define research types
    const TYPE_REGULAR = 'regular';
    const TYPE_DEEP = 'deep';
    const TYPE_QUICK = 'quick';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'question',
        'research_type',
        'status',
        'research_id',
        'research_requested_at',
        'research_completed_at',
        'research_report_url',
        'research_markdown_content',
        'research_error',
        'metadata'
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'research_requested_at' => 'datetime',
        'research_completed_at' => 'datetime',
        'metadata' => 'array',
        'research_type' => 'string',
    ];

    /**
     * Get the user that owns the research question.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include research questions of a specific type.
     */
    public function scopeOfType($query, $type)
    {
        return $query->where('research_type', $type);
    }

    /**
     * Scope a query to only include research questions with a specific status.
     */
    public function scopeWithStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope a query to only include completed research questions.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include pending or in-progress research questions.
     */
    public function scopeInProgress($query)
    {
        return $query->whereIn('status', ['pending', 'in_progress']);
    }

    /**
     * Scope a query to only include failed research questions.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }
}
