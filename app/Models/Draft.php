<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOneThrough;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use App\Models\User;
use App\Models\AssistantThread;
use App\Models\AssistantMessage;

class Draft extends Model
{
    use HasFactory;

    protected $fillable = [
        'case_file_id',
        'template_id',
        'draft_type',
        'description',
        'document_name',
        'status',
        'version',
        'last_edited_by',
        'document_id',
        'structured_context',
        'sections_structure',
        'active_section_id',
        'ai_generation_prompts',
        'metadata',
        'ai_summary',
        'caption_data'
    ];

    protected $casts = [
        'structured_context' => 'array',
        'sections_structure' => 'array',
        'ai_generation_prompts' => 'array',
        'metadata' => 'array',
        'caption_data' => 'array',
        'interview_completed_at' => 'datetime',
        'published_at' => 'datetime',
        'version' => 'integer',
    ];

    /**
     * Get the case file that owns the draft.
     */
    public function caseFile(): BelongsTo
    {
        return $this->belongsTo(CaseFile::class);
    }

    /**
     * Get the document generated from this draft.
     */
    public function document(): BelongsTo
    {
        return $this->belongsTo(Document::class);
    }

    /**
     * Get the user who last edited this draft.
     */
    public function lastEditor(): BelongsTo
    {
        return $this->belongsTo(User::class, 'last_edited_by');
    }

    /**
     * Get the template used for this draft.
     */
    public function template(): BelongsTo
    {
        return $this->belongsTo(DocumentTemplate::class, 'template_id');
    }

    /**
     * Get the exhibits associated with this draft.
     */
    public function exhibits()
    {
        return $this->hasMany(Exhibit::class);
    }

    /**
     * Get the assistant thread associated with this draft.
     */
    public function assistantThread()
    {
        return $this->hasOneThrough(
            AssistantThread::class,
            CaseFile::class,
            'id', // Foreign key on case_files table
            'case_file_id', // Foreign key on assistant_threads table
            'case_file_id', // Local key on drafts table
            'id' // Local key on case_files table
        )->where('metadata->draft_id', $this->id)
          ->where('type', 'document_editor');
    }

    /**
     * Get the AI chat messages associated with this draft.
     */
    public function aiChatMessages()
    {
        return $this->hasManyThrough(
            AssistantMessage::class,
            AssistantThread::class,
            'metadata->draft_id', // Foreign key on assistant_threads table (JSON path)
            'assistant_thread_id', // Foreign key on assistant_messages table
            'id', // Local key on drafts table
            'id' // Local key on assistant_threads table
        )->where('assistant_threads.type', 'document_editor');
    }
}
