<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ResearchSession extends Model
{
    protected $fillable = [
        'case_file_id',
        'status',
        'search_parameters',
        'research_results'
    ];

    protected $casts = [
        'search_parameters' => 'json',
        'research_results' => 'json'
    ];

    public function queries(): HasMany
    {
        return $this->hasMany(ResearchQuery::class);
    }
}