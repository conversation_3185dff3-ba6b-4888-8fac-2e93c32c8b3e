<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class OpenAiProjectCapacity extends Model
{
    protected $table = 'openai_project_capacity';

    protected $fillable = [
        'openai_project_id',
        'total_capacity_bytes',
        'allocated_bytes',
        'used_bytes',
        'reserved_bytes',
        'basic_users',
        'standard_users',
        'pro_users',
        'accepts_new_users',
    ];

    protected $casts = [
        'total_capacity_bytes' => 'integer',
        'allocated_bytes' => 'integer',
        'used_bytes' => 'integer',
        'reserved_bytes' => 'integer',
        'basic_users' => 'integer',
        'standard_users' => 'integer',
        'pro_users' => 'integer',
        'accepts_new_users' => 'boolean',
    ];

    public function openaiProject(): BelongsTo
    {
        return $this->belongsTo(OpenAiProject::class, 'openai_project_id');
    }

    /**
     * Get available capacity for new allocations.
     */
    public function getAvailableCapacity(): int
    {
        return max(0, $this->total_capacity_bytes - $this->allocated_bytes - $this->reserved_bytes);
    }

    /**
     * Check if project can accommodate a new user of given tier.
     */
    public function canAccommodateUser(string $tier): bool
    {
        if (!$this->accepts_new_users) {
            return false;
        }

        $requiredBytes = $this->getStorageForTier($tier);
        return $this->getAvailableCapacity() >= $requiredBytes;
    }

    /**
     * Get storage allocation for a subscription tier.
     */
    public function getStorageForTier(string $tier): int
    {
        $allocations = [
            'pro_se_basic' => 1 * 1024 * 1024 * 1024,      // 1GB
            'pro_se_standard' => 5 * 1024 * 1024 * 1024,   // 5GB
            'pro_se_plus' => 10 * 1024 * 1024 * 1024,      // 10GB
            'attorney_basic' => 10 * 1024 * 1024 * 1024,   // 10GB
            'attorney_pro' => 10 * 1024 * 1024 * 1024,     // 10GB
            'attorney_enterprise' => 10 * 1024 * 1024 * 1024, // 10GB
        ];

        return $allocations[$tier] ?? 0;
    }

    /**
     * Allocate storage for a user.
     */
    public function allocateUser(string $tier): bool
    {
        if (!$this->canAccommodateUser($tier)) {
            return false;
        }

        $requiredBytes = $this->getStorageForTier($tier);
        
        $this->increment('allocated_bytes', $requiredBytes);
        
        // Update user counts
        match($tier) {
            'pro_se_basic' => $this->increment('basic_users'),
            'pro_se_standard' => $this->increment('standard_users'),
            'pro_se_plus', 'attorney_basic', 'attorney_pro', 'attorney_enterprise' => $this->increment('pro_users'),
        };

        // Check if project should stop accepting new users
        if ($this->getAvailableCapacity() < (1 * 1024 * 1024 * 1024)) { // Less than 1GB available
            $this->update(['accepts_new_users' => false]);
        }

        return true;
    }

    /**
     * Deallocate storage when user downgrades or cancels.
     */
    public function deallocateUser(string $tier): void
    {
        $requiredBytes = $this->getStorageForTier($tier);
        
        $this->decrement('allocated_bytes', $requiredBytes);
        
        // Update user counts
        match($tier) {
            'pro_se_basic' => $this->decrement('basic_users'),
            'pro_se_standard' => $this->decrement('standard_users'),
            'pro_se_plus', 'attorney_basic', 'attorney_pro', 'attorney_enterprise' => $this->decrement('pro_users'),
        };

        // Re-enable accepting users if capacity is available
        if ($this->getAvailableCapacity() >= (1 * 1024 * 1024 * 1024)) {
            $this->update(['accepts_new_users' => true]);
        }
    }

    /**
     * Get capacity information.
     */
    public function getCapacityInfo(): array
    {
        $totalGB = round($this->total_capacity_bytes / (1024 * 1024 * 1024), 2);
        $allocatedGB = round($this->allocated_bytes / (1024 * 1024 * 1024), 2);
        $usedGB = round($this->used_bytes / (1024 * 1024 * 1024), 2);
        $availableGB = round($this->getAvailableCapacity() / (1024 * 1024 * 1024), 2);

        return [
            'total_gb' => $totalGB,
            'allocated_gb' => $allocatedGB,
            'used_gb' => $usedGB,
            'available_gb' => $availableGB,
            'allocation_percentage' => ($this->allocated_bytes / $this->total_capacity_bytes) * 100,
            'usage_percentage' => ($this->used_bytes / $this->total_capacity_bytes) * 100,
            'basic_users' => $this->basic_users,
            'standard_users' => $this->standard_users,
            'pro_users' => $this->pro_users,
            'total_users' => $this->basic_users + $this->standard_users + $this->pro_users,
            'accepts_new_users' => $this->accepts_new_users
        ];
    }
}
