<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class OpenAiProject extends Model
{
    protected $table = 'openai_projects';
    protected $fillable = [
        'name',
        'api_key',
        'organization_id',
        'storage_used',
        'is_active'
    ];

    protected $casts = [
        'storage_used' => 'integer',
        'is_active' => 'boolean',
    ];

    public function caseFiles(): HasMany
    {
        return $this->hasMany(CaseFile::class, 'openai_project_id');
    }

    public function capacity(): HasOne
    {
        return $this->hasOne(OpenAiProjectCapacity::class, 'openai_project_id');
    }

    public function userAllocations(): HasMany
    {
        return $this->hasMany(UserStorageAllocation::class, 'openai_project_id');
    }

    /**
     * Get the storage usage as a percentage of the 100GB limit.
     *
     * @return float Storage usage percentage (0-100)
     */
    public function getStorageUsagePercentage(): float
    {
        $limitBytes = 100 * 1024 * 1024 * 1024; // 100GB in bytes
        return ($this->storage_used / $limitBytes) * 100;
    }

    /**
     * Check if the project is nearing storage capacity.
     *
     * @param float $threshold Percentage threshold (default 90%)
     * @return bool True if storage usage exceeds the threshold
     */
    public function isNearingCapacity(float $threshold = 90.0): bool
    {
        return $this->getStorageUsagePercentage() > $threshold;
    }

    /**
     * Get human-readable storage usage information.
     *
     * @return array Storage usage details
     */
    public function getStorageInfo(): array
    {
        $limitBytes = 100 * 1024 * 1024 * 1024; // 100GB in bytes
        $usedGB = round($this->storage_used / (1024 * 1024 * 1024), 2);
        $percentage = $this->getStorageUsagePercentage();

        return [
            'used_bytes' => $this->storage_used,
            'used_gb' => $usedGB,
            'limit_gb' => 100,
            'percentage' => round($percentage, 2),
            'remaining_gb' => round(100 - $usedGB, 2),
            'is_nearing_capacity' => $this->isNearingCapacity()
        ];
    }

    /**
     * Get the available storage space in bytes.
     *
     * @return int Available storage in bytes
     */
    public function getAvailableStorage(): int
    {
        $limitBytes = 100 * 1024 * 1024 * 1024; // 100GB in bytes
        return max(0, $limitBytes - $this->storage_used);
    }
}
