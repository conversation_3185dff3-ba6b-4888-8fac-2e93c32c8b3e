<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class Document extends Model
{
    use HasFactory;

    // Ingestion status constants
    const STATUS_PENDING = 'pending';
    const STATUS_UPLOADING = 'uploading';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUMMARIZING = 'summarizing';
    const STATUS_INDEXING = 'indexing';
    const STATUS_INDEXED = 'indexed';
    const STATUS_FAILED = 'failed';

    // List of all valid statuses
    const VALID_STATUSES = [
        self::STATUS_PENDING,
        self::STATUS_UPLOADING,
        self::STATUS_PROCESSING,
        self::STATUS_SUMMARIZING,
        self::STATUS_INDEXING,
        self::STATUS_INDEXED,
        self::STATUS_FAILED,
    ];

    protected $fillable = [
        'case_file_id',
        'storage_path',
        'original_filename',
        'mime_type',
        'file_size',
        'title',
        'description',
        'transcription',
        'openai_file_id',
        'ingestion_status',
        'ingestion_error',
        'ingested_at',
        'skip_vector_store',
        'document_type',
        'created_by',
        'metadata'
    ];

    protected $casts = [
        'ingested_at' => 'datetime',
        'metadata' => 'array',
    ];

    public function caseFile(): BelongsTo
    {
        return $this->belongsTo(CaseFile::class, 'case_file_id');
    }

    public function draft(): BelongsTo
    {
        return $this->belongsTo(Draft::class);
    }

    public function exhibit()
    {
        return $this->hasOne(Exhibit::class);
    }

    /**
     * Get the research item associated with this document
     */
    public function researchItem()
    {
        return $this->hasOne(LegalResearchItem::class);
    }

    /**
     * Determine if this document is a research report
     */
    public function isResearchReport(): bool
    {
        return $this->document_type === 'research_report';
    }

    /**
     * Get human readable file size
     *
     * @return string
     */
    public function getHumanFileSizeAttribute()
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);

        $bytes /= pow(1024, $pow);

        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * Scope a query to search documents by filename or title
     *
     * @param Builder $query
     * @param string $search
     * @return Builder
     */
    public function scopeSearch(Builder $query, string $search): Builder
    {
        return $query->where(function ($query) use ($search) {
            $query->where('original_filename', 'like', "%{$search}%")
                ->orWhere('title', 'like', "%{$search}%");
        });
    }
}
