<?php

namespace App\Utils;

use App\Models\Document;
use Dompdf\Dompdf;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Settings;
use setasign\Fpdi\Tcpdf\Fpdi;

class ExhibitDocGenerator
{

    public function handle(array $documentIds, string $title = null, ?int $caseFileId = null, ?int $createdBy = null)
    {
        // Make sure temp directory exists
        $tempDir = storage_path('app/temp');
        $title = $title ?: "New Document" . date('YmdHis');
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }

        Log::info('ExhibitDocGenerator starting', [
            'document_ids' => $documentIds,
            'title' => $title,
            'case_file_id' => $caseFileId,
            'created_by' => $createdBy
        ]);



        // Determine which disk to use (default to 's3' but allow configuration)
        $disk = Config::get('filesystems.default', 's3');


        // Check if LibreOffice is available for better conversion
        $libreOfficeCommand = Config::get('services.document_conversion.libreoffice_command', 'soffice');
        $useLibreOffice = Config::get('services.document_conversion.use_libreoffice', true);
        $libreOfficeAvailable = false;

        if ($useLibreOffice) {
            // Test if the command exists
            exec("command -v {$libreOfficeCommand}", $testOutput, $testReturnCode);
            $libreOfficeAvailable = ($testReturnCode === 0);
        }

        // Array to store paths to individual PDFs
        $pdfPaths = [];

        // Process each document
        foreach ($documentIds as $documentId) {
            Log::info('Processing document ID', ['document_id' => $documentId]);

            // Find the document
            $document = Document::find($documentId);

            if (!$document) {
                Log::warning('Document not found', ['document_id' => $documentId]);
                continue;
            }

            Log::info('Found document', [
                'document_id' => $document->id,
                'title' => $document->title,
                'mime_type' => $document->mime_type,
                'storage_path' => $document->storage_path
            ]);


            try {
                $tempPdfPath = null;

                // Check if the file exists in storage
                if (!Storage::disk($disk)->exists($document->storage_path)) {
                    Log::warning('Document file not found in storage', [
                        'document_id' => $document->id,
                        'storage_path' => $document->storage_path,
                        'disk' => $disk
                    ]);
                    continue;
                }

                Log::info('Document file found in storage', [
                    'document_id' => $document->id,
                    'storage_path' => $document->storage_path
                ]);

                // If it's a DOCX file, convert it to PDF
                if ($document->mime_type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {

                    // Create a temporary local copy of the file
                    $tempDocxPath = storage_path('app/temp/' . uniqid() . '.docx');

                    // Get the file contents and save to temp location
                    $fileContents = Storage::disk($disk)->get($document->storage_path);
                    file_put_contents($tempDocxPath, $fileContents);

                    // Generate PDF filename and paths
                    $tempPdfPath = storage_path('app/temp/' . uniqid() . '.pdf');

                    // Try LibreOffice conversion if available
                    if ($libreOfficeAvailable) {
                        $command = "{$libreOfficeCommand} --headless --convert-to pdf --outdir " .
                            escapeshellarg($tempDir) . " " .
                            escapeshellarg($tempDocxPath);

                        exec($command, $output, $returnCode);

                        $expectedPdfPath = $tempDir . '/' . pathinfo($tempDocxPath, PATHINFO_FILENAME) . '.pdf';

                        if ($returnCode === 0 && file_exists($expectedPdfPath)) {
                            $tempPdfPath = $expectedPdfPath;
                        }
                    }

                    // If LibreOffice conversion wasn't attempted or failed, use PHPWord + DomPDF
                    if (!isset($tempPdfPath) || !file_exists($tempPdfPath)) {

                        // Set up PDF renderer
                        Settings::setPdfRendererName(Settings::PDF_RENDERER_DOMPDF);
                        Settings::setPdfRendererPath(base_path('vendor/dompdf/dompdf'));

                        // Load the document
                        $phpWord = IOFactory::load($tempDocxPath);

                        // Save as HTML first with improved styling
                        $tempHtmlPath = storage_path('app/temp/' . uniqid() . '.html');
                        $htmlWriter = IOFactory::createWriter($phpWord, 'HTML');
                        $htmlWriter->save($tempHtmlPath);

                        // Enhance the HTML with better CSS for DomPDF
                        $html = file_get_contents($tempHtmlPath);
                        $enhancedHtml = $this->enhanceHtmlForDomPdf($html);
                        file_put_contents($tempHtmlPath, $enhancedHtml);

                        // Convert HTML to PDF using DomPDF with better options
                        $dompdf = new Dompdf([
                            'defaultFont' => 'Times New Roman',
                            'isRemoteEnabled' => true,
                            'isHtml5ParserEnabled' => true,
                        ]);
                        $dompdf->loadHtml($enhancedHtml);
                        $dompdf->setPaper('A4', 'portrait');
                        $dompdf->render();

                        // Save the PDF to temp location
                        file_put_contents($tempPdfPath, $dompdf->output());

                        // Clean up HTML temp file
                        if (file_exists($tempHtmlPath)) {
                            unlink($tempHtmlPath);
                        }
                    }

                    // Clean up DOCX temp file
                    if (file_exists($tempDocxPath)) {
                        unlink($tempDocxPath);
                    }
                } else if ($document->mime_type === 'application/pdf') {
                    // If it's already a PDF, just download it to a temp location
                    $tempPdfPath = storage_path('app/temp/' . uniqid() . '.pdf');
                    $fileContents = Storage::disk($disk)->get($document->storage_path);
                    file_put_contents($tempPdfPath, $fileContents);
                } else {
                    continue;
                }

                // Create a cover page for the document (with exhibit label or generated UUID)
                $coverPagePath = $this->createExhibitCoverPage($document, $tempDir);

                if ($coverPagePath && $tempPdfPath) {
                    // Merge cover page with document
                    $mergedPdfPath = $this->mergePdfs($coverPagePath, $tempPdfPath, $tempDir);

                    if ($mergedPdfPath && file_exists($mergedPdfPath)) {
                        // Add to our collection of PDFs
                        $pdfPaths[] = $mergedPdfPath;

                        // Clean up individual files
                        if (file_exists($tempPdfPath)) {
                            unlink($tempPdfPath);
                        }
                        if (file_exists($coverPagePath)) {
                            unlink($coverPagePath);
                        }
                    } else {

                        $pdfPaths[] = $tempPdfPath;
                    }
                } else {

                    if ($tempPdfPath) {
                        $pdfPaths[] = $tempPdfPath;
                    }
                }
            } catch (\Exception $e) {

                Log::error('Error processing document', [
                    'document_id' => $documentId,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString()
                ]);
                // Continue with next document
                continue;
            }
        }

        // If we have PDFs to merge
        if (count($pdfPaths) > 0) {
            Log::info('Starting to merge PDFs', ['pdf_count' => count($pdfPaths)]);

            $finalPdfPath = $this->mergeMultiplePdfs($pdfPaths, $tempDir);

            if ($finalPdfPath && file_exists($finalPdfPath)) {
                // Get custom title if provided, or generate a default one
                $customTitle = $title;
                $timestamp = date('Ymd_His');

                if ($customTitle) {
                    // Sanitize the custom title for use in a filename
                    $sanitizedTitle = preg_replace('/[^a-zA-Z0-9_-]/', '_', $customTitle);
                    $finalFilename = "{$sanitizedTitle}_{$timestamp}.pdf";
                } else {
                    $finalFilename = "merged_exhibits_{$timestamp}.pdf";
                }

                $finalStoragePath = "exhibits/{$finalFilename}";

                // Upload the final PDF to storage
                $finalPdfContent = file_get_contents($finalPdfPath);
                Storage::disk($disk)->put($finalStoragePath, $finalPdfContent);

                // Use provided case file ID and created by, or get from the first valid document
                $finalCaseFileId = $caseFileId;
                $finalCreatedBy = $createdBy;

                if (!$finalCaseFileId || !$finalCreatedBy) {
                    foreach ($documentIds as $docId) {
                        $doc = Document::find($docId);
                        if ($doc) {
                            if (!$finalCaseFileId && $doc->case_file_id) {
                                $finalCaseFileId = $doc->case_file_id;
                            }
                            if (!$finalCreatedBy && $doc->created_by) {
                                $finalCreatedBy = $doc->created_by;
                            }
                            if ($finalCaseFileId && $finalCreatedBy) {
                                break;
                            }
                        }
                    }
                }

                if (!$finalCaseFileId) {
                    Log::warning('No case file ID found for merged document', [
                        'document_ids' => $documentIds
                    ]);
                } else {
                    // Create a new document record for the merged PDF
                    $mergedDocument = Document::create([
                        'case_file_id' => $finalCaseFileId,
                        'storage_path' => $finalStoragePath,
                        'original_filename' => $finalFilename,
                        'mime_type' => 'application/pdf',
                        'file_size' => strlen($finalPdfContent),
                        'title' =>  "Merged Exhibits for {$customTitle} - " . date('F d, Y'),
                        'description' => "Merged PDF containing " . count($pdfPaths) . " exhibits",
                        'document_type' => 'exhibit',
                        'created_by' => $finalCreatedBy,
                        'ingestion_status' => 'indexed',
                    ]);

                    Log::info('Created merged document record', [
                        'document_id' => $mergedDocument->id,
                        'case_file_id' => $finalCaseFileId,
                        'title' => $mergedDocument->title,
                        'pdf_count' => count($pdfPaths)
                    ]);



                }





                // Clean up temporary files
                foreach ($pdfPaths as $path) {
                    if (file_exists($path)) {
                        unlink($path);
                    }
                }

                if (!Config::get('app.debug', false) && file_exists($finalPdfPath)) {
                    unlink($finalPdfPath);
                } else {

                }

                Log::info('ExhibitDocGenerator completed successfully');
                return 0;
            } else {
                Log::error('Failed to create final PDF path');
                return 1;
            }
        } else {
            Log::warning('No PDFs to merge - no documents were processed successfully');
            return 1;
        }
    }

    /**
     * Enhance HTML for better DomPDF rendering
     *
     * @param string $html The original HTML
     * @return string Enhanced HTML with better CSS
     */
    protected function enhanceHtmlForDomPdf(string $html): string
    {
        // Fix common encoding issues
        $html = mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8');
        // Add better CSS styling for DomPDF
        $css = <<<CSS
        <style>
            body {
                font-family: 'Times New Roman', Times, serif;
                font-size: 12pt;
                line-height: 1.5;
                margin: 1.5cm;
            }
            h1, h2, h3, h4, h5, h6 {
                margin-top: 1em;
                margin-bottom: 0.5em;
                page-break-after: avoid;
            }
            table {
                border-collapse: collapse;
                width: 100%;
                margin-bottom: 1em;
            }
            table, th, td {
                border: 1px solid #000;
            }
            th, td {
                padding: 5px;
                text-align: left;
            }
            p {
                margin-bottom: 0.5em;
                text-align: justify;
            }
            ul, ol {
                margin-bottom: 1em;
                padding-left: 2em;
            }
            img {
                max-width: 100%;
            }
            .pagebreak {
                page-break-before: always;
            }
        </style>
        CSS;

        // Insert CSS into the head section
        if (strpos($html, '</head>') !== false) {
            $html = str_replace('</head>', $css . '</head>', $html);
        } else {
            // If no head tag, add it
            $html = '<html><head>' . $css . '</head><body>' . $html . '</body></html>';
        }

        return $html;
    }

    /**
     * Create a cover page for an exhibit
     *
     * @param Document $document The document with exhibit
     * @param string $tempDir Directory to save the cover page
     * @return string|false Path to the cover page PDF or false if creation failed
     */
    protected function createExhibitCoverPage(Document $document, string $tempDir)
    {
        try {
            // Get exhibit information
            if ($document->exhibit) {
                $exhibitLabel = $document->exhibit->label;
            } else {
                // Create a shorter, more readable ID for the label if no exhibit exists
                $uuid = Str::uuid()->toString();
                $shortUuid = substr($uuid, 0, 8); // First 8 characters of the UUID
                $exhibitLabel = "AUTO-{$shortUuid}";
            }
            $description = $document->description ?? '';

            // Create a temporary HTML file for the cover page
            $coverHtmlPath = $tempDir . '/' . uniqid() . '_cover.html';

            // Create HTML content for the cover page
            $htmlContent = <<<HTML
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <title>Exhibit Cover Page</title>
                <style>
                    body {
                        font-family: 'Times New Roman', Times, serif;
                        font-size: 16pt;
                        line-height: 1.5;
                        margin: 0;
                        padding: 0;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 100vh;
                    }
                    .cover-content {
                        text-align: center;
                        max-width: 80%;
                    }
                    .exhibit-label {
                        font-size: 24pt;
                        font-weight: bold;
                        margin-bottom: 20px;
                    }
                    .exhibit-description {
                        font-size: 18pt;
                        margin-top: 20px;
                    }
                </style>
            </head>
            <body>
                <div class="cover-content">
                    <div class="exhibit-label">{$exhibitLabel}</div>
                    <hr>
                    <div class="exhibit-description">DESCRIPTION: {$description}</div>
                </div>
            </body>
            </html>
            HTML;

            // Save the HTML content to a file
            file_put_contents($coverHtmlPath, $htmlContent);

            // Convert HTML to PDF using DomPDF
            $dompdf = new Dompdf([
                'defaultFont' => 'Times New Roman',
                'isRemoteEnabled' => true,
                'isHtml5ParserEnabled' => true,
            ]);
            $dompdf->loadHtml($htmlContent);
            $dompdf->setPaper('A4', 'portrait');
            $dompdf->render();

            // Save the PDF
            $coverPdfPath = $tempDir . '/' . uniqid() . '_cover.pdf';
            file_put_contents($coverPdfPath, $dompdf->output());

            // Clean up HTML file
            if (file_exists($coverHtmlPath)) {
                unlink($coverHtmlPath);
            }

            return $coverPdfPath;
        } catch (\Exception $e) {
            $logData = [
                'document_id' => $document->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ];

            // Add exhibit ID to log data if it exists
            if ($document->exhibit) {
                $logData['exhibit_id'] = $document->exhibit->id;
            }

            Log::error('Error creating exhibit cover page', $logData);
            return false;
        }
    }

    /**
     * Merge two PDFs with the first one as the cover page
     *
     * @param string $coverPdfPath Path to the cover page PDF
     * @param string $documentPdfPath Path to the document PDF
     * @param string $tempDir Directory to save the merged PDF
     * @return string|false Path to the merged PDF or false if merging failed
     */
    protected function mergePdfs(string $coverPdfPath, string $documentPdfPath, string $tempDir)
    {
        try {
            // Create a new PDF with FPDI
            $pdf = new Fpdi();

            // Import the cover page
            $pageCount = $pdf->setSourceFile($coverPdfPath);
            for ($i = 1; $i <= $pageCount; $i++) {
                $template = $pdf->importPage($i);
                $pdf->AddPage();
                $pdf->useTemplate($template);
            }

            // Import the document pages
            $pageCount = $pdf->setSourceFile($documentPdfPath);
            for ($i = 1; $i <= $pageCount; $i++) {
                $template = $pdf->importPage($i);
                $pdf->AddPage();
                $pdf->useTemplate($template);
            }

            // Save the merged PDF
            $mergedPdfPath = $tempDir . '/' . uniqid() . '_merged.pdf';
            $pdf->Output($mergedPdfPath, 'F');

            return $mergedPdfPath;
        } catch (\Exception $e) {
            Log::error('Error merging PDFs', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Merge multiple PDFs into a single PDF
     *
     * @param array $pdfPaths Array of paths to PDF files
     * @param string $tempDir Directory to save the merged PDF
     * @return string|false Path to the merged PDF or false if merging failed
     */
    protected function mergeMultiplePdfs(array $pdfPaths, string $tempDir)
    {
        if (empty($pdfPaths)) {
            return false;
        }

        try {
            // Create a new PDF with FPDI
            $pdf = new Fpdi();

            // Import all PDFs
            foreach ($pdfPaths as $index => $pdfPath) {


                // Get page count for this PDF
                $pageCount = $pdf->setSourceFile($pdfPath);

                // Import all pages from this PDF
                for ($i = 1; $i <= $pageCount; $i++) {
                    $template = $pdf->importPage($i);
                    $pdf->AddPage();
                    $pdf->useTemplate($template);
                }
            }

            // Save the merged PDF
            $mergedPdfPath = $tempDir . '/' . uniqid() . '_final_merged.pdf';
            $pdf->Output($mergedPdfPath, 'F');

            return $mergedPdfPath;
        } catch (\Exception $e) {

            Log::error('Error merging multiple PDFs', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
}
