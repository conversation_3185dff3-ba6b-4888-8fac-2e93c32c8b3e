<?php

namespace App\Providers;

use App\Services\VectorStore\VectorStoreManager;
use Illuminate\Support\ServiceProvider;

class VectorStoreServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->app->singleton(VectorStoreManager::class, function ($app) {
            return new VectorStoreManager();
        });
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        //
    }
}
