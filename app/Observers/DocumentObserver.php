<?php

namespace App\Observers;

use App\Models\Document;
use App\Services\UserStorageAllocationService;
use Illuminate\Support\Facades\Log;

class DocumentObserver
{
    /**
     * Handle the Document "created" event.
     * 
     * This increments the storage usage for the OpenAI project
     * when a document is created and assigned to a case.
     */
    public function created(Document $document): void
    {
        $this->updateProjectStorageUsage($document, 'increment');
    }

    /**
     * Handle the Document "updated" event.
     * 
     * This handles changes in file size if the document is replaced.
     */
    public function updated(Document $document): void
    {
        // Check if file_size has changed
        if ($document->isDirty('file_size')) {
            $oldSize = $document->getOriginal('file_size') ?? 0;
            $newSize = $document->file_size ?? 0;
            $sizeDifference = $newSize - $oldSize;

            if ($sizeDifference !== 0) {
                $this->updateProjectStorageUsage($document, $sizeDifference > 0 ? 'increment' : 'decrement', abs($sizeDifference));
            }
        }
    }

    /**
     * Handle the Document "deleted" event.
     * 
     * This decrements the storage usage for the OpenAI project
     * when a document is deleted.
     */
    public function deleted(Document $document): void
    {
        $this->updateProjectStorageUsage($document, 'decrement');
    }

    /**
     * Update the storage usage for the OpenAI project associated with the document.
     * 
     * @param Document $document The document being processed
     * @param string $operation Either 'increment' or 'decrement'
     * @param int|null $customSize Custom size to use instead of document file_size
     */
    private function updateProjectStorageUsage(Document $document, string $operation, ?int $customSize = null): void
    {
        try {
            // Get the case file and its associated OpenAI project
            $caseFile = $document->caseFile;
            if (!$caseFile || !$caseFile->openai_project_id) {
                Log::info('Document has no associated OpenAI project, skipping storage update', [
                    'document_id' => $document->id,
                    'case_file_id' => $caseFile?->id
                ]);
                return;
            }

            $project = $caseFile->openAiProject;
            if (!$project) {
                Log::warning('OpenAI project not found for document', [
                    'document_id' => $document->id,
                    'case_file_id' => $caseFile->id,
                    'openai_project_id' => $caseFile->openai_project_id
                ]);
                return;
            }

            $fileSize = $customSize ?? $document->file_size ?? 0;
            
            if ($fileSize <= 0) {
                Log::info('Document has no file size, skipping storage update', [
                    'document_id' => $document->id,
                    'file_size' => $fileSize
                ]);
                return;
            }

            // Update storage usage
            $bytesChange = $operation === 'increment' ? $fileSize : -$fileSize;

            // Update project storage
            if ($operation === 'increment') {
                $project->increment('storage_used', $fileSize);
            } elseif ($operation === 'decrement') {
                $project->decrement('storage_used', $fileSize);
            }

            // Update user storage allocation
            $user = $caseFile->user;
            if ($user) {
                $storageService = app(UserStorageAllocationService::class);
                $storageService->updateUserStorageUsage($user, $bytesChange);
            }

            Log::info('Updated storage usage', [
                'document_id' => $document->id,
                'project_id' => $project->id,
                'project_name' => $project->name,
                'user_id' => $user?->id,
                'operation' => $operation,
                'file_size' => $fileSize,
                'new_project_storage' => $project->fresh()->storage_used
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update OpenAI project storage usage', [
                'document_id' => $document->id,
                'operation' => $operation,
                'file_size' => $customSize ?? $document->file_size ?? 0,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }
}
