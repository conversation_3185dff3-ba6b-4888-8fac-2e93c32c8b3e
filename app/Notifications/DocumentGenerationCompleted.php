<?php

namespace App\Notifications;

use App\Models\Document;
use App\Models\Draft;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class DocumentGenerationCompleted extends Notification
{
    use Queueable;

    /**
     * The generated document
     */
    protected $document;

    /**
     * The draft used to generate the document
     */
    protected $draft;

    /**
     * Error message if document generation failed
     */
    protected $errorMessage;

    /**
     * Create a new notification instance.
     *
     * @param Document|null $document The generated document (null if generation failed)
     * @param Draft $draft The draft used to generate the document
     * @param string|null $errorMessage Error message if document generation failed
     */
    public function __construct(?Document $document, Draft $draft, ?string $errorMessage = null)
    {
        $this->document = $document;
        $this->draft = $draft;
        $this->errorMessage = $errorMessage;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $message = (new MailMessage)
            ->subject('Document Generation ' . ($this->document ? 'Completed' : 'Failed'));

        Log::info('AYO!!! Document generation completed', [
            'document' => $this->document->id,
            'draft' => $this->draft->case_file_id,
            'error' => $this->errorMessage
        ]);
        if ($this->document) {
            $message->line('Your document "' . $this->document->title . '" has been generated successfully.')
                ->line('Draft: ' . ($this->draft->document_name ?? $this->draft->draft_type))
                ->action('View Document', route('case-files.documents.show', [
                    'case_file' => $this->draft->case_file_id,
                    'document' => $this->document->id
                ]));
        } else {
            $message->line('There was an error generating your document from draft "' .
                    ($this->draft->document_name ?? $this->draft->draft_type) . '".')
                ->line('Error: ' . ($this->errorMessage ?? 'Unknown error'));
        }

        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        $data = [
            'draft_id' => $this->draft->id,
            'draft_title' => $this->draft->document_name ?? $this->draft->draft_type,
            'case_file_id' => $this->draft->case_file_id,
            'success' => $this->document !== null,
        ];

        Log::info('AYO!!! Document generation completed', [
            'document' => $this->document->id,
            'draft' => $this->draft->case_file_id,
            'error' => $this->errorMessage
        ]);

        if ($this->document && $this->document->case_file_id) {
            $data['document_id'] = $this->document->id;
            $data['document_title'] = $this->document->title;
            $data['message'] = 'Your document "' . $this->document->title . '" has been generated successfully.';
           $data['url'] = route('case-files.documents.show', [
               'case_file' => $this->document->case_file_id,
               'document' => $this->document->id
           ]);
        } else {
            $data['message'] = 'There was an error generating your document: ' . ($this->errorMessage ?? 'Unknown error');
        }

        return $data;
    }
}
