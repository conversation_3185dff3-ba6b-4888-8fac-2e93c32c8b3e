<?php

namespace App\Livewire\Chat;

use App\Models\AssistantThread;
use App\Models\AssistantMessage;
use App\Models\CaseFile;
use App\Services\AssistantChatService;
use App\Services\CreditService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Attributes\Validate;
use Livewire\Component;
use Livewire\Attributes\On;
use Livewire\WithFileUploads;

class ChatInterface extends Component
{
    use WithFileUploads;

    public $isChatOpen = false;
    public $message = '';
    public $caseFileId = null;
    public $currentThread = null;
    public $threads = [];
    public $chatMessages = [];
    public $caseFile = null;
    public $isWaitingForResponse = false;
    public $interviewMode = false;
    public $strategyMode = false;
    public $strategyId = null;
    public $interviewPrompt = null;
    public $systemPrompt = null;
    public $insufficientCredits = false;
    public $requiredCredits = 5;
    public $currentBalance = 0;
    /**
     * The name attribute for the textarea element
     * @var string
     */
    public $name = "chat-message";
    /**
     * The height of the textarea element
     * @var string
     */
    public $height = '150px';
    public $queuedFiles = [];
    public $fileObjects = [];
    public $tempUpload;

    // New thread modal properties
    public $showNewThreadModal = false;
    public $newThreadTitle = '';
    public $newThreadDescription = '';
    public $newThreadCategory = '';
    public $newThreadType = '';

    // Delete thread modal properties
    public $showDeleteThreadModal = false;
    public $threadToDelete = null;

    // Add these properties for document selection
    public $showDocumentModal = false;
    public $selectedDocuments = [];
    public $availableDocuments = [];

    // Define validation rules as a property
    protected $rules = [
        'newThreadTitle' => 'required|min:3',
    ];

    protected $listeners = [
        'openChatInterface' => 'open',
        'voice-message-updated' => 'handleVoiceMessageUpdate',
        'set-interview-mode' => 'setInterviewMode',
        'set-strategy-mode' => 'setStrategyMode',
        'set-strategy-id' => 'setStrategyId',
        'set-interview-prompt' => 'setInterviewPrompt',
        'set-system-prompt' => 'setSystemPrompt',
        'restart-interview' => 'restartInterview',
        'selectChatThread' => 'selectThreadById'
    ];

    public function mount($interviewMode = false)
    {
        $this->interviewMode = $interviewMode;
        $this->isChatOpen = false; // Ensure chat is closed by default

        // Check user's credit balance
        $this->checkCreditBalance();
    }

    /**
     * Check if the user has enough credits for AI interactions
     */
    private function checkCreditBalance()
    {
        $creditService = app(CreditService::class);
        $user = Auth::user();
        if ($user) {
            $this->currentBalance = $creditService->getBalance($user);
            $this->insufficientCredits = $this->currentBalance < $this->requiredCredits;
        }
    }

    public function setInterviewMode($mode = true)
    {
        // Log for debugging
        \Log::info('Interview mode set to: ' . ($mode ? 'true' : 'false'));

        $this->interviewMode = $mode;

        // Ensure strategy mode is off when interview mode is on
        if ($mode) {
            $this->strategyMode = false;
        }
    }

    /**
     * Set the strategy mode for the chat interface
     *
     * @param bool $mode Whether to enable strategy mode
     */
    public function setStrategyMode($mode = true)
    {
        // Log for debugging
        \Illuminate\Support\Facades\Log::info('Strategy mode set to: ' . ($mode ? 'true' : 'false'));

        $this->strategyMode = $mode;

        // Ensure interview mode is off when strategy mode is on
        if ($mode) {
            $this->interviewMode = false;
        }
    }

    /**
     * Set the strategy ID for the chat interface
     *
     * @param int|null $strategyId The ID of the strategy
     */
    public function setStrategyId($strategyId)
    {
        // Log for debugging
        \Illuminate\Support\Facades\Log::info('Strategy ID set to: ' . $strategyId);

        $this->strategyId = $strategyId;
    }

    /**
     * Set the interview prompt to be used when starting the interview
     *
     * @param string $prompt The formatted prompt for the AI assistant
     */
    public function setInterviewPrompt($prompt)
    {
        // Log for debugging
        \Log::info('Interview prompt set');

        $this->interviewPrompt = $prompt;
    }

    /**
     * Set the system prompt to be used when creating a new thread
     *
     * @param string $prompt The system instructions for the AI assistant
     */
    public function setSystemPrompt($prompt)
    {
        // Log for debugging
        \Log::info('System prompt set');

        $this->systemPrompt = $prompt;
    }

    /**
     * Reset the assistant to use the default system prompt for regular chat
     */
    protected function resetAssistantToDefault()
    {
        if (!$this->caseFile) {
            return;
        }

        try {
            $assistantService = app(\App\Services\OpenAI\CaseAssistantService::class);
            $assistantService->resetAssistantToDefault($this->caseFile);
            \Log::info('Assistant reset to default prompt for case: ' . $this->caseFile->id);
        } catch (\Exception $e) {
            \Log::error('Failed to reset assistant to default prompt', [
                'case_id' => $this->caseFile->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    #[On('openChatForCase')]
    public function openForCase($caseFileId)
    {
        // Log for debugging
        \Illuminate\Support\Facades\Log::info('Opening chat for case: ' . $caseFileId .
            ', Interview mode: ' . ($this->interviewMode ? 'true' : 'false') .
            ', Strategy mode: ' . ($this->strategyMode ? 'true' : 'false') .
            ', Strategy ID: ' . ($this->strategyId ?? 'null'));

        $this->caseFileId = $caseFileId;

        // Load the full case file model to access its properties
        $this->caseFile = CaseFile::findOrFail($caseFileId);

        $this->loadThreads();
        $this->open();

        // If in interview mode, create a special interview thread if it doesn't exist
        if ($this->interviewMode) {
            // Check if there's already an interview thread
            $interviewThread = AssistantThread::where('case_file_id', $this->caseFileId)
                ->where('type', 'interview')
                ->first();

            if (!$interviewThread) {
                // Create a new thread specifically for the interview
                $chatService = app(AssistantChatService::class);

                try {
                    // If we have a system prompt, use it when creating the thread
                    if (isset($this->systemPrompt) && !empty($this->systemPrompt)) {
                        $thread = $chatService->createThreadWithSystemPrompt(
                            $this->caseFile,
                            Auth::user(),
                            'Fact Gathering Interview',
                            'AI-guided interview to gather case facts',
                            'interview',
                            $this->systemPrompt,
                            AssistantThread::TYPE_INTERVIEW
                        );
                    } else {
                        $thread = $chatService->createThread(
                            $this->caseFile,
                            Auth::user(),
                            'Fact Gathering Interview',
                            'AI-guided interview to gather case facts',
                            'interview',
                            AssistantThread::TYPE_INTERVIEW
                        );
                    }

                    $this->loadThreads();
                    $this->selectThread($thread->id);
                    \Illuminate\Support\Facades\Log::info('Created interview thread. Sending message now ');

                    // Send a simple initial message to start the interview
                    $this->message = 'I\'m ready to begin the fact gathering interview for my case.';
                    $this->sendMessage();
                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('Failed to create interview thread: ' . $e->getMessage() . $e->getTraceAsString());

                    // Show a user-friendly error message
                    $this->dispatch('notify', [
                        'type' => 'error',
                        'message' => 'Unable to start interview. Please try again later.'
                    ]);
                }
            } else {
                // Select the existing interview thread
                $this->selectThread($interviewThread->id);
            }
        }
        // If in strategy mode, create a special strategy thread if it doesn't exist
        else if ($this->strategyMode) {
            // Check if there's already a strategy thread for this strategy ID
            $query = AssistantThread::where('case_file_id', $this->caseFileId)
                ->where('type', 'strategy');

            // If we have a specific strategy ID, use it to find the thread
            if ($this->strategyId) {
                $query->where('strategy_id', $this->strategyId);
            }

            $strategyThread = $query->first();

            if (!$strategyThread) {
                // Create a new thread specifically for the strategy
                $chatService = app(AssistantChatService::class);

                try {
                    // Default system prompt for strategy if none is provided
                    $defaultSystemPrompt = "You are a legal strategy assistant specialized in helping attorneys develop comprehensive case strategies.
                    Your role is to analyze case information that you are provided or have access to, identify legal issues, suggest approaches, and help formulate actionable strategies.
                    The strategy could be for a specific case, a general legal issue, or for how to draft a particular legal document.

                    When asked to develop a strategy, you should:
                    1. Analyze the case facts and legal context
                    2. Identify key legal issues and potential arguments
                    3. Suggest strategic approaches and tactics
                    4. Outline potential risks and how to mitigate them
                    5. Recommend specific action items

                    Your responses should be structured, professional, and focused on practical legal strategy.
                    Do not ask redundant questions. Use the information you have in your vector database to answer questions or make inferences.
                    Remember that the client is not a lawyer and may not understand legal jargon.";

                    // if we have a case summary, get the latest one and add it to the system prompt
                    $latestSummary = $this->caseFile->summaries()->latest()->first();
                    if ($latestSummary) {
                        $defaultSystemPrompt .= "\n\nCASE SUMMARY:\n{$latestSummary->content}";
                    }

                    $systemPromptToUse = isset($this->systemPrompt) && !empty($this->systemPrompt)
                        ? $this->systemPrompt
                        : $defaultSystemPrompt;

                    // Create a title that includes the strategy ID if available
                    $title = $this->strategyId
                        ? 'Strategy #' . $this->strategyId
                        : 'Case Strategy';

                    $thread = $chatService->createThreadWithSystemPrompt(
                        $this->caseFile,
                        Auth::user(),
                        $title,
                        'Thread for developing legal strategy for this case',
                        'strategy',
                        $systemPromptToUse,
                        'strategy',
                        null,
                        ['strategy_id' => $this->strategyId]
                    );

                    $this->loadThreads();
                    $this->selectThread($thread->id);
                    \Illuminate\Support\Facades\Log::info('Created strategy thread');

                    $assistantData = [
                        'assistant_thread_id' => $thread->id,
                        'role' => 'assistant',
                        'content' => "Hello! I'm here to help you develop a legal strategy for your case. Please provide me with the necessary details and I'll assist you in creating a comprehensive strategy.",
                    ];

                    // Store the user message in our database
                    AssistantMessage::create($assistantData);
                    $this->loadMessages();


                } catch (\Exception $e) {
                    \Illuminate\Support\Facades\Log::error('Failed to create strategy thread: ' . $e->getMessage());

                    // Show a user-friendly error message
                    $this->dispatch('notify', [
                        'type' => 'error',
                        'message' => 'Unable to start strategy chat. Please try again later.'
                    ]);
                }
            } else {
                // Select the existing strategy thread
                $this->selectThread($strategyThread->id);
            }
        }
        // If not in interview or strategy mode, reset assistant to default and handle regular chat
        else {
            // Reset assistant to default prompt for regular chat
            $this->resetAssistantToDefault();

            // If no threads exist, open the new thread modal
            if (count($this->threads) === 0) {
                $this->createNewThread();
            }
        }
    }

    public function open()
    {
        $this->isChatOpen = true;
        // When opening the chat, we'll let Alpine.js handle the scrolling
        // via the x-on:open-chat-interface.window event
    }

    public function close()
    {
        $this->isChatOpen = false;
        $this->reset('message', 'showNewThreadModal', 'selectedDocuments');
    }

    public function loadThreads()
    {
        if (!$this->caseFileId) {
            return;
        }

        $this->threads = AssistantThread::where('case_file_id', $this->caseFileId)
            ->whereNot('type', AssistantThread::TYPE_INTERVIEW)
            ->whereNot('type', AssistantThread::TYPE_STRATEGY)
            ->whereNot('type', AssistantThread::TYPE_DOCUMENT_EDITOR)
            ->whereNot('type', AssistantThread::TYPE_RESEARCH)
            ->orderBy('last_activity_at', 'desc')
            ->get();

        // If there are threads but no current thread selected, select the most recent one
        if ($this->threads->count() > 0 && !$this->currentThread) {
            $this->selectThread($this->threads->first()->id);
        }
    }

    public function selectThread($threadId)
    {
        $this->currentThread = AssistantThread::findOrFail($threadId);

        // Reset assistant to default prompt when selecting a regular chat thread
        // (not interview, strategy, document_editor, or research threads)
        if (!in_array($this->currentThread->type, [
            AssistantThread::TYPE_INTERVIEW,
            AssistantThread::TYPE_STRATEGY,
            AssistantThread::TYPE_DOCUMENT_EDITOR,
            AssistantThread::TYPE_RESEARCH
        ])) {
            $this->resetAssistantToDefault();
        }

        $this->loadMessages();
    }

    /**
     * Select a thread by ID and open the chat interface
     * This is used when opening the chat from another component
     *
     * @param int $threadId
     */
    public function selectThreadById($threadId)
    {
        // Find the thread
        $thread = AssistantThread::findOrFail($threadId);

        // Set the case file ID
        $this->caseFileId = $thread->case_file_id;

        // Load the case file
        $this->caseFile = CaseFile::findOrFail($this->caseFileId);

        // Load all threads for this case
        $this->loadThreads();

        // Select the specific thread
        $this->selectThread($threadId);
    }

    public function loadMessages()
    {
        if (!$this->currentThread) {
            $this->chatMessages = [];
            return;
        }

        $this->chatMessages = AssistantMessage::where('assistant_thread_id', $this->currentThread->id)
            ->orderBy('created_at', 'asc')
            ->get();

        // Dispatch an event to trigger scrolling to the bottom
        $this->dispatch('messages-updated');
    }

    public function createNewThread()
    {
        $this->reset(['newThreadTitle', 'newThreadDescription', 'newThreadCategory', 'newThreadType']);
        $this->newThreadType = AssistantThread::TYPE_CHAT; // Default to chat type
        $this->showNewThreadModal = true;
    }

    public function saveNewThread()
    {
        // Validate manually without using the validate() method
        $this->validate([
            'newThreadTitle' => 'required|min:3',
        ]);

        //        dd($this->newThreadType);

        $chatService = app(AssistantChatService::class);
        $caseFile = CaseFile::findOrFail($this->caseFileId);

        try {
            // Reset assistant to default prompt before creating a new regular chat thread
            $threadType = $this->newThreadType ?? AssistantThread::TYPE_CHAT;
            if (!in_array($threadType, [
                AssistantThread::TYPE_INTERVIEW,
                AssistantThread::TYPE_STRATEGY,
                AssistantThread::TYPE_DOCUMENT_EDITOR,
                AssistantThread::TYPE_RESEARCH
            ])) {
                $this->resetAssistantToDefault();
            }

            $thread = $chatService->createThread(
                $caseFile,
                Auth::user(),
                $this->newThreadTitle,
                $this->newThreadDescription,
                $this->newThreadCategory,
                $threadType
            );

            $this->showNewThreadModal = false;
            $this->loadThreads();
            $this->selectThread($thread->id);
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to create thread: ' . $e->getMessage());
        }
    }

    public function cancelNewThread()
    {
        $this->showNewThreadModal = false;
    }

    public function handleVoiceMessageUpdate($message)
    {
        $this->message = $message;
    }

    /**
     * Append transcribed text to the existing message
     */
    public function appendTranscription($transcription): void
    {
        $this->message = trim($this->message . "\n" . $transcription);
        $this->dispatch('voice-message-updated', name: $this->name, message: $this->message);
    }

    public function sendMessage()
    {
        if ((empty(trim($this->message)) && empty($this->selectedDocuments)) || !$this->currentThread) {
            return;
        }

        // Check if user has enough credits
        $this->checkCreditBalance();

        if ($this->insufficientCredits) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => "You need at least {$this->requiredCredits} credits to send messages to the AI assistant. Your current balance is {$this->currentBalance} credits."
            ]);
            return;
        }

        $thread = $this->currentThread;
        $message = $this->message;

        // Process documents if any
        $attachments = [];
        if (!empty($this->selectedDocuments)) {
            // Get the selected documents to check their types
            $documents = \App\Models\Document::whereIn('id', $this->selectedDocuments)->get();

            // Separate images from other documents
            $imageDocuments = [];
            $regularDocuments = [];

            foreach ($documents as $document) {
                if (str_starts_with($document->mime_type, 'image/')) {
                    $imageDocuments[] = $document->id;
                } else {
                    $regularDocuments[] = $document->id;
                }
            }

            // Add regular documents to attachments if any
            if (!empty($regularDocuments)) {
                $attachments['documents'] = $regularDocuments;
            }

            // Add image documents to attachments if any
            if (!empty($imageDocuments)) {
                $attachments['images'] = $imageDocuments;
            }
        }

        $assistantData = [
            'assistant_thread_id' => $thread->id,
            'role' => 'user',
            'content' => $message,
            'attachments' => !empty($attachments) ? json_encode($attachments) : null
        ];

        // Store the user message in our database
        $userMessage = AssistantMessage::create($assistantData);

        try {
            $this->message = '';
            $this->selectedDocuments = []; // Clear selected documents after sending
            $this->isWaitingForResponse = true;
            $this->loadMessages(); // This will dispatch the messages-updated event

            // Dispatch an event to reset document checkboxes in the frontend
            $this->dispatch('reset-document-selection');

            $this->dispatch(
                'send-message-to-assistant',
                userMessageId: $userMessage->id,
                threadId: $thread->id
            )->self();
        } catch (\Exception $e) {
            $this->isWaitingForResponse = false;
            session()->flash('error', 'Failed to send message: ' . $e->getMessage());
        }
    }

    #[On('send-message-to-assistant')]
    public function sendMessageToAssistant($userMessageId, $threadId)
    {
        $userMessage = AssistantMessage::findOrFail($userMessageId);
        $thread = AssistantThread::findOrFail($threadId);

        $chatService = app(AssistantChatService::class);
        $chatService->sendMessageToAssistant($userMessage, $thread);
        $this->isWaitingForResponse = false;

        // Get the current message count before loading new messages
        $currentCount = count($this->chatMessages);

        // Load the messages
        $this->loadMessages(); // This will dispatch the messages-updated event

        // If we have more messages now, it means the assistant responded
        if (count($this->chatMessages) > $currentCount) {
            // Dispatch a specific event for new assistant messages
            $this->dispatch('new-assistant-message');
        }
    }

    public function confirmDeleteThread($threadId)
    {
        $this->threadToDelete = $threadId;
        $this->showDeleteThreadModal = true;
    }

    public function cancelDeleteThread()
    {
        $this->showDeleteThreadModal = false;
        $this->threadToDelete = null;
    }

    public function deleteThread()
    {
        if (!$this->threadToDelete) {
            return;
        }

        try {
            $thread = AssistantThread::findOrFail($this->threadToDelete);

            // Delete associated messages first
            AssistantMessage::where('assistant_thread_id', $thread->id)->delete();

            // Then delete the thread
            $thread->delete();

            // Reset current thread if it was the one deleted
            if ($this->currentThread && $this->currentThread->id === $this->threadToDelete) {
                $this->currentThread = null;
            }

            $this->showDeleteThreadModal = false;
            $this->threadToDelete = null;

            // Reload threads
            $this->loadThreads();

            session()->flash('message', 'Thread deleted successfully.');
        } catch (\Exception $e) {
            session()->flash('error', 'Failed to delete thread: ' . $e->getMessage());
        }
    }

    /**
     * Save the current conversation as a strategy
     * This is used when in strategy mode
     */
    public function saveAsStrategy()
    {
        if (!$this->strategyMode || !$this->currentThread || count($this->chatMessages) === 0) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => __('strategy.notifications.no_conversation')
            ]);
            return;
        }

        try {


            $prompt = "Based on our conversation and the information in the files you have access to about the case, please create a structured legal strategy with the following sections:
            1. EXECUTIVE SUMMARY - A brief overview of the case and recommended strategy
            2. STRATEGY RECOMMENDATIONS - Detailed strategic approaches for this case
            3. LEGAL ANALYSIS - Analysis of key legal issues and potential arguments
            4. ACTION ITEMS - Specific next steps and actions to take

            Format your response as a structured document with clear headings for each section.";

            // Create a message with the prompt
            $message = \App\Models\AssistantMessage::create([
                'assistant_thread_id' => $this->currentThread->id,
                'role' => 'user',
                'content' => "Generate a strategy based on our conversation.",
            ]);

            // Send the message and get the response
            $chatService = app(AssistantChatService::class);
            $response = $chatService->sendMessageToAssistant($message, $this->currentThread, $prompt);

            // Refresh messages
            $this->loadMessages();

            // Get the last assistant message
            $lastMessage = $this->currentThread->messages()
                ->where('role', 'assistant')
                ->latest()
                ->first();

            if ($lastMessage) {
                // If we have a strategy ID, update the existing strategy directly
                if ($this->strategyId) {
                    // Find the strategy
                    $strategy = \App\Models\CaseStrategy::find($this->strategyId);

                    if ($strategy) {
                        // Parse the response to extract sections
                        $sections = $this->parseStrategyResponse($lastMessage->content);

                        // Update the strategy with the new content
                        $strategy->update([
                            'executive_summary' => $sections['executiveSummary'] ?? $strategy->executive_summary,
                            'strategy_data' => ['content' => $sections['strategyData'] ?? ''],
                            'legal_analysis' => ['content' => $sections['legalAnalysis'] ?? ''],
                            'action_items' => $this->parseActionItems($sections['actionItems'] ?? ''),
                            'notes' => $strategy->notes // Keep existing notes
                        ]);

                        return redirect()->route('case-files.strategies.index', ['caseFile' => $strategy->case_file_id, 'strategy' => $strategy->id])->with('success', "Strategy \"{$strategy->title}\" updated successfully.");
                    }
                }

                // If no strategy ID or strategy not found, create a new one
                $this->dispatch('create-strategy-from-conversation', threadId: $this->currentThread->id, strategyId: $this->strategyId);

                $this->dispatch('notify', [
                    'type' => 'success',
                    'message' => __('strategy.notifications.conversation_saved')
                ]);
            } else {
                // No assistant message found
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => __('strategy.notifications.no_response')
                ]);
            }
        } catch (\Exception $e) {
            return redirect()->route('case-files.strategies.index', ['caseFile' => $strategy->case_file_id, 'strategy' => $strategy->id])->with('error', "unable to generate strategy: ");
            \Illuminate\Support\Facades\Log::error('Failed to save strategy from conversation: ' . $e->getMessage());
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => __('strategy.notifications.conversation_save_failed') . ' ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Parse the strategy response from the AI assistant
     *
     * @param string $response The response from the AI assistant
     * @return array The parsed sections of the strategy
     */
    protected function parseStrategyResponse($response)
    {
        // Define the sections we're looking for
        $sections = [
            'EXECUTIVE SUMMARY' => 'executiveSummary',
            'STRATEGY RECOMMENDATIONS' => 'strategyData',
            'LEGAL ANALYSIS' => 'legalAnalysis',
            'ACTION ITEMS' => 'actionItems'
        ];

        $result = [];
        $currentSection = null;

        // Split response by lines
        $lines = explode("\n", $response);

        foreach ($lines as $line) {
            // Check if this line is a section header
            foreach ($sections as $header => $property) {
                if (stripos($line, $header) !== false) {
                    $currentSection = $property;
                    $result[$currentSection] = '';
                    continue 2; // Skip to next line
                }
            }

            // Add content to current section
            if ($currentSection && isset($result[$currentSection])) {
                $result[$currentSection] .= $line . "\n";
            }
        }

        // Trim whitespace from all sections
        foreach ($result as $key => $value) {
            $result[$key] = trim($value);
        }

        return $result;
    }

    /**
     * Parse action items from a string into an array
     *
     * @param string $actionItemsText The text containing action items
     * @return array The parsed action items
     */
    protected function parseActionItems($actionItemsText)
    {
        if (empty($actionItemsText)) {
            return [];
        }

        $items = [];
        $lines = explode("\n", $actionItemsText);

        foreach ($lines as $line) {
            $line = trim($line);
            // Look for lines that start with a number, dash, asterisk, or bullet point
            if (!empty($line) && (preg_match('/^\d+\.\s+(.+)$/', $line, $matches) ||
                                 preg_match('/^[\-\*•]\s+(.+)$/', $line, $matches))) {
                $items[] = isset($matches[1]) ? trim($matches[1]) : trim($line);
            } elseif (!empty($line)) {
                // If it doesn't match a list format but isn't empty, add it anyway
                $items[] = trim($line);
            }
        }

        return $items;
    }

    public function render()
    {
        return view('livewire.chat.chat-interface');
    }

    /**
     * Handle file upload from Alpine.js
     */
    public function uploadFile()
    {
        if (!$this->tempUpload) {
            return;
        }

        // Store the file object
        $index = count($this->fileObjects);
        $this->fileObjects[$index] = $this->tempUpload;

        // Reset the temporary upload
        $this->tempUpload = null;

        // Return the file info to Alpine.js
        return [
            'success' => true,
            'index' => $index
        ];
    }

    /**
     * Remove a file from the queue
     */
    public function removeQueuedFile($index)
    {
        if (isset($this->fileObjects[$index])) {
            unset($this->fileObjects[$index]);
        }
    }

    // Add method to load available documents
    public function loadAvailableDocuments()
    {
        if (!$this->caseFileId) {
            return;
        }

        $this->availableDocuments = \App\Models\Document::where('case_file_id', $this->caseFileId)
            ->orderBy('created_at', 'desc')
            ->get();

        $this->showDocumentModal = true;
    }

    // Add method to toggle document selection
    public function toggleDocument($documentId)
    {
        $index = array_search($documentId, $this->selectedDocuments);
        if ($index !== false) {
            // Remove document if already selected
            unset($this->selectedDocuments[$index]);
            $this->selectedDocuments = array_values($this->selectedDocuments); // Re-index array
            $this->dispatch('unset-checkbox-update', documentId: $documentId);
        } else {
            // Add document if not selected
            $this->selectedDocuments[] = $documentId;
        }
    }

    // Add method to close document modal
    public function closeDocumentModal()
    {
        $this->showDocumentModal = false;
    }

    // Add this method to allow restarting the interview
    public function restartInterview()
    {

        try {
            // Find the interview thread
            $interviewThread = AssistantThread::where('case_file_id', $this->caseFileId)
                ->where('title', 'Fact Gathering Interview')
                ->first();

            if ($interviewThread) {
                // Get the OpenAI thread ID before deleting
                $openaiThreadId = $interviewThread->openai_thread_id;

                // Delete associated messages first
                AssistantMessage::where('assistant_thread_id', $interviewThread->id)->delete();

                // Delete the thread from our database
                $interviewThread->delete();

                // Delete the thread from OpenAI
                if ($openaiThreadId) {
                    try {
                        $caseFile = CaseFile::findOrFail($this->caseFileId);
                        $openAI = \OpenAI::client($caseFile->openaiProject->api_key);
                        $openAI->threads()->delete($openaiThreadId);
                        \Log::info('Deleted OpenAI thread: ' . $openaiThreadId);
                    } catch (\Exception $e) {
                        \Log::error('Failed to delete OpenAI thread: ' . $e->getMessage());
                        // Continue with the restart process even if OpenAI deletion fails
                    }
                }

                // Reset current thread
                $this->currentThread = null;
                $this->chatMessages = [];

                // Create a new interview thread
                $this->openForCase($this->caseFileId);

                // Notify the user
                $this->dispatch('notify', [
                    'type' => 'success',
                    'message' => 'Interview restarted successfully.'
                ]);
            }
        } catch (\Exception $e) {
            \Log::error('Failed to restart interview: ' . $e->getMessage());
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Failed to restart interview: ' . $e->getMessage()
            ]);
        }
    }
}
