<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\CaseFile;
use App\Models\Document;
use App\Jobs\ProcessExhibitJob;
use Livewire\WithPagination;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class DocumentList extends Component
{
    use WithPagination;

    public CaseFile $caseFile;
    public $showingPreviewModal = false;
    public $previewDocument = null;
    public $documentUrls = [];
    public $search = '';

    protected $queryString = ['search'];

    protected $listeners = [
        'document-uploaded' => '$refresh',
        'document-reprocessed' => '$refresh',
        'document-deleted' => '$refresh'
    ];

    public function mount(CaseFile $caseFile)
    {
        $this->caseFile = $caseFile;
    }

    public function getDocumentUrl($documentId)
    {
        if (!isset($this->documentUrls[$documentId])) {
            $document = Document::find($documentId);
            $this->documentUrls[$documentId] = Storage::disk('s3')->temporaryUrl(
                $document->storage_path,
                now()->addMinutes(5)
            );
        }

        return $this->documentUrls[$documentId];
    }

    public function preview($documentId)
    {
        $this->previewDocument = Document::find($documentId);
        $this->getDocumentUrl($documentId); // Generate URL only when previewing
        $this->showingPreviewModal = true;
    }

    public function closePreviewModal()
    {
        $this->showingPreviewModal = false;
        $this->previewDocument = null;
        $this->documentUrls = []; // Clear cached URLs
    }

    /**
     * Retry processing a document that failed to index
     *
     * @param int $documentId
     * @return void
     */
    public function retryProcessDocument($documentId)
    {
        try {
            $document = Document::findOrFail($documentId);

            // Only allow retrying failed documents
            if ($document->ingestion_status !== Document::STATUS_FAILED) {
                session()->flash('error', __('documents.errors.cannot_retry_non_failed'));
                return;
            }

            // Reset the document status to pending
            $document->update([
                'ingestion_status' => Document::STATUS_PENDING,
                'ingestion_error' => null
            ]);

            // Dispatch the job to process the document
            ProcessExhibitJob::dispatch($document);

            // Flash success message
            session()->flash('success', __('documents.document_reprocessing_started'));

            // Clear any cached URLs to force refresh
            $this->documentUrls = [];

            // Reset pagination if needed
            $this->resetPage();

            // Don't dispatch event immediately - let the component refresh naturally
            // The status change will be visible on next render

            Log::info('Document reprocessing started', [
                'document_id' => $document->id,
                'case_file_id' => $document->case_file_id
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to retry document processing', [
                'document_id' => $documentId,
                'error' => $e->getMessage()
            ]);

            session()->flash('error', __('documents.errors.retry_failed', ['message' => $e->getMessage()]));
        }
    }

    public function render()
    {
        $documents = Document::where('case_file_id', $this->caseFile->id)
            ->with('exhibit') // Eager load the exhibit relationship
            ->when($this->search, function ($query, $search) {
                return $query->where(function ($query) use ($search) {
                    $query->where('title', 'like', "%{$search}%")
                        ->orWhere('description', 'like', "%{$search}%")
                        ->orWhere('original_filename', 'like', "%{$search}%");
                });
            })
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('livewire.document-list', [
            'documents' => $documents
        ]);
    }
}
