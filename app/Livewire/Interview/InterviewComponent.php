<?php

namespace App\Livewire\Interview;

use App\Models\CaseSummary;
use Livewire\Attributes\On;
use Livewire\Component;
use App\Models\CaseFile;
use App\Models\Party;
use App\Models\InterviewProgress;
use App\Models\InterviewQuestion;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;

class InterviewComponent extends Component
{
    public $caseFileId;
    public $steps = [
        'intro' => 'Introduction',
        'case_identification' => 'Case Identification',
        'exhibits' => 'Upload Exhibits',
        'parties' => 'Parties Involved',
        'overview' => 'Case Overview',
        'summary' => 'Case Summary',
    ];
    public $currentStep = 0;
    public $completedSteps = [];
    public $interviewData = [
        'case_identification' => [
            'has_active_case' => null,
            'user_role' => null,
            'case_number' => null,
            'considering_legal_action' => false,
            'threatened_with_legal_action' => false,
            'information_gathering' => false,
            'title' => '',
            'case_types' => [],
            'quick_summary' => '',
            'desired_outcome' => '',
            'lawyer_review' => null,
        ],
        'parties' => [],
        'exhibits' => [],
        'overview' => [
            'text' => ''
        ],
        'ai_questions' => [],
        'desired_outcome' => '',
    ];

    // Case type options
    public $caseTypeOptions = [];

    // Add these properties to track analysis state
    public $analyzing = false;
    public $questionsGenerated = false;

    protected $rules = [
        'interviewData.case_identification.has_active_case' => 'required|boolean',
        'interviewData.case_identification.user_role' => 'nullable|required_if:interviewData.case_identification.has_active_case,1|in:plaintiff,defendant',
        'interviewData.case_identification.case_number' => 'nullable|string|max:255',
        'interviewData.case_identification.considering_legal_action' => 'boolean',
        'interviewData.case_identification.threatened_with_legal_action' => 'boolean',
        'interviewData.case_identification.information_gathering' => 'boolean',
        'interviewData.case_identification.title' => 'required|string|max:255',
        'interviewData.case_identification.case_types' => 'required|array|min:1',
        'interviewData.case_identification.quick_summary' => 'required|string',
        'interviewData.case_identification.desired_outcome' => 'required|string',
        'interviewData.case_identification.lawyer_review' => 'required|boolean',
    ];

    protected $messages = [
        'interviewData.case_identification.has_active_case.required' => 'Please select whether you have an active case.',
        'interviewData.case_identification.user_role.required_if' => 'Please select your role in the case.',
        'interviewData.case_identification.case_number.max' => 'The case number must not exceed 255 characters.',
        'interviewData.case_identification.title.required' => 'Please enter a case title.',
        'interviewData.case_identification.title.max' => 'The case title must not exceed 255 characters.',
        'interviewData.case_identification.case_types.required' => 'Please select at least one case type.',
        'interviewData.case_identification.case_types.min' => 'Please select at least one case type.',
        'interviewData.case_identification.quick_summary.required' => 'A quick summary of your case is required before you can proceed.',
        'interviewData.case_identification.desired_outcome.required' => 'A description of your desired outcome is required before you can proceed.',
        'interviewData.case_identification.lawyer_review.required' => 'Please indicate whether you would like an actual lawyer to review your case.',
    ];

    protected $listeners = [
        'party-selected' => 'addParty',
        'previewDocument' => 'previewDocument'
    ];
    public $caseFile;

    public $showingPreviewModal = false;
    public $previewDocument = null;
    public $documentUrl = null;
    public $edit = false;

    public function mount($caseFile)
    {
        $this->caseFileId = $caseFile->id;
        $this->caseFile = $caseFile;

        // Load case type options from language files
        $this->caseTypeOptions = collect(__('case_types'))->toArray();

        // Initialize completed steps
        foreach (array_keys($this->steps) as $step) {
            $this->completedSteps[$step] = false;
        }

        // Mark intro as completed by default
        $this->completedSteps['intro'] = true;

        // If interview is completed and summary exists, mark all steps as completed
        if ($caseFile->interview_status === CaseFile::INTERVIEW_COMPLETED && $caseFile->summaries->count() > 0) {
            foreach (array_keys($this->steps) as $step) {
                $this->completedSteps[$step] = true;
            }

            // Set current step to summary
            $summaryIndex = array_search('summary', array_keys($this->steps));
            if ($summaryIndex !== false) {
                $this->currentStep = $summaryIndex;
            }
        }

        // Initialize interview data with existing case file data
        if ($caseFile->structured_interview_data) {
            $this->interviewData = array_merge($this->interviewData, $caseFile->structured_interview_data);
        }

        // Prefill case data if it exists on the case file
        if (!empty($caseFile->case_number) && empty($this->interviewData['case_identification']['case_number'])) {
            $this->interviewData['case_identification']['case_number'] = $caseFile->case_number;
        }

        if (!empty($caseFile->title) && empty($this->interviewData['case_identification']['title'])) {
            $this->interviewData['case_identification']['title'] = $caseFile->title;
        }

        if (!empty($caseFile->desired_outcome) && empty($this->interviewData['case_identification']['desired_outcome'])) {
            $this->interviewData['case_identification']['desired_outcome'] = $caseFile->desired_outcome;
        }


        if (!empty($caseFile->case_types) && empty($this->interviewData['case_identification']['case_types'])) {
            $this->interviewData['case_identification']['case_types'] = is_array($caseFile->case_types) ? $caseFile->case_types : json_decode($caseFile->case_types, true);
        }

        if (!empty($caseFile->initial_summary) && empty($this->interviewData['case_identification']['quick_summary'])) {
            $this->interviewData['case_identification']['quick_summary'] = $caseFile->initial_summary;
        }

        // Load progress from InterviewProgress if it exists
        $progress = InterviewProgress::where('case_file_id', $this->caseFileId)->first();

        if ($progress) {
            $this->currentStep = $progress->current_step;
            $caseSummary = CaseSummary::where('case_file_id', $this->caseFileId)->first();
            foreach ($progress->completed_steps as $step => $completed) {
                if($step === 'overview' && $caseSummary){
                        $this->currentStep = array_search('summary', array_keys($this->steps));

                } else {
                    $this->completedSteps[$step] = $completed;
                }
            }

//
//            // If step_data exists, merge it with interviewData
//            if (!empty($progress->step_data)) {
//                dd($progress->step_data, $this->interviewData);
//                $this->interviewData = array_merge($this->interviewData, $progress->step_data);
//            }
            $requestedStep  = request()->query('step');
            $edit           = request()->query('edit');

            if ($requestedStep !== null && is_numeric($requestedStep)) {
                $stepIndex = (int) $requestedStep;

                // Validate that the requested step exists and is accessible
                if ($stepIndex >= 0 && $stepIndex < count($this->steps)) {
                    $this->currentStep = $stepIndex;
                }
            }

            $this->edit = $edit;
        }



        // Ensure overview is an array with a text key
        if (!is_array($this->interviewData['overview'])) {
            $this->interviewData['overview'] = ['text' => $this->interviewData['overview']];
        }

        // Initialize the overview text field if it doesn't exist
        if (!isset($this->interviewData['overview']['text'])) {
            $this->interviewData['overview']['text'] = '';
        }

        // Check if questions have been generated for this case file

        // If current step is AI questions but questions haven't been generated, go back to overview step
        if (array_keys($this->steps)[$this->currentStep] === 'ai_questions' && !$this->questionsGenerated) {
            // Find the index of the overview step
            $overviewIndex = array_search('overview', array_keys($this->steps));
            if ($overviewIndex !== false) {
                $this->currentStep = $overviewIndex;
                // Save this change to the progress
                $this->saveProgress();
            }
        }
    }

    public function updated() {
        if(!isset($this->interviewData['case_identification']['case_types'])) {
            $this->interviewData['case_identification']['case_types'] = [];
        }
    }

    public function updatedInterviewDataCaseIdentificationLawyerReview($value): void
    {
        if ($value == 1) {
            $this->dispatch('notify', [
                'message' => __('notifications.lawyer_review_thanks'),
                'type' => 'info'
            ]);
        }
    }

    public function nextStep()
    {
        // Add debugging
        \Log::info('Current step: ' . $this->currentStep);
        \Log::info('Interview data overview: ', [
            'overview' => $this->interviewData['overview'] ?? 'not set',
            'text' => $this->interviewData['overview']['text'] ?? 'not set'
        ]);

        // Validate current step before proceeding
        if (!$this->validateCurrentStep()) {
            \Log::info('Validation failed for step: ' . $this->currentStep);
            return;
        }

        // Mark current step as completed
        $currentStepKey = array_keys($this->steps)[$this->currentStep];
        $this->completedSteps[$currentStepKey] = true;

        // Check if next step would be AI questions
        $nextStepIndex = $this->currentStep + 1;
        $nextStepKey = array_keys($this->steps)[$nextStepIndex] ?? null;

        // If next step is AI questions and questions haven't been generated,
        // skip to the outcome step instead of blocking progress
        if ($nextStepKey === 'ai_questions' && !$this->questionsGenerated) {
            // Find the index of the outcome step
            $outcomeIndex = array_search('outcome', array_keys($this->steps));
            if ($outcomeIndex !== false) {
                $this->currentStep = $outcomeIndex;
                $this->saveProgress();
                return;
            }
        }

        // Move to next step
        $this->currentStep++;

        // Save progress
        $this->saveProgress();

        \Log::info('Successfully moved to step: ' . $this->currentStep);
    }

    public function previousStep()
    {
        if ($this->currentStep > 0) {
            $this->currentStep--;
        }
        $this->saveProgress();
    }

    public function goToStep($stepIndex)
    {
        $stepKey = array_keys($this->steps)[$stepIndex];

        // Only allow navigation to completed steps or the current step
        if ($this->completedSteps[$stepKey] || $stepIndex === $this->currentStep || $stepKey === 'summary' && $this->completedSteps['overview']) {
            $this->currentStep = $stepIndex;
        }
    }

    protected function validateCurrentStep()
    {
        // Get the current step key
        $currentStepKey = array_keys($this->steps)[$this->currentStep];

        \Log::info('Validating step', [
            'currentStep' => $this->currentStep,
            'currentStepKey' => $currentStepKey
        ]);

        if ($currentStepKey === 'case_identification') {
            $this->validate([
                'interviewData.case_identification.has_active_case' => 'required|boolean',
                'interviewData.case_identification.user_role' => 'nullable|required_if:interviewData.case_identification.has_active_case,1|in:plaintiff,defendant',
                'interviewData.case_identification.case_number' => 'nullable|string|max:255',
                'interviewData.case_identification.title' => 'required|string|max:255',
                'interviewData.case_identification.case_types' => 'required|array|min:1',
                'interviewData.case_identification.quick_summary' => 'required|string',
                'interviewData.case_identification.desired_outcome' => 'required|string',
                'interviewData.case_identification.lawyer_review' => 'required|boolean',
            ]);

            // If no active case, at least one alternative must be selected
            if (!$this->interviewData['case_identification']['has_active_case']) {
                $hasAlternative =
                    $this->interviewData['case_identification']['considering_legal_action'] ||
                    $this->interviewData['case_identification']['threatened_with_legal_action'] ||
                    $this->interviewData['case_identification']['information_gathering'];

                if (!$hasAlternative) {
                    $this->addError('interviewData.case_identification.alternative_questions', 'Please select at least one option.');
                    return false;
                }
            }
        } elseif ($currentStepKey === 'overview') {
            // No validation needed for the overview step since it's just a button to proceed
            return true;
        }

        return true;
    }

    public function saveProgress()
    {
        if (!$this->caseFileId) {
            // Create a new case file if one doesn't exist
            $caseFile = CaseFile::create([
                'user_id' => Auth::id(),
                'title' => 'New Case ' . now()->format('Y-m-d'),
                'desired_outcome' => '',
                'status' => 'draft',
                'structured_interview_data' => $this->interviewData,
            ]);

            $this->caseFileId = $caseFile->id;
        } else {
            // Update existing case file
            $caseFile = CaseFile::findOrFail($this->caseFileId);

            // Update case fields if they exist in the interview data
            $updateData = [
                'structured_interview_data' => $this->interviewData,
            ];

            if (!empty($this->interviewData['case_identification']['case_number'])) {
                $updateData['case_number'] = $this->interviewData['case_identification']['case_number'];
            }

            if (!empty($this->interviewData['case_identification']['title'])) {
                $updateData['title'] = $this->interviewData['case_identification']['title'];
            }

            if (!empty($this->interviewData['case_identification']['case_types'])) {
                $updateData['case_types'] = $this->interviewData['case_identification']['case_types'];
            }

            if (!empty($this->interviewData['case_identification']['quick_summary'])) {
                $updateData['initial_summary'] = $this->interviewData['case_identification']['quick_summary'];
            }

            if (!empty($this->interviewData['case_identification']['desired_outcome'])) {
                $updateData['desired_outcome'] = $this->interviewData['case_identification']['desired_outcome'];
            }

            $caseFile->update($updateData);
        }

        // Save or update the interview progress
        InterviewProgress::updateOrCreate(
            ['case_file_id' => $this->caseFileId],
            [
                'current_step' => $this->currentStep,
                'completed_steps' => $this->completedSteps,
                'step_data' => $this->interviewData,
                'last_activity' => now(),
            ]
        );

        if($this->edit) {
            $this->dispatch('notify', [
                'message' => 'Interview progress saved.',
                'type' => 'success'
            ]);
        }
    }

    #[On('party-selected')]
    public function addParty($partyId, $relationship)
    {
        $party = \App\Models\Party::find($partyId);

        if ($party) {
            // Add the party to the interview data
            $this->interviewData['parties'][$partyId] = [
                'id' => $partyId,
                'name' => $party->name,
                'relationship' => $relationship
            ];

            // Save the progress to persist the data
            $this->saveProgress();

            // Notify the user
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => __('notifications.party_added', ['name' => $party->name])
            ]);
        }
    }

    public function removeParty($partyId)
    {
        if (isset($this->interviewData['parties'][$partyId])) {
            // Get the party name before removing it
            $partyName = $this->interviewData['parties'][$partyId]['name'];

            // Remove the party from the interview data
            unset($this->interviewData['parties'][$partyId]);

            // Save the progress to persist the data
            $this->saveProgress();

            // Notify the user
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => "Removed {$partyName} from case parties"
            ]);
        }
    }

    public function updatedInterviewDataOverviewText($value)
    {
        $this->interviewData['overview']['text'] = $value;
        $this->saveProgress();
    }

    #[On('voice-message-updated')]
    public function handleVoiceMessageUpdated($name, $message)
    {
        if ($name === 'case_overview') {
            $this->interviewData['overview']['text'] = $message;
            // Ensure the data is properly updated
            $this->interviewData = $this->interviewData;
        } elseif ($name === 'quick_summary') {
            $this->interviewData['case_identification']['quick_summary'] = $message;
            // Ensure the data is properly updated
            $this->interviewData = $this->interviewData;
        } elseif ($name === 'desired_outcome') {
            $this->interviewData['case_identification']['desired_outcome'] = $message;
            // Ensure the data is properly updated
            $this->interviewData = $this->interviewData;
        }
    }

    // Add a method to directly update the overview text
    public function updateOverviewText($value)
    {
        \Log::info("Updating overview text: {$value}");
        $this->interviewData['overview']['text'] = $value;
    }

    // Add this method to handle the analysis button click
    public function initFactGatheringInterview()
    {
        // Validate the current step first
        if (!$this->validateCurrentStep()) {
            return;
        }


            // Mark current step as completed
            $currentStepKey = array_keys($this->steps)[$this->currentStep];
            $this->completedSteps[$currentStepKey] = true;

            // Generate the interview prompt as a system prompt
            $interviewPrompt = $this->generateInterviewPrompt();

            // Dispatch events to open the chat interface in interview mode with the system prompt
            $this->dispatch('set-system-prompt', $interviewPrompt);
            $this->dispatch('set-interview-mode', true);
            $this->dispatch('openChatForCase', $this->caseFileId);




    }

    public function previewDocument($documentId)
    {
        $this->previewDocument = \App\Models\Document::find($documentId);

        if ($this->previewDocument) {
            // Generate a temporary URL for the document
            $this->documentUrl = \Illuminate\Support\Facades\Storage::disk('s3')->temporaryUrl(
                $this->previewDocument->storage_path,
                now()->addMinutes(5)
            );
        }

        $this->showingPreviewModal = true;
    }

    public function closePreviewModal()
    {
        $this->showingPreviewModal = false;
        $this->previewDocument = null;
        $this->documentUrl = null;
    }

    public function getSelectedCaseTypesCountProperty()
    {
        return isset($this->interviewData['case_identification']['case_types']) && is_array($this->interviewData['case_identification']['case_types'])
            ? count($this->interviewData['case_identification']['case_types'])
            : 0;
    }

    /**
     * Generate a comprehensive prompt for the AI assistant to begin the fact-gathering interview
     *
     * @return string The formatted prompt
     */
    public function  generateInterviewPrompt()
    {
        $prompt = "You are an attorney and a client needs your help. Here's what we know so far about the client's case:\n\n";

        // Add case identification information
        $prompt .= $this->formatCaseIdentificationSection();

        // Add case summary and desired outcome
        $prompt .= $this->formatCaseSummarySection();

        // Add case type information
        $prompt .= $this->formatCaseTypeSection();

        // Add party information
        $prompt .= $this->formatPartiesSection();

        // Add document information
        $prompt .= $this->formatDocumentsSection();

        // Add instructions for the AI assistant
        $prompt .= $this->formatAssistantInstructions();

        return $prompt;
    }

    /**
     * Format case identification information
     *
     * @return string
     */
    protected function formatCaseIdentificationSection()
    {
        $section = "## Case Identification\n";

        // Case title
        $title = $this->interviewData['case_identification']['title'] ?? 'Untitled Case';
        $section .= "**Case Title:** {$title}\n";

        // Case number (if available)
        $caseNumber = $this->interviewData['case_identification']['case_number'] ?? null;
        if (!empty($caseNumber)) {
            $section .= "**Case/Reference Number:** {$caseNumber}\n";
        }

        // Case status
        $section .= "**Case Status:** ";
        if (isset($this->interviewData['case_identification']['has_active_case']) && $this->interviewData['case_identification']['has_active_case']) {
            $role = $this->interviewData['case_identification']['user_role'] ?? 'unknown';
            if ($role === 'plaintiff') {
                $section .= "Client is currently suing someone (Plaintiff)\n";
            } elseif ($role === 'defendant') {
                $section .= "Client is currently being sued (Defendant)\n";
            } else {
                $section .= "Client has an active case\n";
            }
        } else {
            $statuses = [];
            if (!empty($this->interviewData['case_identification']['considering_legal_action'])) {
                $statuses[] = "considering taking legal action";
            }
            if (!empty($this->interviewData['case_identification']['threatened_with_legal_action'])) {
                $statuses[] = "has been threatened with legal action";
            }
            if (!empty($this->interviewData['case_identification']['information_gathering'])) {
                $statuses[] = "gathering information about their legal options";
            }

            if (!empty($statuses)) {
                $section .= "Client is " . implode(" and ", $statuses) . "\n";
            } else {
                $section .= "No active case, client is seeking legal advice\n";
            }
        }

        return $section . "\n";
    }

    /**
     * Format case summary and desired outcome
     *
     * @return string
     */
    protected function formatCaseSummarySection()
    {
        $section = "## Case Summary\n";

        // Initial summary
        $summary = $this->interviewData['case_identification']['quick_summary'] ?? '';
        if (!empty($summary)) {
            $section .= "**Client's Summary:**\n{$summary}\n\n";
        } else {
            $section .= "**Client's Summary:** Not provided\n\n";
        }

        // Desired outcome
        $outcome = $this->interviewData['case_identification']['desired_outcome'] ?? '';
        if (!empty($outcome)) {
            $section .= "**Desired Outcome:**\n{$outcome}\n";
        } else {
            $section .= "**Desired Outcome:** Not specified\n";
        }

        return $section . "\n";
    }

    /**
     * Format selected case types
     *
     * @return string
     */
    protected function formatCaseTypeSection()
    {
        $section = "## Legal Categories\n";

        $selectedTypes = $this->interviewData['case_identification']['case_types'] ?? [];

        if (!empty($selectedTypes)) {
            $section .= "The client has identified the following legal categories for their case:\n\n";

            foreach ($selectedTypes as $type) {
                $description = $this->caseTypeOptions[$type] ?? '';
                $section .= "- **{$type}**: {$description}\n";
            }
        } else {
            $section .= "No specific legal categories have been identified yet.\n";
        }

        return $section . "\n";
    }

    /**
     * Format information about parties involved
     *
     * @return string
     */
    protected function formatPartiesSection()
    {
        $section = "## Parties Involved\n";

        $parties = $this->interviewData['parties'] ?? [];

        if (!empty($parties)) {
            foreach ($parties as $party) {
                $name = $party['name'] ?? 'Unknown';
                $relationship = $party['relationship'] ?? 'unknown';

                // Convert relationship to more readable format
                $relationshipText = ucfirst(str_replace('_', ' ', $relationship));

                $section .= "- **{$name}** - {$relationshipText}\n";
            }
        } else {
            $section .= "No specific parties have been identified yet.\n";
        }

        return $section . "\n";
    }

    /**
     * Format information about uploaded documents
     *
     * @return string
     */
    protected function formatDocumentsSection()
    {
        $section = "## Case Documents\n";

        if (!$this->caseFile || !$this->caseFile->documents || $this->caseFile->documents->count() === 0) {
            return $section . "No documents have been provided by the client.\n\n";
        }

        $section .= "The client has provided the following documents:\n\n";

        foreach ($this->caseFile->documents as $document) {
            // Get the exhibit associated with this document
            $exhibit = \App\Models\Exhibit::where('document_id', $document->id)
                ->where('case_file_id', $this->caseFile->id)
                ->first();

            // Document title or filename
            $title = $document->title ?: ($document->original_filename ?? 'Untitled Document');
            $section .= "- **{$title}**";

            // Add exhibit label if available
            if ($exhibit && !empty($exhibit->label)) {
                $section .= " (Exhibit {$exhibit->label})";
            }

            // Add document summary if available
            if (!empty($document->summary)) {
                $section .= "\n  Summary: {$document->summary}";
            }

            $section .= "\n";
        }

        return $section . "\n";
    }

    /**
     * Format instructions for the AI assistant
     *
     * @return string
     */
    protected function formatAssistantInstructions()
    {
        $caseFile = $this->caseFile;
        $systemPrompt = '';

        // Add vector store and research reminder
        $systemPrompt .= "\n## AVAILABLE RESOURCES\n";

        // Add detailed list of all exhibits in the case file
        $exhibits = $caseFile->exhibits()->with('document')->get();
        if ($exhibits && $exhibits->count() > 0) {
            $systemPrompt .= "### DOCUMENTS ALREADY IN YOUR POSSESSION\n";
            $systemPrompt .= "**IMPORTANT**: The following documents have already been uploaded and are available in your vector store. You have full access to their contents and should reference them directly rather than asking the client to provide them again:\n\n";

            foreach ($exhibits as $exhibit) {
                $document = $exhibit->document;
                $systemPrompt .= "- **Exhibit {$exhibit->label}**: {$document->title}\n";
                $systemPrompt .= "  - Description: {$document->description}\n";
                $systemPrompt .= "  - Document Type: {$document->document_type}\n";
                if (!empty($document->summary)) {
                    $systemPrompt .= "  - Summary: {$document->summary}\n";
                }
                if (!empty($document->metadata)) {
                    $systemPrompt .= "  - Additional Details: " . json_encode($document->metadata) . "\n";
                }
                $systemPrompt .= "\n";
            }

            $systemPrompt .= "**Document Usage Guidelines:**\n";
            $systemPrompt .= "- When you need information that might be in these documents, search your vector store first\n";
            $systemPrompt .= "- Reference specific documents when discussing facts they contain (e.g., \"According to Exhibit A, the contract states...\")\n";
            $systemPrompt .= "- Use document information to ask more informed follow-up questions\n";
            $systemPrompt .= "- If a document seems incomplete or unclear, ask the client for clarification about that specific aspect\n";
            $systemPrompt .= "- Only ask for NEW documents that are not already listed above\n\n";
        } else {
            $systemPrompt .= "No documents have been provided yet. You may need to ask the client for relevant documentation during the interview.\n\n";
        }

        return "## Instructions for the Interview\n"
            . "Based on the information above, please conduct a fact-gathering interview with the client. \n"
            . "Ask specific, relevant questions to better understand their case and gather all necessary information. \n"
            . "Focus on identifying key legal issues, gathering relevant facts, and understanding the client's goals. \n"
            . "Be thorough but conversational, and adapt your questions based on the client's responses.\n\n"
            . "### Interview Approach:\n"
            . "- Ask only 2-3 questions at a time to avoid overwhelming the client\n"
            . "- Use plain language appropriate for someone with little to no legal knowledge\n"
            . "- Build upon the information you already have from the documents in your vector store\n"
            . "- When referencing documents, be specific about which exhibit you're referring to\n"
            . "- use the information in the documents in your vector store to make inferences about certain facts\n"
            . "- Ask for clarification or additional context about information found in documents\n"
            . "- Focus on gathering facts NOT already clearly established in the existing documents\n\n"
            . $systemPrompt
            . $this->formatInterviewConclusionCriteria()
            . $this->formatPostInterviewAnalysisStructure();
    }

    /**
     * Format criteria for concluding the interview
     *
     * @return string
     */
    protected function formatInterviewConclusionCriteria()
    {
        return "## Interview Conclusion Criteria\n"
            . "Conclude the interview when you have gathered sufficient information on:\n"
            . "1. All relevant facts about the incident/situation\n"
            . "2. Timeline of events with specific dates when possible\n"
            . "3. Parties involved and their relationships to the client and each other\n"
            . "4. Client's goals and desired outcomes (both legal and practical)\n"
            . "5. Jurisdiction information (where events occurred, where parties are located)\n"
            . "6. Any potential legal issues or claims identified\n"
            . "7. Available evidence and documentation\n"
            . "8. Any attempts at resolution that have already been made\n\n"
            . "When you determine you have gathered sufficient information in these areas, inform the client that you have enough information to proceed with a legal analysis, and ask if there's anything else they would like to add before concluding.\n\n"
            . "Everytime the client confirms they have nothing more to add, or if they explicitly ask for analysis, state: 'I'll now provide a comprehensive analysis of your case based on the information you've shared.' Then proceed with your analysis.\n\n"
            . "When concluding the interview, include a special marker at the end of your message in this format:\n\n"
            . "---INTERVIEW_ANALYSIS_BEGIN---\n"
            . "{\n"
            . "  \"conclude_interview\": true,\n"
            . "  \"analysis\": {\n"
            . "    \"case_summary\": \"...\",\n"
            . "    \"key_facts\": [\"...\", \"...\"],\n"
            . "    \"jurisdiction_analysis\": \"...\",\n"
            . "    \"legal_issues\": [\"...\", \"...\"],\n"
            . "    \"parties_analysis\": \"...\",\n"
            . "    \"evidence_assessment\": \"...\",\n"
            . "    \"timeline\": \"...\",\n"
            . "    \"strengths_weaknesses\": \"...\",\n"
            . "    \"next_steps\": \"...\",\n"
            . "    \"additional_information_needed\": \"...\"\n"
            . "  }\n"
            . "}\n"
            . "---INTERVIEW_ANALYSIS_END---\n\n"
            . "This marker will not be visible to the user but will be used by our system to process your analysis.\n\n"
            . "If the client provides additional information after you've concluded the interview, respond with: 'Thank you for providing additional information. I'll now update my analysis accordingly.' and then update your analysis with the new information.\n\n"
            . "**CRITICAL**: You must actively use the documents in your vector store throughout the interview and analysis. These documents contain key facts and evidence that should inform your questions and analysis. Reference them by exhibit label when discussing their contents, and use them to corroborate or expand upon the client's statements.\n\n";
    }

    /**
     * Format structure for post-interview analysis
     *
     * @return string
     */
    protected function formatPostInterviewAnalysisStructure()
    {
        return "## Post-Interview Analysis Structure\n"
            . "After concluding the interview, provide a comprehensive analysis with the following sections:\n\n"
            . "1. **Case Summary**: A concise overview of the situation based on all gathered facts (3-5 paragraphs)\n"
            . "2. **Key Facts**: Bullet points of the most important facts established during the interview (10-15 points maximum)\n"
            . "3. **Jurisdiction Analysis**: Identification of relevant jurisdiction(s), applicable law, and any jurisdictional challenges or considerations\n"
            . "4. **Legal Issues Identified**: Potential legal claims, defenses, or issues that arise from the facts\n"
            . "5. **Parties Analysis**: Assessment of each party's role, potential liability, and interests\n"
            . "6. **Evidence Assessment**: Evaluation of available evidence and identification of evidence gaps\n"
            . "7. **Timeline**: Chronological sequence of relevant events with dates when available\n"
            . "8. **Strengths and Weaknesses**: Analysis of the strongest and weakest aspects of the client's position\n"
            . "9. **Next Steps**: Recommended actions for case development, including potential legal strategies\n"
            . "10. **Additional Information Needed**: Any gaps in information that require follow-up\n\n"
            . "Format this analysis in a clear, structured manner using markdown formatting with appropriate headers and bullet points. This analysis will be used for future legal work including research, strategy development, and document drafting.\n\n"
            . $this->formatCaseTypeSpecificGuidance();
    }

    /**
     * Format case-type specific guidance for the interview
     *
     * @return string
     */
    protected function formatCaseTypeSpecificGuidance()
    {
        $selectedTypes = $this->interviewData['case_identification']['case_types'] ?? [];
        if (empty($selectedTypes)) {
            return "";
        }

        $guidance = "## Case-Specific Considerations\n"
            . "### Jurisdiction Considerations\n"
            . "For all case types, be sure to identify and address:\n"
            . "- The jurisdiction(s) where events occurred\n"
            . "- Where the parties are located or incorporated\n"
            . "- Any potential conflicts of law or jurisdictional challenges\n"
            . "- Applicable statutes of limitations\n"
            . "- Relevant local, state, or federal laws that may apply\n\n";

        // Add specific guidance based on case types
        foreach ($selectedTypes as $type) {
            switch ($type) {
                case 'Personal Injury':
                    $guidance .= "### Personal Injury Considerations\n"
                        . "For personal injury cases, be sure to address:\n"
                        . "- Nature and extent of injuries (including medical diagnosis if available)\n"
                        . "- All medical treatment received and planned future treatment\n"
                        . "- Impact on daily life, work, and relationships\n"
                        . "- Economic damages (medical bills, lost wages, etc.)\n"
                        . "- Non-economic damages (pain and suffering, emotional distress)\n\n";
                    break;

                case 'Employment':
                    $guidance .= "### Employment Law Considerations\n"
                        . "For employment cases, be sure to address:\n"
                        . "- Employment history and status (dates, positions, performance reviews)\n"
                        . "- Relevant workplace policies and whether they were followed\n"
                        . "- Any complaints made to HR or management and their responses\n"
                        . "- Potential discrimination, harassment, or retaliation factors\n"
                        . "- Documentation of incidents and communications\n\n";
                    break;

                case 'Contract Dispute':
                    $guidance .= "### Contract Dispute Considerations\n"
                        . "For contract disputes, be sure to address:\n"
                        . "- Contract formation (offer, acceptance, consideration)\n"
                        . "- Key terms and obligations of each party\n"
                        . "- Alleged breaches and their impacts\n"
                        . "- Damages resulting from the breach\n"
                        . "- Any attempts to cure or mitigate\n"
                        . "- Jurisdiction and choice of law provisions in the contract\n"
                        . "- Where the contract was formed and performed\n\n";
                    break;

                case 'Family Law':
                    $guidance .= "### Family Law Considerations\n"
                        . "For family law cases, be sure to address:\n"
                        . "- Marital/relationship history and current status\n"
                        . "- Children involved (ages, custody arrangements, special needs)\n"
                        . "- Financial considerations (assets, debts, income, support)\n"
                        . "- Any history of domestic violence or abuse\n"
                        . "- Previous court orders or agreements\n\n";
                    break;

                case 'Real Estate':
                    $guidance .= "### Real Estate Considerations\n"
                        . "For real estate cases, be sure to address:\n"
                        . "- Property details (location, type, value, condition)\n"
                        . "- Ownership and title information\n"
                        . "- Transaction history and documentation\n"
                        . "- Nature of the dispute (boundary, title, contract, disclosure)\n"
                        . "- Any inspections or appraisals\n"
                        . "- Jurisdiction where the property is located\n"
                        . "- Local zoning laws and regulations that may apply\n\n";
                    break;

                // Add more case types as needed
            }
        }

        return $guidance;
    }

    /**
     * Temporary method to display the generated prompt
     */
    public function showGeneratedPrompt()
    {
        dd($this->generateInterviewPrompt());
    }

    public function render()
    {
        return view('livewire.interview.interview-component');
    }
}
