<?php

namespace App\Livewire\Drafts;

use App\Models\Draft;
use App\Models\CaseFile;
use Livewire\Component;

class DraftEditor extends Component
{
    public $draftId;
    public $caseFileId;
    public $content = '';
    public $autoSave = true;
    public $lastSaved = null;

    public function mount(CaseFile $caseFile, Draft $draft)
    {
        $this->caseFileId = $caseFile->id;
        $this->draftId = $draft->id;

        // Get content from structured_context if available
        $context = $draft->structured_context;
        if (is_array($context) && isset($context['content'])) {
            $this->content = $context['content'];
        }
    }

    public function saveContent()
    {
        $draft = Draft::find($this->draftId);
        if (!$draft) {
            return;
        }

        $structuredContext = is_array($draft->structured_context) ? $draft->structured_context : [];
        $structuredContext['content'] = $this->content;

        $draft->update([
            'structured_context' => $structuredContext
        ]);

        $this->lastSaved = now()->format('g:i:s A');
    }

    public function updatedContent()
    {
        if ($this->autoSave) {
            $this->saveContent();
        }
    }

    public function toggleAutoSave()
    {
        $this->autoSave = !$this->autoSave;
    }

    public function render()
    {
        return view('livewire.drafts.draft-editor');
    }
}
