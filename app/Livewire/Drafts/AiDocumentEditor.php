<?php

namespace App\Livewire\Drafts;

use App\Models\Draft;
use App\Models\CaseFile;
use App\Models\Exhibit;
use App\Models\AssistantThread;
use App\Models\AssistantMessage;
use App\Services\DocumentAiService;
use App\Services\DocumentPromptService;
use App\Services\ExhibitService;
use App\Services\CreditService;
use App\Jobs\ProcessAiResponseJob;
use App\Jobs\ProcessSectionGenerationJob;
use App\Jobs\ProcessFullDocumentGenerationJob;
use App\Events\AiResponseProcessed;
use App\Events\SectionContentGenerated;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Livewire\Component;
use Livewire\Attributes\On;

class AiDocumentEditor extends Component
{
    // Draft properties
    public $draftId;
    public $caseFileId;
    public Draft $draft;
    public CaseFile $caseFile;
    public $content = '';
    public $lastSaved = null;

    // Document structure properties
    public $sections = [];
    public $activeSectionId = null;
    public $activeSectionContent = '';

    // AI Chat properties
    public $aiMessage = '';
    public $chatMessages = [];
    public $isWaitingForResponse = false;
    public $insufficientCredits = false;
    public $requiredCredits = 5;
    public $currentBalance = 0;

    // UI state properties
    public $leftPanelWidth = 40; // percentage
    public $rightPanelWidth = 60; // percentage
    public $isDragging = false;
    public $showExhibitSidebar = false;
    public $aiSuggestions = [];

    // Services
    protected $documentAiService;
    protected $documentPromptService;
    protected $exhibitService;
    protected $creditService;
    protected $assistantThread;

    public $courtDocTypes = ['motion', 'pleading', 'complaint', 'affidavit', 'proposed_order', 'certificate_of_service'];

    protected $listeners = [
        'insertTextAtCursor' => 'handleInsertTextAtCursor',
        'process-ai-response' => 'processAiResponse',
        'process-section-generation' => 'processSectionGeneration',
        'process-document-generation' => 'processDocumentGeneration',
        'voice-message-updated' => 'handleVoiceMessageUpdated',
        //        'echo:draft.{draftId},ai.response.processed' => 'handleAiResponseProcessed'
    ];

    public function boot(
        DocumentAiService $documentAiService,
        DocumentPromptService $documentPromptService,
        ExhibitService $exhibitService,
        CreditService $creditService
    ) {
        $this->documentAiService = $documentAiService;
        $this->documentPromptService = $documentPromptService;
        $this->exhibitService = $exhibitService;
        $this->creditService = $creditService;
    }

    /**
     * Check if the user has enough credits for AI interactions
     */
    private function checkCreditBalance()
    {
        $user = Auth::user();
        if ($user) {
            $this->currentBalance = $this->creditService->getBalance($user);
            $this->insufficientCredits = $this->currentBalance < $this->requiredCredits;
        }
    }

    public function mount(CaseFile $caseFile, Draft $draft)
    {
        $this->caseFileId = $caseFile->id;
        $this->caseFile = $caseFile;
        $this->draftId = $draft->id;
        $this->draft = $draft;

        // Check user's credit balance
        $this->checkCreditBalance();

        // Initialize sections from the draft's sections_structure
        $this->initializeSections();

        // Check if there's a saved active section ID in the database
        if (!empty($this->draft->active_section_id)) {
            // Validate the saved section ID
            $sectionExists = false;
            foreach ($this->sections as $section) {
                if ($section['id'] === $this->draft->active_section_id) {
                    $sectionExists = true;
                    break;
                }
            }

            if ($sectionExists) {
                // Use the saved active section ID
                $this->setActiveSection($this->draft->active_section_id);
                Log::info('Restored active section from database', [
                    'sectionId' => $this->draft->active_section_id
                ]);
            } else {
                Log::warning('Saved active section ID not found in sections', [
                    'savedSectionId' => $this->draft->active_section_id,
                    'availableSections' => array_column($this->sections, 'id')
                ]);
                // Fall back to default behavior
                $this->setDefaultActiveSection();
            }
        } else {
            // No saved active section ID, set the first section as active by default
            $this->setDefaultActiveSection();
        }

        // Initialize the AI service for this draft
        $this->documentAiService->initializeForDraft($draft);

        // Load existing messages if available
        $this->loadChatHistory();
    }

    /**
     * Initialize sections from the draft's sections_structure
     */
    protected function initializeSections()
    {
        // If the draft has a sections_structure, use it
        if ($this->draft->sections_structure) {
            // Handle both array and string formats
            if (is_string($this->draft->sections_structure)) {
                try {
                    $this->sections = json_decode($this->draft->sections_structure, true) ?: [];
                    Log::info('Decoded sections_structure from string', [
                        'count' => count($this->sections),
                        'first_section' => !empty($this->sections) ? $this->sections[0]['id'] : 'none'
                    ]);
                } catch (\Exception $e) {
                    Log::error('Error decoding sections_structure', [
                        'error' => $e->getMessage(),
                        'sections_structure' => $this->draft->sections_structure
                    ]);
                    $this->sections = [];
                }
            } else if (is_array($this->draft->sections_structure)) {
                $this->sections = $this->draft->sections_structure;
                Log::info('Using sections_structure as array', [
                    'count' => count($this->sections),
                    'first_section' => !empty($this->sections) ? $this->sections[0]['id'] : 'none'
                ]);
            } else {
                Log::warning('sections_structure is neither string nor array', [
                    'type' => gettype($this->draft->sections_structure)
                ]);
                $this->sections = [];
            }
        }
        // If the draft has a template, use the template's structure
        else if ($this->draft->template_id && $this->draft->template) {
            $template = $this->draft->template;
            $this->sections = is_array($template->structure) ? $template->structure : [];

            // Initialize with default content if available
            if (is_array($template->default_content)) {
                foreach ($this->sections as &$section) {
                    if (isset($template->default_content[$section['id']])) {
                        $section['content'] = $template->default_content[$section['id']];
                    } else {
                        $section['content'] = '';
                    }
                }
            }

            // Save the initialized sections to the draft
            $this->saveSections();
        }
        // Otherwise, create a basic structure based on the draft type
        else {
            $this->createDefaultSections();
        }
    }

    /**
     * Create default sections based on the draft type
     */
    protected function createDefaultSections()
    {
        $draftType = $this->draft->draft_type;

        switch ($draftType) {
            case 'complaint':
            case 'pleading':
            case 'answer':
                $this->sections = [
                    ['id' => 'caption', 'name' => 'Caption', 'required' => true, 'order' => 1, 'content' => ''],
                    ['id' => 'introduction', 'name' => 'Introduction', 'required' => true, 'order' => 2, 'content' => ''],
                    ['id' => 'jurisdiction_venue', 'name' => 'Jurisdiction and Venue', 'required' => true, 'order' => 3, 'content' => ''],
                    ['id' => 'parties', 'name' => 'Parties', 'required' => true, 'order' => 4, 'content' => ''],
                    ['id' => 'factual_allegations', 'name' => 'Factual Allegations', 'required' => true, 'order' => 5, 'content' => ''],
                    ['id' => 'causes_of_action', 'name' => 'Causes of Action', 'required' => true, 'order' => 6, 'content' => ''],
                    ['id' => 'prayer_for_relief', 'name' => 'Prayer for Relief', 'required' => true, 'order' => 7, 'content' => ''],
                    ['id' => 'signature', 'name' => 'Signature Block', 'required' => true, 'order' => 8, 'content' => ''],
                    ['id' => 'certificate_of_service', 'name' => 'Certificate of Service', 'required' => false, 'order' => 9, 'content' => '']
                ];
                break;

            case 'motion':
                $this->sections = [
                    ['id' => 'caption', 'name' => 'Caption', 'required' => true, 'order' => 1, 'content' => ''],
                    ['id' => 'introduction', 'name' => 'Introduction', 'required' => true, 'order' => 2, 'content' => ''],
                    ['id' => 'background', 'name' => 'Background', 'required' => true, 'order' => 3, 'content' => ''],
                    ['id' => 'legal_standard', 'name' => 'Legal Standard', 'required' => true, 'order' => 4, 'content' => ''],
                    ['id' => 'argument', 'name' => 'Argument', 'required' => true, 'order' => 5, 'content' => ''],
                    ['id' => 'prayer_for_relief', 'name' => 'Prayer for Relief', 'required' => true, 'order' => 6, 'content' => ''],
                    ['id' => 'signature', 'name' => 'Signature Block', 'required' => true, 'order' => 7, 'content' => ''],
                    ['id' => 'certificate_of_service', 'name' => 'Certificate of Service', 'required' => false, 'order' => 8, 'content' => '']
                ];
                break;

            case 'proposed_order':
                $this->sections = [
                    ['id' => 'caption', 'name' => 'Caption', 'required' => true, 'order' => 1, 'content' => ''],
                    ['id' => 'preamble', 'name' => 'Preamble', 'required' => true, 'order' => 3, 'content' => ''],
                    ['id' => 'findings', 'name' => 'Findings of Fact', 'required' => false, 'order' => 4, 'content' => ''],
                    ['id' => 'conclusions', 'name' => 'Conclusions of Law', 'required' => false, 'order' => 5, 'content' => ''],
                    ['id' => 'order_terms', 'name' => 'Order Terms', 'required' => true, 'order' => 6, 'content' => ''],
                    ['id' => 'signature_block', 'name' => 'Signature Block', 'required' => true, 'order' => 7, 'content' => ''],
                    ['id' => 'certificate_of_service', 'name' => 'Certificate of Service', 'required' => false, 'order' => 8, 'content' => '']
                ];
                break;

            case 'certificate_of_service':
                $this->sections = [
                    ['id' => 'caption', 'name' => 'Caption', 'required' => true, 'order' => 1, 'content' => ''],
                    ['id' => 'document_served', 'name' => 'Document Served', 'required' => true, 'order' => 3, 'content' => ''],
                    ['id' => 'service_details', 'name' => 'Service Details', 'required' => true, 'order' => 4, 'content' => ''],
                    ['id' => 'recipients', 'name' => 'Recipients', 'required' => true, 'order' => 5, 'content' => ''],
                    ['id' => 'certification', 'name' => 'Certification', 'required' => true, 'order' => 6, 'content' => ''],
                    ['id' => 'signature', 'name' => 'Signature Block', 'required' => true, 'order' => 7, 'content' => '']
                ];
                break;

            case 'affidavit':
                $this->sections = [
                    ['id' => 'caption', 'name' => 'Caption', 'required' => true, 'order' => 1, 'content' => ''],
                    ['id' => 'affiant_intro', 'name' => 'Affiant Introduction', 'required' => true, 'order' => 3, 'content' => ''],
                    ['id' => 'oath_statement', 'name' => 'Oath Statement', 'required' => true, 'order' => 4, 'content' => ''],
                    ['id' => 'factual_statements', 'name' => 'Factual Statements', 'required' => true, 'order' => 5, 'content' => ''],
                    ['id' => 'signature', 'name' => 'Affiant Signature', 'required' => true, 'order' => 7, 'content' => ''],
                    ['id' => 'notary_block', 'name' => 'Notary Block', 'required' => true, 'order' => 8, 'content' => ''],
                    ['id' => 'certificate_of_service', 'name' => 'Certificate of Service', 'required' => false, 'order' => 9, 'content' => '']
                ];
                break;

            default:
                // Generic document structure
                $this->sections = [
                    ['id' => 'header', 'name' => 'Header', 'required' => true, 'order' => 1, 'content' => ''],
                    ['id' => 'body', 'name' => 'Body', 'required' => true, 'order' => 2, 'content' => ''],
                    ['id' => 'signature', 'name' => 'Signature Block', 'required' => true, 'order' => 3, 'content' => ''],
                ];
                break;
        }

        // Save the default sections to the draft
        $this->saveSections();
    }

    /**
     * Save the sections to the draft
     */
    public function saveSections()
    {
        try {
            // Ensure sections is an array before saving
            if (!is_array($this->sections)) {
                Log::warning('Attempting to save non-array sections', [
                    'type' => gettype($this->sections)
                ]);
                $this->sections = [];
            }

            // Log the sections being saved
            Log::info('Saving sections to draft', [
                'count' => count($this->sections),
                'first_section' => !empty($this->sections) ? $this->sections[0]['id'] : 'none',
                'draft_id' => $this->draft->id
            ]);

            // Update the draft with the sections
            $this->draft->update([
                'sections_structure' => $this->sections
            ]);

            // Verify the sections were saved correctly
            $updatedDraft = $this->draft->fresh();
            $savedSections = $updatedDraft->sections_structure;

            Log::info('Sections saved to database', [
                'saved_type' => gettype($savedSections),
                'saved_count' => is_array($savedSections) ? count($savedSections) : 'not an array'
            ]);

            $this->lastSaved = now()->format('g:i:s A');
        } catch (\Exception $e) {
            Log::error('Error saving sections', [
                'error' => $e->getMessage(),
                'draft_id' => $this->draft->id
            ]);
        }
    }

    /**
     * Set the default active section (first valid section)
     */
    protected function setDefaultActiveSection()
    {
        if (empty($this->sections)) {
            Log::warning('No sections found when setting default active section');
            return;
        }

        // Ensure the first section has a valid ID
        $firstSectionId = $this->sections[0]['id'] ?? null;
        if (!empty($firstSectionId) && $firstSectionId !== 'undefined' && $firstSectionId !== 'null') {
            $this->setActiveSection($firstSectionId);
            Log::info('Set first section as active by default', [
                'sectionId' => $firstSectionId
            ]);
        } else {
            Log::warning('First section has invalid ID', [
                'sections' => $this->sections
            ]);
            // Try to find a valid section
            foreach ($this->sections as $section) {
                if (!empty($section['id']) && $section['id'] !== 'undefined' && $section['id'] !== 'null') {
                    $this->setActiveSection($section['id']);
                    Log::info('Found valid section to set as active', [
                        'sectionId' => $section['id']
                    ]);
                    break;
                }
            }
        }
    }

    /**
     * Set the active section
     */
    public function setActiveSection($sectionId)
    {

        // Validate the section ID
        if (empty($sectionId)) {
            Log::warning('Attempted to set empty section ID');
            return;
        }

        // Log the section change
        Log::info('Setting active section', [
            'sectionId' => $sectionId,
            'previousSectionId' => $this->activeSectionId
        ]);

        $this->activeSectionId = $sectionId;
        Log::alert('is array?', [
            "is_array" => is_array($this->sections)
        ]);

        // Make sure sections is an array
        if (!is_array($this->sections)) {
            Log::warning('Sections is not an array in setActiveSection', [
                'type' => gettype($this->sections)
            ]);

            // Try to decode if it's a string
            if (is_string($this->sections)) {
                try {
                    $this->sections = json_decode($this->sections, true) ?: [];
                } catch (\Exception $e) {
                    Log::error('Error decoding sections in setActiveSection', [
                        'error' => $e->getMessage()
                    ]);
                    $this->sections = [];
                }
            } else {
                $this->sections = [];
            }
        }

        // Find the section content
        $sectionFound = false;
        foreach ($this->sections as $section) {
            if ($section['id'] === $sectionId) {
                $sectionFound = true;
                $content = $section['content'] ?? '';

                // Check if the content is already a Delta JSON string
                $this->activeSectionContent = $content;
                Log::info('Found section content', [
                    'sectionId' => $sectionId,
                    'contentLength' => strlen($content),
                    'isJson' => is_string($content) && $this->isJsonString($content),
                    'content' => (!is_string($content) || !$this->isJsonString($content)) ? ($content ?: '(empty)') : '[JSON content]'
                ]);
                break;
            }
        }

        if (!$sectionFound) {
            Log::warning('Section not found in sections array', [
                'sectionId' => $sectionId,
                'sectionCount' => count($this->sections),
                'availableSections' => array_column($this->sections, 'id')
            ]);
            $this->activeSectionContent = '';
        }

        // Save the active section ID to the database
        try {

            $this->draft->update([
                'active_section_id' => $sectionId
            ]);
            Log::info('Saved active section ID to database', [
                'draftId' => $this->draft->id,
                'sectionId' => $sectionId
            ]);
        } catch (\Exception $e) {
            Log::error('Error saving active section ID to database', [
                'error' => $e->getMessage(),
                'draftId' => $this->draft->id,
                'sectionId' => $sectionId
            ]);
        }

        // Log the editor ID that will be used
        $editorId = 'quill-editor-' . $sectionId;
        Log::info('Editor ID for active section', [
            'editorId' => $editorId
        ]);

        // Dispatch an event to force the editor to update with the content

        // Also update the content in the editor directly
        Log::alert("Active section content: " . $this->activeSectionContent);
        $this->dispatch(
            'update-editor-content',
            editorId: $editorId,
            content: $this->activeSectionContent
        );
    }

    /**
     * Listen for caption content updated event
     */
    #[\Livewire\Attributes\On('caption-content-updated')]
    public function captionContentUpdated($data)
    {
        // Update the active section content with the caption form data
        if ($this->activeSectionId === 'caption' && isset($data['content'])) {
            Log::info('Caption content updated event received', [
                'contentLength' => strlen($data['content'])
            ]);

            $this->activeSectionContent = $data['content'];

            // Parse the content to get the caption data
            $captionData = json_decode($data['content'], true);
            if (json_last_error() === JSON_ERROR_NONE && is_array($captionData)) {
                // Update the draft with the caption data
                $this->draft->update([
                    'caption_data' => $captionData
                ]);

                Log::info('Updated draft caption_data from event', [
                    'draftId' => $this->draft->id
                ]);
            }

            // Also update the section content for backward compatibility
            $this->updateSectionContent();
        }
    }

    /**
     * Update the active section content
     */
    public function updateSectionContent($noRefresh = false)
    {
        // Log the method call
        Log::info('updateSectionContent called', [
            'activeSectionId' => $this->activeSectionId,
            'contentLength' => $this->activeSectionId ? strlen($this->activeSectionContent) : 0,
            'isJson' => $this->activeSectionId && is_string($this->activeSectionContent) ? $this->isJsonString($this->activeSectionContent) : false
        ]);

        if (empty($this->activeSectionId)) {
            Log::warning('No active section when trying to update content');
            return;
        }

        // Ensure activeSectionId is not undefined or null
        if ($this->activeSectionId === 'undefined' || $this->activeSectionId === 'null') {
            Log::error('Invalid section ID:', ['activeSectionId' => $this->activeSectionId]);
            return;
        }

        // Make sure sections is an array
        if (!is_array($this->sections)) {
            Log::warning('Sections is not an array in updateSectionContent', [
                'type' => gettype($this->sections)
            ]);

            // Try to decode if it's a string
            if (is_string($this->sections)) {
                try {
                    $this->sections = json_decode($this->sections, true) ?: [];
                } catch (\Exception $e) {
                    Log::error('Error decoding sections in updateSectionContent', [
                        'error' => $e->getMessage()
                    ]);
                    $this->sections = [];
                }
            } else {
                $this->sections = [];
            }
        }

        // Check if this is a caption section for a motion or pleading
        $isCaptionSection = $this->activeSectionId === 'caption';
        $isMotionOrPleading = in_array($this->draft->draft_type, ['motion', 'pleading']);
        $useCaptionForm = $isCaptionSection && $isMotionOrPleading;

        // Check if content is a Quill Delta JSON string
        $isQuillDelta = is_string($this->activeSectionContent) && $this->isJsonString($this->activeSectionContent);

        // Log the section type and content
        Log::info('Section type information', [
            'isCaptionSection' => $isCaptionSection,
            'isMotionOrPleading' => $isMotionOrPleading,
            'useCaptionForm' => $useCaptionForm,
            'isQuillDelta' => $isQuillDelta,
            'contentLength' => strlen($this->activeSectionContent)
        ]);

        // For caption sections, make sure we have content
        if ($useCaptionForm && empty($this->activeSectionContent)) {
            Log::warning('Caption section has empty content. This may indicate the form data was not properly synchronized.');
        }

        // If content is a Quill Delta JSON string, make sure it's properly formatted
        if ($isQuillDelta) {
            try {
                $jsonData = json_decode($this->activeSectionContent, true);

                // Check if it's a valid Quill Delta format with 'ops' array
                if (!isset($jsonData['ops']) || !is_array($jsonData['ops'])) {
                    Log::warning('Content is JSON but not a valid Quill Delta format', [
                        'section_id' => $this->activeSectionId,
                        'has_ops' => isset($jsonData['ops']),
                        'ops_is_array' => isset($jsonData['ops']) && is_array($jsonData['ops'])
                    ]);
                } else {
                    Log::info('Content is a valid Quill Delta format', [
                        'section_id' => $this->activeSectionId,
                        'ops_count' => count($jsonData['ops'])
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Error parsing JSON content', [
                    'error' => $e->getMessage(),
                    'section_id' => $this->activeSectionId
                ]);
            }
        }

        // Update the section content in the sections array
        $sectionFound = false;
        foreach ($this->sections as &$section) {
            if ($section['id'] === $this->activeSectionId) {
                $section['content'] = $this->activeSectionContent;
                $sectionFound = true;
                Log::info('Section content updated in sections array', [
                    'sectionId' => $section['id'],
                    'sectionName' => $section['name'] ?? 'unnamed',
                    'contentLength' => strlen($this->activeSectionContent),
                    'isJson' => is_string($this->activeSectionContent) && $this->isJsonString($this->activeSectionContent)
                ]);
                break;
            }
        }

        if (!$sectionFound) {
            Log::warning('Section not found in sections array', [
                'activeSectionId' => $this->activeSectionId,
                'sectionCount' => count($this->sections),
                'availableSections' => array_column($this->sections, 'id')
            ]);

            // If section not found, add it to the sections array
            $this->sections[] = [
                'id' => $this->activeSectionId,
                'name' => 'Section ' . count($this->sections) + 1,
                'content' => $this->activeSectionContent,
                'order' => count($this->sections) + 1,
                'required' => false
            ];

            Log::info('Added new section to sections array', [
                'sectionId' => $this->activeSectionId,
                'sectionCount' => count($this->sections)
            ]);
        }

        // Save the updated sections
        $this->saveSections();
        Log::info('Sections saved to database');
        if ($noRefresh) {
            return;
        }

        // Dispatch an event to notify that the section content has been updated
        // $this->dispatch('section-content-updated', sectionId: $this->activeSectionId);
        Log::info('section-content-updated event dispatched', [
            'sectionId' => $this->activeSectionId
        ]);

        // Also dispatch the section-content-loaded event to ensure the editor is updated
        $this->dispatch(
            'section-content-loaded',
            sectionId: $this->activeSectionId,
            content: $this->activeSectionContent
        );
        Log::info('section-content-loaded event dispatched for content update');

        // Also dispatch the direct update event
        $editorId = 'quill-editor-' . $this->activeSectionId;
        $this->dispatch(
            'update-editor-content',
            editorId: $editorId,
            content: $this->activeSectionContent
        );
        Log::info('update-editor-content event dispatched');
    }

    /**
     * Load chat history from the assistant thread
     */
    protected function loadChatHistory()
    {
        // Get the thread associated with this draft
        $thread = AssistantThread::where('case_file_id', $this->caseFile->id)
            ->where('type', 'document_editor')
            ->where('metadata->draft_id', $this->draft->id)
            ->first();

        if ($thread) {
            $this->assistantThread = $thread;

            // Load messages from the thread
            $messages = AssistantMessage::where('assistant_thread_id', $thread->id)
                ->orderBy('created_at', 'asc')
                ->get()
                ->map(function ($message) {
                    return [
                        'role' => $message->role,
                        'content' => $message->content,
                        'timestamp' => $message->created_at->format('g:i A')
                    ];
                })
                ->toArray();

            $this->chatMessages = $messages;
        }
    }

    /**
     * Send a message to the AI assistant
     */
    public function sendAiMessage()
    {
        if (empty(trim($this->aiMessage))) {
            return;
        }

        // Check if user has enough credits
        $this->checkCreditBalance();

        if ($this->insufficientCredits) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => "You need at least {$this->requiredCredits} credits to send messages to the AI assistant. Your current balance is {$this->currentBalance} credits."
            ]);
            return;
        }

        // Store the message for processing
        $userPrompt = $this->aiMessage;

        // Add the user message to the chat immediately
        $this->chatMessages[] = [
            'role' => 'user',
            'content' => $userPrompt,
            'timestamp' => now()->format('g:i A')
        ];

        // Clear the input field immediately
        $this->aiMessage = '';

        // Set waiting state immediately for better UX
        $this->isWaitingForResponse = true;

        // Scroll to bottom of chat (will be handled by JavaScript)
        $this->dispatch('scrollChatToBottom');

        // Process the AI response directly
        $this->processAiResponse($userPrompt);
    }

    /**
     * Process the AI response (triggered by event)
     */
    #[On('process-ai-response')]
    public function processAiResponse($prompt)
    {
        // Set waiting state
        $this->isWaitingForResponse = true;

        try {
            // Get document context
            $context = $this->getDocumentContext();

            // Add prompt type to context
            $context['prompt_type'] = 'user_request';

            // Log the active section and content length for debugging
            Log::info('Dispatching AI response job', [
                'active_section_id' => $this->activeSectionId,
                'active_section_content_length' => strlen($this->activeSectionContent),
                'prompt_preview' => substr($prompt, 0, 100)
            ]);


            // Dispatch the job to process the AI response asynchronously
            ProcessAiResponseJob::dispatch(
                $this->draft->id,
                $prompt,
                $context,
                Auth::id()
            );

            // The rest of the processing will happen in the handleAiResponseProcessed method
            // when the job completes and broadcasts the result
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error dispatching AI response job: ' . $e->getMessage(), [
                'exception' => $e,
                'draft_id' => $this->draft->id
            ]);

            // Add error message to chat
            $this->chatMessages = array_filter($this->chatMessages, function ($message) {
                return !isset($message['is_thinking']) || !$message['is_thinking'];
            });

            $this->chatMessages[] = [
                'role' => 'assistant',
                'content' => 'Sorry, I encountered an error while processing your request. Please try again.',
                'timestamp' => now()->format('g:i A'),
                'is_error' => true
            ];

            // Reset the waiting state
            $this->isWaitingForResponse = false;
        }
    }

    /**
     * Generate content for the active section
     */
    public function generateSectionContent()
    {
        if (!$this->activeSectionId) {
            return;
        }

        // Check if user has enough credits
        $this->checkCreditBalance();

        if ($this->insufficientCredits) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => "You need at least {$this->requiredCredits} credits to generate content. Your current balance is {$this->currentBalance} credits."
            ]);
            return;
        }

        // Find the active section
        $activeSection = null;
        foreach ($this->sections as $section) {
            if ($section['id'] === $this->activeSectionId) {
                $activeSection = $section;
                break;
            }
        }

        if (!$activeSection) {
            return;
        }

        // Determine section type from name
        $sectionType = $this->getSectionTypeFromName($activeSection['name']);

        // Get exhibits for context
        $exhibits = $this->getExhibits()->map(function ($exhibit) {
            return [
                'label' => $exhibit->label,
                'description' => $exhibit->description
            ];
        })->toArray();

        // Create variables for prompt
        $variables = [
            'document_type' => $this->draft->draft_type,
            'section_name' => $activeSection['name'],
            'exhibits' => $exhibits
        ];

        // Get the prompt for this section type
        $prompt = $this->documentPromptService->getSectionPrompt($sectionType, 'generate', $variables);

        $activeSection = ucwords($activeSection['name']);
        $userDisplayPrompt = "Generate content for the {$activeSection} section.";

        // Add the prompt to the chat
        $this->chatMessages[] = [
            'role' => 'user',
            'content' => $userDisplayPrompt,
            'timestamp' => now()->format('g:i A')
        ];

        // Set waiting state immediately for better UX
        $this->isWaitingForResponse = true;

        // Scroll to bottom of chat
        $this->dispatch('scrollChatToBottom');

        // Dispatch event to process the section generation
        $this->dispatch(
            'process-section-generation',
            prompt: $prompt,
            sectionId: $this->activeSectionId
        )->self();
    }

    /**
     * Process section content generation
     */
    #[On('process-section-generation')]
    public function processSectionGeneration($prompt, $sectionId)
    {
        // Set waiting state
        $this->isWaitingForResponse = true;

        try {
            // Get document context
            $context = $this->getDocumentContext();

            // Add prompt type to context
            $context['prompt_type'] = 'section_generation';

            // Log the active section and content length for debugging
            Log::info('Dispatching section generation job', [
                'section_id' => $sectionId,
                'active_section_id' => $this->activeSectionId,
                'prompt_preview' => substr($prompt, 0, 100)
            ]);

            // Dispatch the job to process the section generation asynchronously
            ProcessSectionGenerationJob::dispatch(
                $this->draft->id,
                $prompt,
                $context,
                $sectionId,
                Auth::id()
            );

            // The rest of the processing will happen in the handleSectionContentGenerated method
            // when the job completes and broadcasts the result
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error dispatching section generation job: ' . $e->getMessage(), [
                'exception' => $e,
                'draft_id' => $this->draft->id,
                'section_id' => $sectionId
            ]);

            // Add error message to chat
            $this->chatMessages[] = [
                'role' => 'assistant',
                'content' => 'Sorry, I encountered an error while generating content for this section. Please try again.',
                'timestamp' => now()->format('g:i A'),
                'is_error' => true
            ];

            // Reset waiting state
            $this->isWaitingForResponse = false;

            // Scroll to bottom of chat
            $this->dispatch('scrollChatToBottom');
        }
    }

    /**
     * Handle the section content generated event from Pusher
     */
    public function handleSectionContentGenerated($data)
    {
        Log::info('Received section content from Pusher in Livewire component', [
            'draft_id' => $data['draftId'],
            'section_id' => $data['sectionId'],
            'has_error' => isset($data['response']['error']),
            'is_completion' => isset($data['chatMessage']['is_completion']),
            'method_called' => true
        ]);

        // Dispatch a browser console log for debugging
        $this->dispatch('consoleLog', ['message' => 'Livewire received section content', 'data' => $data]);

        // Add the AI response to the chat
        $this->chatMessages[] = $data['chatMessage'];

        // If there was an error, set waiting state to false and return
        if (isset($data['response']['error'])) {
            $this->isWaitingForResponse = false;
            return;
        }

        // Check if this is the completion message for the full document generation
        if (isset($data['chatMessage']['is_completion']) && $data['chatMessage']['is_completion'] === true) {
            Log::info('Full document generation complete');
            $this->isWaitingForResponse = false;

            // Show a prominent success notification
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'Full document generation complete! All sections have been processed.',
                'duration' => 10000 // Show for 10 seconds
            ]);

            // Also add a success message to the chat
            $this->chatMessages[] = [
                'role' => 'system',
                'content' => '✅ Full document generation is now complete! All sections have been processed. You can now review and edit each section as needed.',
                'timestamp' => now()->format('g:i A'),
                'is_success' => true
            ];

            // Scroll to bottom of chat
            $this->dispatch('scrollChatToBottom');
            return;
        }

        $response = $data['response'];
        $sectionId = $data['sectionId'];

        // Get the document content from the response
        $documentContent = isset($response['document_content']) ? $response['document_content'] : '';
        Log::alert("response", [
            'response' => $response
        ]);
        // Log the response structure for debugging
        Log::info('Section generation response structure', [
            'has_document_content' => isset($response['document_content']),
            'response_keys' => array_keys($response),
            'document_content_length' => isset($response['document_content']) ? strlen(json_encode($response['document_content'])) : 0
        ]);

        // Log document content preview
        if (!empty($documentContent)) {
            Log::debug('Section content from AI (via Pusher)', [
                'section_id' => $sectionId,
                'content' => $documentContent ,
                'is_json' => $this->isJsonString($documentContent)
            ]);

//            if (!$this->isJsonString($documentContent)) {
//                $documentContent = json_encode($documentContent);
//            }

            Log::info('CAPTION CONTENT!!', [
                'contents' => $documentContent
            ]);

        } else {
            // Try to extract document content from other response fields if available
            if (isset($response['content']) && !empty($response['content'])) {
                Log::info('Using content field instead of document_content', [
                    'section_id' => $sectionId
                ]);
                $documentContent = $response['content'];
            } else if (isset($data['chatMessage']['content']) && !empty($data['chatMessage']['content'])) {
                Log::info('Using chatMessage content as fallback', [
                    'section_id' => $sectionId
                ]);
                $documentContent = $data['chatMessage']['content'];
            } else {
                Log::warning('Section generation response did not contain any usable content', [
                    'section_id' => $sectionId,
                    'response_keys' => isset($response) ? array_keys($response) : [],
                    'data_keys' => array_keys($data)
                ]);
                $this->isWaitingForResponse = false;
                return;
            }
        }

        // Special handling for caption sections
        if ($sectionId === 'caption') {
//            Log::info('Processing AI-generated caption content', [
//                'content_length' => strlen($documentContent),
//                'content_preview' => substr($documentContent, 0, 100)
//            ]);

            // Try to parse the content as JSON
            try {
                // Clean up the content - remove any markdown code block markers
                $cleanContent = $documentContent;
//                Log::alert('CLEAN CONTENT', $cleanContent);

                $jsonData = json_decode($cleanContent, true);

                if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
                    // Successfully parsed JSON

                    // Add format identifier if missing
                    if (!isset($jsonData['_format'])) {
                        $jsonData['_format'] = 'caption_data_v1';
                        $jsonData['_type'] = 'structured_caption';
                        $cleanContent = json_encode($jsonData);
                    }

                    // Update the draft with the caption data
                    $this->draft->update([
                        'caption_data' => $jsonData
                    ]);

                    Log::info('Updated draft caption_data directly', [
                        'draftId' => $this->draft->id,
                        'keys' => array_keys($jsonData)
                    ]);

                    // If we're still on the caption section, update it
                    if ($this->activeSectionId === $sectionId) {
                        $this->activeSectionContent = $cleanContent;

                        // Save the updated content to the database (for backward compatibility)
                        $this->updateSectionContent();

                        // Also dispatch an event to update the caption form
                        $this->dispatch('caption-content-updated', [
                            'content' => $cleanContent
                        ]);

                        // Show a success notification
                        $this->dispatch('notify', [
                            'type' => 'success',
                            'message' => 'Caption updated successfully'
                        ]);
                    } else {
                        // If section changed, add as a suggestion
                        $this->aiSuggestions[] = [
                            'content' => $cleanContent,
                            'type' => 'section_content',
                            'section_id' => $sectionId,
                            'timestamp' => now()->format('g:i A')
                        ];


                        // Dispatch event to switch to editor tab
                        $this->dispatch('switchToEditorTab');

                        // Show a notification about the new suggestion
                        $this->dispatch('notify', [
                            'type' => 'info',
                            'message' => 'New caption suggestion available'
                        ]);
                    }
                } else {
                    // JSON parsing failed, log the error
                    Log::warning('Failed to parse AI-generated caption as JSON', [
                        'error' => json_last_error_msg(),
                        'content' => $documentContent
                    ]);

                    // Add a message to the chat about the format issue
                    $this->chatMessages[] = [
                        'role' => 'assistant',
                        'content' => 'I generated caption content, but it wasn\'t in the expected JSON format. Please try again or fill the caption form manually.',
                        'timestamp' => now()->format('g:i A'),
                        'is_warning' => true
                    ];
                }
            } catch (\Exception $e) {
                Log::error('Error processing caption JSON', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                    'content' => $documentContent
                ]);
            }
        } else {
            // Regular section handling (non-caption)
            if (true || $this->activeSectionId === $sectionId) {
                $this->setActiveSection($sectionId);
                // Update the section content
                $this->activeSectionContent = $documentContent;

                // Log before updating section content
                Log::info('Updating section content', [
                    'section_id' => $sectionId,
                    'content_length' => strlen($documentContent)
                ]);



                // Save the updated content to the database
                $this->updateSectionContent();

                // Show a success notification
                $this->dispatch('notify', [
                    'type' => 'success',
                    'message' => 'Content Applied. ' . $sectionId
                ]);

            } else {
                // If section changed, add content as a suggestion instead
                if (!empty($documentContent)) {
                    $this->aiSuggestions[] = [
                        'content' => $documentContent,
                        'type' => 'section_content',
                        'section_id' => $sectionId,
                        'timestamp' => now()->format('g:i A')
                    ];

                    // Log that suggestion was added
                    Log::info('Added section content suggestion', [
                        'suggestion_count' => count($this->aiSuggestions),
                        'for_section' => $sectionId,
                        'active_section' => $this->activeSectionId
                    ]);


                }
            }
        }

        // Reset waiting state
        $this->isWaitingForResponse = false;
        // Dispatch event to switch to editor tab
        $this->dispatch('switchToEditorTab');

        // Scroll to bottom of chat
        $this->dispatch('scrollChatToBottom');
    }

    /**
     * Determine section type from section name
     */
    protected function getSectionTypeFromName($name)
    {
        $name = strtolower($name);

        if ($name === 'caption') {
            return 'caption';
        } elseif (str_contains($name, 'intro') || str_contains($name, 'introduction')) {
            return 'introduction';
        } elseif (str_contains($name, 'fact') || str_contains($name, 'statement of facts')) {
            return 'facts';
        } elseif (str_contains($name, 'argument') || str_contains($name, 'legal argument')) {
            return 'legal_argument';
        } elseif (str_contains($name, 'conclusion')) {
            return 'conclusion';
        } elseif (str_contains($name, 'prayer') || str_contains($name, 'relief')) {
            return 'prayer';
        } elseif (str_contains($name, 'cause') || str_contains($name, 'action')) {
            return 'causes_of_action';
        } else {
            return 'generic';
        }
    }

    /**
     * Generate the entire document by processing each section individually
     */
    public function generateEntireDocument()
    {
        // Check if user has enough credits
        $this->checkCreditBalance();

        if ($this->insufficientCredits) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => "You need at least {$this->requiredCredits} credits to generate content. Your current balance is {$this->currentBalance} credits."
            ]);
            return;
        }

        // Add a message to the chat
        $this->chatMessages[] = [
            'role' => 'user',
            'content' => 'Please generate content for all sections of the document.',
            'timestamp' => now()->format('g:i A')
        ];

        // Add a "thinking" message to the chat
        $this->chatMessages[] = [
            'role' => 'system',
            'content' => 'Starting full document generation. I will process each section individually so this may take some time. I will inform you when each section is complete. You may apply each section using the suggestions button ...',
            'timestamp' => now()->format('g:i A'),
            'is_thinking' => true
        ];

        // Show a notification that the process has started
        $this->dispatch('notify', [
            'type' => 'info',
            'message' => 'Full document generation has started. Please do not leave this page until you receive a completion notification.'
        ]);

        // Set waiting state immediately for better UX
        $this->isWaitingForResponse = true;

        // Scroll to bottom of chat
        $this->dispatch('scrollChatToBottom');

        try {
            // Get document context
            $context = $this->getDocumentContext();

            // Add prompt type to context
            $context['prompt_type'] = 'document_generation';

            // Log the document generation request
            Log::info('Starting full document generation using section-by-section approach', [
                'draft_id' => $this->draft->id,
                'draft_type' => $this->draft->draft_type,
                'section_count' => count($this->sections)
            ]);

            // Import the job class


            // Dispatch the job to process each section asynchronously
            ProcessFullDocumentGenerationJob::dispatch(
                $this->draft->id,
                $this->sections,
                $context,
                Auth::id()
            );

            // The rest of the processing will happen as each section is generated
            // and the SectionContentGenerated events are broadcast
        } catch (\Exception $e) {
            // Log the error
            Log::error('Error dispatching full document generation job: ' . $e->getMessage(), [
                'exception' => $e,
                'draft_id' => $this->draft->id
            ]);

            // Add error message to chat
            $this->chatMessages = array_filter($this->chatMessages, function ($message) {
                return !isset($message['is_thinking']) || !$message['is_thinking'];
            });

            $this->chatMessages[] = [
                'role' => 'assistant',
                'content' => 'Sorry, I encountered an error while starting the document generation. Please try again.',
                'timestamp' => now()->format('g:i A'),
                'is_error' => true
            ];

            // Reset the waiting state
            $this->isWaitingForResponse = false;

            // Show an error notification
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Error starting document generation: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Process entire document generation
     */
    #[On('process-document-generation')]
    public function processDocumentGeneration($prompt)
    {
        $this->isWaitingForResponse = true;

        try {
            // Get document context
            $context = $this->getDocumentContext();

            // Add prompt type to context
            $context['prompt_type'] = 'document_generation';

            // Log the document generation request
            Log::info('Processing document generation request', [
                'draft_id' => $this->draft->id,
                'draft_type' => $this->draft->draft_type,
                'prompt_length' => strlen($prompt)
            ]);

            // Send to AI service
            $response = $this->documentAiService->generateContent($prompt, $context);

            // Log the response for debugging
            Log::info('AI document generation response processed', [
                'has_document_content' => isset($response['document_content']) && !empty($response['document_content']),
                'document_content_length' => isset($response['document_content']) ? strlen($response['document_content']) : 0,
                'has_explanation' => isset($response['changes_explanation']) && !empty($response['changes_explanation'])
            ]);

            // Add the AI response to the chat
            // Make sure we're only storing the explanation part in the chat
            $explanation = isset($response['changes_explanation']) ? $response['changes_explanation'] : 'Response generated by AI assistant.';
            $this->chatMessages[] = [
                'role' => 'assistant',
                'content' => $explanation,
                'timestamp' => now()->format('g:i A')
            ];

            // Parse the response into sections
            $documentContent = isset($response['document_content']) ? $response['document_content'] : '';
            if (!empty($documentContent)) {
                // Log document content preview
                Log::debug('Document generation content', [
                    'content_preview' => substr($documentContent, 0, 100) . '...',
                    'content_length' => strlen($documentContent)
                ]);

                // Parse the content into sections
                $this->parseSectionsFromResponse($documentContent);

                // Show a success notification
                $this->dispatch('notify', [
                    'type' => 'success',
                    'message' => 'Document generated successfully'
                ]);
            } else {
                Log::warning('Document generation response did not contain document content');

                // Show a warning notification
                $this->dispatch('notify', [
                    'type' => 'warning',
                    'message' => 'Document generation did not produce content'
                ]);
            }
        } catch (\Exception $e) {
            // Add error message to chat
            $this->chatMessages[] = [
                'role' => 'assistant',
                'content' => 'Sorry, I encountered an error while generating the document. Please try again.',
                'timestamp' => now()->format('g:i A'),
                'is_error' => true
            ];

            // Log the error
            Log::error('AI Document Generation Error: ' . $e->getMessage(), [
                'exception' => $e,
                'draft_id' => $this->draft->id,
                'prompt' => $prompt
            ]);

            // Show an error notification
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Error generating document: ' . $e->getMessage()
            ]);
        } finally {
            $this->isWaitingForResponse = false;

            // Scroll to bottom of chat
            $this->dispatch('scrollChatToBottom');
        }
    }

    /**
     * Parse sections from AI response
     */
    protected function parseSectionsFromResponse($response)
    {
        // Split the response by section headers
        $lines = explode("\n", $response);
        $currentSection = null;
        $sectionContent = '';

        foreach ($lines as $line) {
            // Check if this line is a section header
            if (
                preg_match('/^#+\s+(.+)$/i', $line, $matches) ||
                preg_match('/^([A-Z][A-Z\s]+):$/i', $line, $matches)
            ) {

                // If we were already processing a section, save it
                if ($currentSection !== null) {
                    $this->updateSectionByName($currentSection, trim($sectionContent));
                    $sectionContent = '';
                }

                // Start a new section
                $currentSection = $matches[1];
            } else {
                // Add this line to the current section content
                $sectionContent .= $line . "\n";
            }
        }

        // Save the last section
        if ($currentSection !== null) {
            $this->updateSectionByName($currentSection, trim($sectionContent));
        }

        // Update the draft in the database
        $this->saveSections();
    }

    /**
     * Update section by name
     */
    protected function updateSectionByName($sectionName, $content)
    {
        // Find a section with a similar name
        foreach ($this->sections as &$section) {
            $lowerSectionName = strtolower($section['name']);
            $lowerInputName = strtolower($sectionName);

            if (
                str_contains($lowerSectionName, $lowerInputName) ||
                str_contains($lowerInputName, $lowerSectionName)
            ) {
                // Special handling for caption sections
                if ($section['id'] === 'caption') {
                    Log::info('Processing caption section from full document generation', [
                        'content_length' => strlen($content),
                        'content_preview' => substr($content, 0, 100)
                    ]);

                    // Try to extract JSON from the content
                    if (
                        preg_match('/```json\s*({[\s\S]*?})\s*```/s', $content, $matches) ||
                        preg_match('/{\s*"courtName"[\s\S]*?}/s', $content, $matches)
                    ) {

                        $jsonContent = $matches[1] ?? $matches[0];

                        // Try to parse the JSON
                        try {
                            $jsonData = json_decode($jsonContent, true);

                            if (json_last_error() === JSON_ERROR_NONE && is_array($jsonData)) {
                                // Successfully parsed JSON
                                Log::info('Successfully extracted caption JSON from full document', [
                                    'keys' => array_keys($jsonData)
                                ]);

                                // Update the section with the JSON content
                                $section['content'] = $jsonContent;

                                // If this is the active section, update the content
                                if ($section['id'] === $this->activeSectionId) {
                                    $this->activeSectionContent = $jsonContent;

                                    // Save the updated content to the database
                                    $this->updateSectionContent();

                                    // Also dispatch an event to update the caption form
                                    $this->dispatch('caption-content-updated', [
                                        'content' => $jsonContent
                                    ]);
                                }

                                return;
                            }
                        } catch (\Exception $e) {
                            Log::error('Error parsing caption JSON from full document', [
                                'error' => $e->getMessage(),
                                'content' => $jsonContent
                            ]);
                        }
                    }

                    // If we couldn't extract valid JSON, log a warning
                    Log::warning('Could not extract valid JSON for caption section', [
                        'content' => $content
                    ]);

                    // Don't update the caption section with invalid content
                    return;
                } else {
                    // Regular section handling
                    $section['content'] = $content;

                    // If this is the active section, update the content
                    if ($section['id'] === $this->activeSectionId) {
                        $this->activeSectionContent = $content;
                    }

                    return;
                }
            }
        }
    }

    /**
     * Get document context for AI
     */
    protected function getDocumentContext()
    {
        // Prepare document context for AI
        $context = [
            'document_type' => $this->draft->draft_type,
            'sections' => $this->sections,
            'active_section' => $this->activeSectionId,
        ];

        // Add reference draft content if available
        if (!empty($this->draft->metadata) && !empty($this->draft->metadata['reference_draft_id'])) {
            $referenceDraftId = $this->draft->metadata['reference_draft_id'];
            $referenceDraft = \App\Models\Draft::find($referenceDraftId);

            if ($referenceDraft) {
                $context['reference_draft'] = [
                    'id' => $referenceDraft->id,
                    'draft_type' => $referenceDraft->draft_type,
                    'description' => $referenceDraft->description,
                ];

                // Add reference draft sections content
                if (!empty($referenceDraft->sections_structure) && is_array($referenceDraft->sections_structure)) {
                    $context['reference_draft']['sections'] = [];

                    foreach ($referenceDraft->sections_structure as $section) {
                        if (!empty($section['id']) && !empty($section['content'])) {
                            $context['reference_draft']['sections'][$section['id']] = [
                                'name' => $section['name'] ?? '',
                                'content' => $section['content']
                            ];
                        }
                    }
                }
            }
        }

        return $context;
    }

    /**
     * Handle inserting text at cursor position (event-based approach)
     * This is a legacy method that uses events, but we now prefer the direct approach
     */
    public function handleInsertTextAtCursor($text)
    {
        // Log the method call
        Log::info('handleInsertTextAtCursor called', [
            'textLength' => strlen($text),
            'activeSectionId' => $this->activeSectionId
        ]);

        // Ensure the text is properly sanitized
        $text = trim($text);

        // Log the editor ID that would be used
        $editorId = $this->activeSectionId ? 'section-editor-' . $this->activeSectionId : 'none';
        Log::info('Editor ID for text insertion', [
            'editorId' => $editorId,
            'exists' => $this->activeSectionId ? 'yes' : 'no'
        ]);

        // Make sure we have an active section
        if (!$this->activeSectionId) {
            // If no active section, show an error notification
            Log::warning('No active section when trying to insert text');
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Please select a section first'
            ]);
            return;
        }

        // Directly update the active section content
        // If there's existing content, append the new content
        if (!empty($this->activeSectionContent)) {
            Log::info('Appending text to existing content', [
                'existingLength' => strlen($this->activeSectionContent),
                'newTextLength' => strlen($text)
            ]);
            $this->activeSectionContent .= "\n\n" . $text;
        } else {
            Log::info('Setting content for empty section');
            $this->activeSectionContent = $text;
        }

        // Update the section in the database
        $this->updateSectionContent();
        Log::info('Section content updated successfully');

        // Show a success notification
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Content inserted successfully'
        ]);
    }

    /**
     * Insert content directly from a suggestion
     */
    public function insertContentDirectly($index)
    {
        // Log the method call
        Log::info('insertContentDirectly called', [
            'index' => $index,
            'activeSectionId' => $this->activeSectionId,
            'suggestionCount' => count($this->aiSuggestions)
        ]);

        // Make sure we have an active section
        if (!$this->activeSectionId) {
            Log::warning('No active section when trying to insert content directly');
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Please select a section first'
            ]);
            return;
        }

        // Make sure the suggestion exists
        if (!isset($this->aiSuggestions[$index])) {
            Log::warning('Suggestion not found', ['index' => $index]);
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Suggestion not found'
            ]);
            return;
        }

        // Get the suggestion content
        $content = $this->aiSuggestions[$index]['content'];
        $suggestionType = $this->aiSuggestions[$index]['type'] ?? 'unknown';
        $suggestionSectionId = $this->activeSectionId;
        $this->setActiveSection($suggestionSectionId);

        Log::info('Found suggestion content', [
            'contentLength' => strlen($content),
            'suggestionType' => $suggestionType,
            'suggestionSectionId' => $suggestionSectionId,
            'activeSectionId' => $this->activeSectionId,
            'isJson' => $this->isJsonString($content)
        ]);

        // If the suggestion has a section_id and it's different from the active section,
        // switch to that section first
        if ($suggestionSectionId && $suggestionSectionId !== $this->activeSectionId) {
            Log::info('Switching to suggestion section', [
                'from' => $this->activeSectionId,
                'to' => $suggestionSectionId
            ]);
            $this->setActiveSection($suggestionSectionId);
        }

        $this->activeSectionContent = $content;

        // Update the section in the database
        try {
            $this->updateSectionContent();
            Log::info('Section content updated successfully from suggestion');

            // If this is a caption section, also dispatch the caption-content-updated event
            if ($this->activeSectionId === 'caption' && $this->isJsonString($this->activeSectionContent)) {
                Log::info('Dispatching caption-content-updated event');
                $this->dispatch('caption-content-updated', [
                    'content' => $this->activeSectionContent
                ]);
            }

            // Remove the suggestion
            $this->removeSuggestion($index);
            Log::info('Suggestion removed', ['index' => $index]);

            // Show a success notification
            $this->dispatch('notify', [
                'type' => 'success',
                'message' => 'Content inserted successfully'
            ]);
        } catch (\Exception $e) {
            Log::error('Error updating section content', [
                'error' => $e->getMessage(),
                'section_id' => $this->activeSectionId
            ]);

            // Show an error notification
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Error updating content: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Get exhibits for the current case file
     */
    public function getExhibits()
    {
        return Exhibit::where('case_file_id', $this->caseFile->id)
            ->orderBy('sort_order')
            ->get();
    }

    /**
     * Toggle exhibit sidebar
     */
    public function toggleExhibitSidebar()
    {
        $this->showExhibitSidebar = !$this->showExhibitSidebar;
    }

    /**
     * Remove a suggestion from the AI suggestions list
     */
    public function removeSuggestion($index)
    {
        if (isset($this->aiSuggestions[$index])) {
            unset($this->aiSuggestions[$index]);
            $this->aiSuggestions = array_values($this->aiSuggestions); // Re-index the array
        }
    }

    /**
     * Apply a section content suggestion
     */
    public function applySectionSuggestion($index)
    {
        // Log the method call
        Log::info('applySectionSuggestion called', [
            'index' => $index,
            'suggestionCount' => count($this->aiSuggestions)
        ]);

        if (!isset($this->aiSuggestions[$index])) {
            Log::warning('Suggestion not found', ['index' => $index]);
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Suggestion not found'
            ]);
            return;
        }

        $suggestion = $this->aiSuggestions[$index];
        Log::info('Found suggestion', [
            'type' => $suggestion['type'] ?? 'unknown',
            'section_id' => $suggestion['section_id'] ?? 'none',
            'content_length' => isset($suggestion['content']) ? strlen($suggestion['content']) : 0,
            'is_json' => isset($suggestion['content']) && $this->isJsonString($suggestion['content'])
        ]);

        if ($suggestion['type'] === 'section_content' && isset($suggestion['section_id'])) {
            // Set the section as active
            Log::info('Setting active section', [
                'from' => $this->activeSectionId,
                'to' => $suggestion['section_id']
            ]);
            $this->setActiveSection($suggestion['section_id']);

            // Update the section content
            $this->activeSectionContent = $suggestion['content'];

            try {
                // Log before updating
                Log::info('Updating section content from suggestion', [
                    'section_id' => $suggestion['section_id'],
                    'content_length' => strlen($suggestion['content'])
                ]);

                $this->updateSectionContent();

                // If this is a caption section, also dispatch the caption-content-updated event
                if ($suggestion['section_id'] === 'caption' && $this->isJsonString($suggestion['content'])) {
                    Log::info('Dispatching caption-content-updated event');
                    $this->dispatch('caption-content-updated', [
                        'content' => $suggestion['content']
                    ]);
                }

                // Remove the suggestion
                $this->removeSuggestion($index);
                Log::info('Suggestion removed', ['index' => $index]);

                // Show a success notification
                $this->dispatch('notify', [
                    'type' => 'success',
                    'message' => 'Content applied to section successfully'
                ]);
            } catch (\Exception $e) {
                Log::error('Error updating section content', [
                    'error' => $e->getMessage(),
                    'section_id' => $suggestion['section_id']
                ]);

                // Show an error notification
                $this->dispatch('notify', [
                    'type' => 'error',
                    'message' => 'Error updating content: ' . $e->getMessage()
                ]);
            }
        } else {
            Log::warning('Invalid suggestion type or missing section_id', [
                'type' => $suggestion['type'] ?? 'unknown',
                'has_section_id' => isset($suggestion['section_id'])
            ]);

            // Show a warning notification
            $this->dispatch('notify', [
                'type' => 'warning',
                'message' => 'Cannot apply this suggestion to a section'
            ]);
        }
    }

    /**
     * Handle voice message updated event
     */
    public function handleVoiceMessageUpdated($name, $message)
    {
        // Handle the voice message updated event based on the input name
        if ($name === 'ai-chat-message') {
            $this->aiMessage = $message;
        }
    }

    /**
     * Check if a string is a valid JSON string
     *
     * @param string $string The string to check
     * @return bool Whether the string is valid JSON
     */
    protected function isJsonString($string)
    {
        if (!is_string($string)) {
            return false;
        }

        // Quick check for JSON format
        if (!preg_match('/^\s*[\[{].*[\]}]\s*$/', $string)) {
            return false;
        }

        // Use json_decode and check for errors
        json_decode($string);
        return (json_last_error() === JSON_ERROR_NONE);
    }

    /**
     * Test method to insert sample text directly
     */
    public function testInsertText()
    {
        // Make sure we have an active section
        if (!$this->activeSectionId) {
            $this->dispatch('notify', [
                'type' => 'error',
                'message' => 'Please select a section first'
            ]);
            return;
        }

        // Sample text to insert
        $sampleText = "This is a test paragraph inserted directly.\n\nIt contains multiple lines of text to demonstrate that line breaks are preserved correctly.\n\n1. This is a numbered list item\n2. This is another numbered list item\n\n* This is a bullet point\n* This is another bullet point";

        // Directly update the active section content
        if (!empty($this->activeSectionContent)) {
            $this->activeSectionContent .= "\n\n" . $sampleText;
        } else {
            $this->activeSectionContent = $sampleText;
        }

        // Update the section in the database
        $this->updateSectionContent();

        // Show a success notification
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Test text inserted successfully'
        ]);
    }

    /**
     * Parse Quill Delta content to plain text
     *
     * @param string $content The content to parse
     * @return string The plain text extracted from the Quill Delta
     */
    protected function parseQuillDeltaToText($content)
    {
        if (!is_string($content) || !$this->isJsonString($content)) {
            return $content;
        }

        try {
            $jsonObj = json_decode($content, true);
            if (isset($jsonObj['ops']) && is_array($jsonObj['ops'])) {
                // Extract text from Quill Delta format
                $plainText = '';
                foreach ($jsonObj['ops'] as $op) {
                    if (isset($op['insert']) && is_string($op['insert'])) {
                        $plainText .= $op['insert'];
                    }
                }
                return $plainText;
            }
        } catch (\Exception $e) {
            Log::warning('Failed to parse Quill Delta content', [
                'error' => $e->getMessage(),
                'content_preview' => substr($content, 0, 100)
            ]);
        }

        return $content;
    }

    public function render()
    {
        return view('livewire.drafts.ai-document-editor');
    }
    /**
     * Handle the AI response processed event
     *
     * @param array $data
     * @return void
     */
    public function handleAiResponseProcessed($data)
    {
        Log::info('Received AI response from Pusher in Livewire component', [
            'draft_id' => $data['draftId'],
            'has_error' => isset($data['response']['error']),
            'method_called' => true
        ]);

        // Dispatch a browser console log for debugging
        $this->dispatch('consoleLog', ['message' => 'Livewire received AI response', 'data' => $data]);

        // Remove the "thinking" message if it exists
        $this->chatMessages = array_filter($this->chatMessages, function ($message) {
            return !isset($message['is_thinking']) || !$message['is_thinking'];
        });

        // Add the AI response to the chat
        $this->chatMessages[] = $data['chatMessage'];

        // If there was an error, set waiting state to false and return
        if (isset($data['response']['error'])) {
            $this->isWaitingForResponse = false;
            return;
        }

        $response = $data['response'];
        $contentSection = isset($response['content_section']) ? $response['content_section'] : null;
        $conversationalResponse = isset($response['conversational_response']) ? $response['conversational_response'] : '';

        // If the response contains document content, suggest inserting it
        $documentContent = isset($response['document_content']) ? $response['document_content'] : '';
        if (!empty($documentContent) && !$conversationalResponse) {
            // Log the document content for debugging
            Log::debug('Document content from AI (via Pusher)', [
                'content_preview' => substr($documentContent, 0, 100) . '...',
                'is_json' => $this->isJsonString($documentContent)
            ]);

            // If the response specifies a section, use that, otherwise use the active section
            $targetSectionId = $contentSection ?? $this->activeSectionId;

            // Add to AI suggestions
            $this->aiSuggestions[] = [
                'content' => $documentContent,
                'type' => 'document_content',
                'timestamp' => now()->format('g:i A'),
                'section_id' => $targetSectionId  // Add section ID to suggestion
            ];

            // Log that suggestion was added
            Log::info('Added AI suggestion', [
                'suggestion_count' => count($this->aiSuggestions),
                'for_section' => $targetSectionId
            ]);

            // Dispatch event to switch to editor tab
            $this->dispatch('switchToEditorTab');

            // Show a notification about the new suggestion
            $this->dispatch('notify', [
                'type' => 'info',
                'message' => 'New AI suggestion available'
            ]);

            // Dispatch an event to show the suggestions modal
            $this->dispatch('showAiSuggestionsModal');

            // If the response specifies a section, update the active section
            if ($contentSection) {
                //                dd($contentSection);
                $this->activeSectionId = $contentSection;
                $this->setActiveSection($contentSection);
                $this->updateSectionContent();
            } else {
                //                dd('No content section');
            }

            // If the content is a JSON string (likely a Quill Delta), parse it
            //            if ($this->isJsonString($documentContent)) {
            //                $this->handlhandleJsonContenteJsonContent($documentContent, $targetSectionId);
            //            } else {
            //                // Otherwise, treat it as plain text/HTML
            //                $this->handlePlainContent($documentContent, $targetSectionId);
            //            }
        } else {
            //            dd('No document content', $response);
        }

        // Set waiting state to false
        $this->isWaitingForResponse = false;
    }

    /**
     * Test method to verify Livewire event handling
     */
    public function testEventHandling()
    {
        Log::info('Test event handling method called');

        // Add a test message to the chat
        $this->chatMessages[] = [
            'role' => 'system',
            'content' => 'Test message: ' . now()->format('H:i:s'),
            'timestamp' => now()->format('g:i A')
        ];

        // Dispatch a notification
        $this->dispatch('notify', [
            'type' => 'success',
            'message' => 'Test event handling successful'
        ]);
    }
}
