<?php

namespace App\Livewire;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Auth\Events\Registered;
use Illuminate\Validation\Rules;
use Livewire\Component;

class LandingPage extends Component
{
    // UI state
    public $currentView = 'main'; // main, login, register, howItWorks, about

    // Login form
    public $loginEmail = '';
    public $loginPassword = '';
    public $remember = false;

    // Register form
    public $name = '';
    public $username = '';
    public $email = '';
    public $password = '';
    public $password_confirmation = '';
    public $is_attorney = false;
    public $bar_card_number = '';
    public $zip_code = '';
    public $latitude = null;
    public $longitude = null;
    public $terms = false;

    // Validation rules for login
    protected function loginRules()
    {
        return [
            'loginEmail' => ['required', 'email'],
            'loginPassword' => ['required'],
        ];
    }

    // Validation rules for registration
    protected function registerRules()
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'username' => ['required', 'string', 'min:3', 'max:25', 'regex:/^[a-zA-Z0-9_-]+$/', 'unique:users'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
            'zip_code' => ['required', 'string', 'max:10'],
            'terms' => ['required', 'accepted'],
        ];

        if ($this->is_attorney) {
            $rules['bar_card_number'] = ['required', 'string', 'max:50'];
        }

        return $rules;
    }

    // Show the login form
    public function showLogin()
    {
        $this->currentView = 'login';
    }

    // Show the register form
    public function showRegister()
    {
        $this->currentView = 'register';
    }

    // Show the how it works section
    public function showHowItWorks()
    {
        $this->currentView = 'howItWorks';
    }

    // Show the about us section
    public function showAbout()
    {
        $this->currentView = 'about';
    }

    // Return to main view
    public function showMain()
    {
        $this->currentView = 'main';
    }

    // Handle login form submission
    public function login()
    {
        try {
            $this->validate($this->loginRules());

            if (Auth::attempt(['email' => $this->loginEmail, 'password' => $this->loginPassword], $this->remember)) {
                session()->regenerate();
                return redirect()->intended(route('dashboard'));
            }

            $this->addError('loginEmail', __('auth.failed'));
        } catch (\Exception $e) {
            $this->addError('loginEmail', 'Error: ' . $e->getMessage());
        }
    }

    /**
     * Set coordinates from Google Places API
     *
     * @param string $lat Latitude
     * @param string $lng Longitude
     * @return void
     */
    public function setCoordinates($lat, $lng)
    {
        $this->latitude = $lat;
        $this->longitude = $lng;

        // Log the coordinates capture for debugging
        \Illuminate\Support\Facades\Log::info("ZIP Code coordinates captured", [
            'latitude' => $lat,
            'longitude' => $lng,
            'zip_code' => $this->zip_code
        ]);
    }

    // Handle registration form submission
    public function register()
    {
        $this->validate($this->registerRules());

        $user = \Illuminate\Support\Facades\DB::transaction(function () {
            return tap(User::create([
                'name' => $this->name,
                'username' => $this->username,
                'email' => $this->email,
                'password' => Hash::make($this->password),
                'is_attorney' => $this->is_attorney,
                'bar_card_number' => $this->is_attorney ? $this->bar_card_number : null,
                'zip_code' => $this->zip_code,
                'latitude' => $this->latitude,
                'longitude' => $this->longitude,
            ]), function (User $user) {
                $this->createTeam($user);
            });
        });

        event(new Registered($user));

        Auth::login($user);

        return redirect(route('dashboard'));
    }

    protected function createTeam(User $user)
    {
        $user->ownedTeams()->create([
            'name' => explode(' ', $user->name, 2)[0]."'s Team",
            'personal_team' => true,
        ]);
    }

    public function render()
    {
        return view('livewire.landing-page');
    }
}
