# Storage Path Setup for Production

This document outlines the necessary steps to ensure storage paths exist and have proper permissions in the production environment.

## Required Commands

Run these commands on your production server during deployment:

```bash
# 1. Create the storage directory structure
mkdir -p storage/app/public/documents
mkdir -p storage/app/public/exhibits

# 2. Create the symbolic link (if not already created)
php artisan storage:link

# 3. Set proper permissions (adjust user/group as needed for your server)
# For Ubuntu/Debian with Apache
chown -R www-data:www-data storage bootstrap/cache
chmod -R 775 storage bootstrap/cache

# For Ubuntu/Debian with Nginx
# chown -R nginx:nginx storage bootstrap/cache
# chmod -R 775 storage bootstrap/cache

# For CentOS with Apache
# chown -R apache:apache storage bootstrap/cache
# chmod -R 775 storage bootstrap/cache
```

## Automated Setup in AppServiceProvider

Add this code to your `app/Providers/AppServiceProvider.php` in the `boot` method to ensure directories are created automatically:

```php
public function boot()
{
    // Ensure storage directories exist
    $paths = [
        storage_path('app/public/documents'),
        storage_path('app/public/exhibits'),
        // Add any other paths you need
    ];

    foreach ($paths as $path) {
        if (!file_exists($path)) {
            mkdir($path, 0755, true);
        }
    }
    
    // Your other boot code...
}
```

## Deployment Script Integration

If you're using a deployment tool like Forge, Envoyer, or a custom script, add these commands to your deployment process:

```bash
# Example deployment script section
echo "Setting up storage directories..."
mkdir -p storage/app/public/documents
mkdir -p storage/app/public/exhibits
php artisan storage:link
chmod -R 775 storage
chown -R www-data:www-data storage  # Adjust user/group as needed
```

## Queue Worker Setup

Since document generation happens in a background job, ensure your queue worker is running:

```bash
# Start the queue worker (adjust based on your server setup)
php artisan queue:work --daemon --tries=3 --timeout=600 > /dev/null 2>&1 &

# For Supervisor configuration (recommended)
# Create a file in /etc/supervisor/conf.d/laravel-worker.conf with:
[program:laravel-worker]
process_name=%(program_name)s_%(process_num)02d
command=php /path/to/your/project/artisan queue:work --sleep=3 --tries=3 --timeout=600
autostart=true
autorestart=true
user=www-data
numprocs=2
redirect_stderr=true
stdout_logfile=/path/to/your/project/storage/logs/worker.log
```

## Troubleshooting

If you encounter permission issues:

1. Check that the web server user has write permissions to the storage directory
2. Verify the symbolic link exists: `ls -la public/storage`
3. Check storage logs for any errors: `tail -f storage/logs/laravel.log`
4. Ensure the queue worker is running: `ps aux | grep queue:work`

## Important Notes

- The document generation process requires both the storage directories and queue worker to be properly configured
- Adjust user/group permissions based on your specific server configuration
- For high-traffic sites, consider increasing the number of queue workers
