# AI Integration and Document Updates Plan

## Overview

This document outlines the technical plan for integrating AI capabilities into the document editor and implementing document generation features. The plan is divided into phases to allow for incremental implementation and testing.

## Phase 1: Integrate with Existing AI Assistant

### 1.1 Leverage Existing AssistantChatService

We'll use the existing `AssistantChatService` that's already attached to case files. This service already handles:

- Creating OpenAI assistant threads
- Sending messages to the assistant
- Receiving and processing responses
- Managing attachments and file uploads
- Handling system prompts

```php
// app/Services/DocumentAiService.php
class DocumentAiService
{
    protected $assistantChatService;
    protected $caseFile;
    protected $thread;

    public function __construct(AssistantChatService $assistantChatService)
    {
        $this->assistantChatService = $assistantChatService;
    }

    public function initializeForDraft(Draft $draft)
    {
        $this->caseFile = $draft->caseFile;

        // Get or create a dedicated thread for document editing
        $this->thread = AssistantThread::where('case_file_id', $this->caseFile->id)
            ->where('type', 'document_editor')
            ->where('metadata->draft_id', $draft->id)
            ->first();

        if (!$this->thread) {
            // Create a new thread for this draft
            $this->thread = $this->assistantChatService->createThreadWithSystemPrompt(
                $this->caseFile,
                Auth::user(),
                "Document Editor: {$draft->draft_type}",
                "Thread for editing {$draft->draft_type} document",
                'document_editor',
                $this->getDocumentSystemPrompt($draft),
                'document_editor'
            );

            // Store draft ID in thread metadata
            $metadata = $this->thread->metadata ?? [];
            $metadata['draft_id'] = $draft->id;
            $this->thread->update(['metadata' => $metadata]);
        }

        return $this;
    }

    protected function getDocumentSystemPrompt(Draft $draft)
    {
        // Create a system prompt specific to document editing
        return "You are a legal document assistant helping to draft a {$draft->draft_type}. ".
               "Your goal is to help the user create a professional, accurate, and effective legal document. ".
               "When asked to generate content for specific sections, provide well-structured, legally sound text. ".
               "Base your responses on legal best practices and the specific details of the case.";
    }

    public function generateContent($prompt, $context = [])
    {
        if (!$this->thread) {
            throw new \Exception('Thread not initialized. Call initializeForDraft() first.');
        }

        // Create a message with the prompt and context
        $message = AssistantMessage::create([
            'assistant_thread_id' => $this->thread->id,
            'role' => 'user',
            'content' => $this->formatPromptWithContext($prompt, $context),
        ]);

        // Send the message and get the response
        return $this->assistantChatService->sendMessageToAssistant($message, $this->thread);
    }

    protected function formatPromptWithContext($prompt, $context = [])
    {
        // Format the prompt with context information
        $formattedPrompt = $prompt;

        if (!empty($context)) {
            $formattedPrompt .= "\n\nContext Information:\n";
            foreach ($context as $key => $value) {
                if (is_array($value)) {
                    $formattedPrompt .= "\n{$key}:\n" . json_encode($value, JSON_PRETTY_PRINT);
                } else {
                    $formattedPrompt .= "\n{$key}: {$value}";
                }
            }
        }

        return $formattedPrompt;
    }
}
```

### 1.2 Create Document-Specific Prompts

```php
// app/Services/DocumentPromptService.php
class DocumentPromptService
{
    protected $sectionPrompts = [
        'caption' => [
            'generate' => 'Generate a caption for a {document_type} in {jurisdiction} court with plaintiff {plaintiff_name} and defendant {defendant_name}.',
            'improve' => 'Improve this caption to ensure it follows the proper format for {jurisdiction} court:',
        ],
        'introduction' => [
            'generate' => 'Write an introduction paragraph for a {document_type} regarding {case_type}.',
            'improve' => 'Improve this introduction to make it more compelling and clear:',
        ],
        // Other section types...
    ];

    public function getSectionPrompt($sectionType, $action, $variables = [])
    {
        if (!isset($this->sectionPrompts[$sectionType][$action])) {
            return "Generate content for the {$sectionType} section.";
        }

        $prompt = $this->sectionPrompts[$sectionType][$action];

        // Replace variables in the prompt
        foreach ($variables as $key => $value) {
            $prompt = str_replace('{' . $key . '}', $value, $prompt);
        }

        return $prompt;
    }
}
```

## Phase 2: Livewire Component Updates

### 2.1 Update AiDocumentEditor Component

```php
// app/Livewire/Drafts/AiDocumentEditor.php
class AiDocumentEditor extends Component
{
    // Existing properties...

    protected $documentAiService;
    protected $documentPromptService;
    protected $assistantThread;

    public function boot(DocumentAiService $documentAiService, DocumentPromptService $documentPromptService)
    {
        $this->documentAiService = $documentAiService;
        $this->documentPromptService = $documentPromptService;
    }

    public function mount(CaseFile $caseFile, Draft $draft)
    {
        // Existing mount code...

        // Initialize the AI service for this draft
        $this->documentAiService->initializeForDraft($draft);

        // Load existing messages if available
        $this->loadChatHistory();
    }

    protected function loadChatHistory()
    {
        // Get the thread associated with this draft
        $thread = AssistantThread::where('case_file_id', $this->caseFile->id)
            ->where('type', 'document_editor')
            ->where('metadata->draft_id', $this->draft->id)
            ->first();

        if ($thread) {
            $this->assistantThread = $thread;

            // Load messages from the thread
            $messages = AssistantMessage::where('assistant_thread_id', $thread->id)
                ->orderBy('created_at', 'asc')
                ->get()
                ->map(function ($message) {
                    return [
                        'role' => $message->role,
                        'content' => $message->content,
                        'timestamp' => $message->created_at->format('g:i A')
                    ];
                })
                ->toArray();

            $this->chatMessages = $messages;
        }
    }

    public function sendAiMessage()
    {
        if (empty(trim($this->aiMessage))) {
            return;
        }

        // Add the user message to the chat
        $this->chatMessages[] = [
            'role' => 'user',
            'content' => $this->aiMessage,
            'timestamp' => now()->format('g:i A')
        ];

        $prompt = $this->aiMessage;
        $this->aiMessage = '';
        $this->isWaitingForResponse = true;

        // Get document context

        // Send to AI service
        $response = $this->documentAiService->generateContent($prompt, $context);

        // Add the AI response to the chat
        $this->chatMessages[] = [
            'role' => 'assistant',
            'content' => $response,
            'timestamp' => now()->format('g:i A')
        ];

        $this->isWaitingForResponse = false;
    }

    public function generateSectionContent()
    {
        if (!$this->activeSectionId) {
            return;
        }

        // Find the active section
        $activeSection = null;
        foreach ($this->sections as $section) {
            if ($section['id'] === $this->activeSectionId) {
                $activeSection = $section;
                break;
            }
        }

        if (!$activeSection) {
            return;
        }

        // Get the appropriate prompt for this section type
        $prompt = $this->documentPromptService->getSectionPrompt(
            $activeSection['id'],
            'generate',
            [
                'document_type' => $this->draft->draft_type,
                'jurisdiction' => $this->caseFile->jurisdiction ?? 'the appropriate',
                'case_type' => $this->caseFile->case_type ?? 'this type of case',
                // Add other variables as needed
            ]
        );

        // Add the prompt to the chat
        $this->chatMessages[] = [
            'role' => 'user',
            'content' => $prompt,
            'timestamp' => now()->format('g:i A')
        ];

        $this->isWaitingForResponse = true;

        // Get document context
        $context = $this->getDocumentContext();

        // Send to AI service
        $response = $this->documentAiService->generateContent($prompt, $context);

        // Add the AI response to the chat
        $this->chatMessages[] = [
            'role' => 'assistant',
            'content' => "I've generated content for the {$activeSection['name']} section:",
            'timestamp' => now()->format('g:i A')
        ];

        // Update the section content
        $this->activeSectionContent = $response;
        $this->updateSectionContent();

        $this->isWaitingForResponse = false;
    }

    protected function getDocumentContext()
    {
        // Prepare document context for AI
        $context = [
            'document_type' => $this->draft->draft_type,
            'sections' => $this->sections,
            'active_section' => $this->activeSectionId,
            'case_file' => [
                'id' => $this->caseFile->id,
                'title' => $this->caseFile->title,
                'description' => $this->caseFile->description,
                'jurisdiction' => $this->caseFile->jurisdiction,
                'case_type' => $this->caseFile->case_type,
                // Add other relevant case file properties
            ],
        ];

        // Add case summary if available
        $caseSummary = $this->caseFile->latestSummary;
        if ($caseSummary) {
            $context['case_summary'] = $caseSummary->content;
        }

        return $context;
    }
}
```

### 2.2 Update Draft Model for AI Integration

```php
// app/Models/Draft.php
class Draft extends Model
{
    // Existing properties and methods...

    /**
     * Get the assistant thread associated with this draft.
     */
    public function assistantThread()
    {
        return $this->hasOneThrough(
            AssistantThread::class,
            CaseFile::class,
            'id', // Foreign key on case_files table
            'case_file_id', // Foreign key on assistant_threads table
            'case_file_id', // Local key on drafts table
            'id' // Local key on case_files table
        )->where('metadata->draft_id', $this->id)
          ->where('type', 'document_editor');
    }

    /**
     * Get the AI chat messages associated with this draft.
     */
    public function aiChatMessages()
    {
        return $this->hasManyThrough(
            AssistantMessage::class,
            AssistantThread::class,
            'metadata->draft_id', // Foreign key on assistant_threads table (JSON path)
            'assistant_thread_id', // Foreign key on assistant_messages table
            'id', // Local key on drafts table
            'id' // Local key on assistant_threads table
        )->where('assistant_threads.type', 'document_editor');
    }

    /**
     * Create or get the AI assistant thread for this draft.
     */
    public function getOrCreateAssistantThread()
    {
        $thread = $this->assistantThread()->first();

        if (!$thread) {
            $chatService = app(AssistantChatService::class);
            $systemPrompt = "You are a legal document assistant helping to draft a {$this->draft_type}. ".
                           "Your goal is to help the user create a professional, accurate, and effective legal document.";

            $thread = $chatService->createThreadWithSystemPrompt(
                $this->caseFile,
                auth()->user(),
                "Document Editor: {$this->draft_type}",
                "Thread for editing {$this->draft_type} document",
                'document_editor',
                $systemPrompt,
                'document_editor'
            );

            // Store draft ID in thread metadata
            $metadata = $thread->metadata ?? [];
            $metadata['draft_id'] = $this->id;
            $thread->update(['metadata' => $metadata]);
        }

        return $thread;
    }
}
```

### 2.3 Hybrid Prompt Generation Approach

The document editor will support two complementary methods for generating content:

#### Button-Generated Prompts

Pre-defined buttons in the UI will trigger specific content generation actions:

```php
// In AiDocumentEditor.php
public function generateSectionContent()
{
    if (!$this->activeSectionId) {
        return;
    }

    // Find the active section
    $activeSection = null;
    foreach ($this->sections as $section) {
        if ($section['id'] === $this->activeSectionId) {
            $activeSection = $section;
            break;
        }
    }

    if (!$activeSection) {
        return;
    }

    // Get pre-defined prompt for the current section
    $prompt = $this->documentPromptService->getSectionPrompt(
        $this->activeSectionId,
        'generate',
        [
            'document_type' => $this->draft->draft_type,
            'jurisdiction' => $this->caseFile->jurisdiction ?? 'the appropriate',
            'case_type' => $this->caseFile->case_type ?? 'this type of case',
            // Other variables...
        ]
    );

    // Add the prompt to the chat
    $this->chatMessages[] = [
        'role' => 'user',
        'content' => $prompt,
        'timestamp' => now()->format('g:i A')
    ];

    $this->isWaitingForResponse = true;

    // Get document context
    $context = $this->getDocumentContext();

    // Send to AI service
    $response = $this->documentAiService->generateContent($prompt, $context);

    // Add the AI response to the chat
    $this->chatMessages[] = [
        'role' => 'assistant',
        'content' => "I've generated content for the {$activeSection['name']} section:",
        'timestamp' => now()->format('g:i A')
    ];

    // Update the section content
    $this->activeSectionContent = $response;
    $this->updateSectionContent();

    $this->isWaitingForResponse = false;
}
```

This approach ensures consistent, high-quality prompts and simplifies the user experience. The UI will include buttons for common actions:

- Generate content for the current section
- Improve the current section
- Generate the entire document
- Add legal citations to the current section
- Simplify language in the current section

#### User-Generated Prompts

A chat interface allows users to type custom prompts for more specific needs:

```php
// In AiDocumentEditor.php
public function sendAiMessage()
{
    if (empty(trim($this->aiMessage))) {
        return;
    }

    // Add the user message to the chat
    $this->chatMessages[] = [
        'role' => 'user',
        'content' => $this->aiMessage,
        'timestamp' => now()->format('g:i A')
    ];

    $userPrompt = $this->aiMessage;
    $this->aiMessage = '';
    $this->isWaitingForResponse = true;

    // Get document context
    $context = $this->getDocumentContext();

    // Send to AI service (which will add context behind the scenes)
    $response = $this->documentAiService->generateContent($userPrompt, $context);

    // Add the AI response to the chat
    $this->chatMessages[] = [
        'role' => 'assistant',
        'content' => $response,
        'timestamp' => now()->format('g:i A')
    ];

    // Store the response for potential application to sections
    $this->lastAiResponse = $response;
    $this->showApplyResponseButton = true;

    $this->isWaitingForResponse = false;
}

public function applyResponseToCurrentSection()
{
    if (!$this->activeSectionId || !$this->lastAiResponse) {
        return;
    }

    // Update the current section with the latest AI response
    $this->activeSectionContent = $this->lastAiResponse;
    $this->updateSectionContent();

    // Reset the apply button state
    $this->showApplyResponseButton = false;
}
```

This approach provides flexibility for specific user needs while maintaining proper context. Users can:

- Ask specific questions about legal concepts
- Request custom content not covered by the pre-defined buttons
- Get explanations about document sections
- Request edits with specific requirements
- Apply the AI's response to the current section if desired

#### Context Enhancement for Both Approaches

Both approaches benefit from automatic context enhancement:

```php
// In DocumentAiService.php
protected function formatPromptWithContext($prompt, $context = [])
{
    // Start with the main prompt (either button-generated or user-typed)
    $formattedPrompt = $prompt;

    // Add a separator and context that's invisible to the user
    if (!empty($context)) {
        $formattedPrompt .= "\n\n--- ADDITIONAL CONTEXT (NOT VISIBLE TO USER) ---\n";

        // Add active section context
        if (isset($context['active_section'])) {
            $formattedPrompt .= "\nActive Section: {$context['active_section']['name']} (ID: {$context['active_section']['id']})\n";
        }

        // Add document type
        if (isset($context['document_type'])) {
            $formattedPrompt .= "\nDocument Type: {$context['document_type']}\n";
        }

        // Add case file details
        if (isset($context['case_file'])) {
            $formattedPrompt .= "\nCase Details: " .
                "Title: {$context['case_file']['title']}, " .
                "Type: {$context['case_file']['case_type']}, " .
                "Jurisdiction: {$context['case_file']['jurisdiction']}\n";
        }

        // Add case summary if available
        if (isset($context['case_summary'])) {
            $formattedPrompt .= "\nCase Summary: {$context['case_summary']}\n";
        }
    }

    return $formattedPrompt;
}
```

This ensures that even with user-generated prompts, the AI has the necessary context to provide relevant responses.
```php
// app/Models/Draft.php
class Draft extends Model
{
    // Existing properties and methods...

    /**
     * Get the assistant thread associated with this draft.
     */
    public function assistantThread()
    {
        return $this->hasOneThrough(
            AssistantThread::class,
            CaseFile::class,
            'id', // Foreign key on case_files table
            'case_file_id', // Foreign key on assistant_threads table
            'case_file_id', // Local key on drafts table
            'id' // Local key on case_files table
        )->where('metadata->draft_id', $this->id)
          ->where('type', 'document_editor');
    }

    /**
     * Get the AI chat messages associated with this draft.
     */
    public function aiChatMessages()
    {
        return $this->hasManyThrough(
            AssistantMessage::class,
            AssistantThread::class,
            'metadata->draft_id', // Foreign key on assistant_threads table (JSON path)
            'assistant_thread_id', // Foreign key on assistant_messages table
            'id', // Local key on drafts table
            'id' // Local key on assistant_threads table
        )->where('assistant_threads.type', 'document_editor');
    }

    /**
     * Create or get the AI assistant thread for this draft.
     */
    public function getOrCreateAssistantThread()
    {
        $thread = $this->assistantThread()->first();

        if (!$thread) {
            $chatService = app(AssistantChatService::class);
            $systemPrompt = "You are a legal document assistant helping to draft a {$this->draft_type}. ".
                           "Your goal is to help the user create a professional, accurate, and effective legal document.";

            $thread = $chatService->createThreadWithSystemPrompt(
                $this->caseFile,
                auth()->user(),
                "Document Editor: {$this->draft_type}",
                "Thread for editing {$this->draft_type} document",
                'document_editor',
                $systemPrompt,
                'document_editor'
            );

            // Store draft ID in thread metadata
            $metadata = $thread->metadata ?? [];
            $metadata['draft_id'] = $this->id;
            $thread->update(['metadata' => $metadata]);
        }

        return $thread;
    }
}
```

## Phase 3: Document Structure Enhancements

### 3.1 Improve Section Management

```php
// app/Models/DraftSection.php
class DraftSection extends Model
{
    protected $fillable = [
        'draft_id',
        'section_id',
        'name',
        'content',
        'order',
        'metadata',
        'is_required',
        'parent_section_id',
    ];

    protected $casts = [
        'metadata' => 'array',
        'is_required' => 'boolean',
    ];

    public function draft()
    {
        return $this->belongsTo(Draft::class);
    }

    public function parentSection()
    {
        return $this->belongsTo(DraftSection::class, 'parent_section_id');
    }

    public function childSections()
    {
        return $this->hasMany(DraftSection::class, 'parent_section_id');
    }
}
```

### 3.2 Add Version Control

```php
// app/Models/DraftVersion.php
class DraftVersion extends Model
{
    protected $fillable = [
        'draft_id',
        'version_number',
        'content',
        'created_by',
        'comment',
    ];

    protected $casts = [
        'content' => 'array',
    ];

    public function draft()
    {
        return $this->belongsTo(Draft::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
```

## Phase 4: Document Generation

### 4.1 Enhance GenDoc Command

```php
// app/Console/Commands/GenDoc.php
class GenDoc extends Command
{
    // Existing code...

    public function handle()
    {
        $draftId = $this->argument('draft_id');
        $draft = Draft::findOrFail($draftId);

        $documentService = new DocumentGenerationService();
        $phpWord = $documentService->generateDocument($draft);

        $filename = $this->sanitizeFilename($draft->title);
        $path = storage_path("app/generated/{$filename}.docx");

        $objWriter = \PhpOffice\PhpWord\IOFactory::createWriter($phpWord, 'Word2007');
        $objWriter->save($path);

        // Create document record
        $document = Document::create([
            'case_file_id' => $draft->case_file_id,
            'draft_id' => $draft->id,
            'title' => $draft->title,
            'file_path' => "generated/{$filename}.docx",
            'file_type' => 'docx',
            'created_by' => $draft->last_edited_by,
        ]);

        $this->info("Document generated successfully: {$path}");

        return $document;
    }
}
```

### 4.2 Create Document Generation Service

```php
// app/Services/DocumentGenerationService.php
class DocumentGenerationService
{
    public function generateDocument(Draft $draft)
    {
        // Create PhpWord instance
        $phpWord = new \PhpOffice\PhpWord\PhpWord();

        // Apply document settings
        $this->applyDocumentSettings($phpWord, $draft);

        // Create main section
        $section = $phpWord->addSection();

        // Process each draft section
        foreach ($draft->sections_structure as $draftSection) {
            $this->processSection($section, $draftSection, $draft);
        }

        return $phpWord;
    }

    protected function applyDocumentSettings($phpWord, $draft)
    {
        // Apply document-wide settings
        // (margins, default font, etc.)
    }

    protected function processSection($section, $draftSection, $draft)
    {
        // Process section based on type
        switch ($draftSection['id']) {
            case 'caption':
                $this->processCaption($section, $draftSection);
                break;
            case 'introduction':
                $this->processTextSection($section, $draftSection);
                break;
            // Other section types...
            default:
                $this->processGenericSection($section, $draftSection);
                break;
        }
    }

    // Section-specific processing methods...
}
```

### 4.3 Add Document Download Controller

```php
// app/Http/Controllers/DocumentController.php
class DocumentController extends Controller
{
    public function download(Document $document)
    {
        $path = storage_path('app/' . $document->file_path);

        if (!file_exists($path)) {
            abort(404, 'Document not found');
        }

        return response()->download($path, $document->title . '.docx');
    }

    public function generate(Draft $draft)
    {
        // Generate document using Artisan command
        $exitCode = Artisan::call('gen-doc', [
            'draft_id' => $draft->id
        ]);

        if ($exitCode !== 0) {
            return back()->with('error', 'Failed to generate document');
        }

        $output = Artisan::output();
        $document = Document::where('draft_id', $draft->id)->latest()->first();

        if (!$document) {
            return back()->with('error', 'Document generation failed');
        }

        return redirect()->route('documents.download', $document);
    }
}
```

## Phase 5: UI Enhancements

### 5.1 Add AI Suggestions Panel

```html
<!-- resources/views/livewire/drafts/ai-document-editor.blade.php -->
<div class="bg-base-200 p-4 rounded-lg mb-4">
    <h3 class="text-lg font-medium mb-2">AI Suggestions</h3>
    <div class="space-y-2">
        @foreach($aiSuggestions as $suggestion)
            <div class="bg-base-100 p-3 rounded-lg">
                <p class="text-sm">{{ $suggestion['text'] }}</p>
                <div class="flex justify-end mt-2">
                    <button wire:click="applySuggestion({{ $loop->index }})" class="btn btn-xs btn-primary">
                        Apply
                    </button>
                </div>
            </div>
        @endforeach
    </div>
</div>
```

### 5.2 Add Section Templates

```html
<!-- resources/views/livewire/drafts/ai-document-editor.blade.php -->
<div class="dropdown dropdown-end">
    <label tabindex="0" class="btn btn-sm">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        Add Section
    </label>
    <ul tabindex="0" class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-52">
        @foreach($sectionTemplates as $template)
            <li>
                <a wire:click="addSection('{{ $template['id'] }}')">
                    {{ $template['name'] }}
                </a>
            </li>
        @endforeach
    </ul>
</div>
```

### 5.3 Add Document Preview

```html
<!-- resources/views/livewire/drafts/ai-document-editor.blade.php -->
<div class="modal" id="previewModal">
    <div class="modal-box w-11/12 max-w-5xl">
        <h3 class="font-bold text-lg">Document Preview</h3>
        <div class="py-4">
            <div class="bg-white p-8 rounded-lg shadow-inner min-h-[600px] prose max-w-none">
                @foreach($sections as $section)
                    <div class="mb-6">
                        <h4 class="text-lg font-bold">{{ $section['name'] }}</h4>
                        <div>{!! $section['content'] !!}</div>
                    </div>
                @endforeach
            </div>
        </div>
        <div class="modal-action">
            <button class="btn" onclick="document.getElementById('previewModal').close()">Close</button>
            <button wire:click="generateDocument" class="btn btn-primary">
                Generate Document
            </button>
        </div>
    </div>
</div>
```

## Phase 6: Advanced AI Features

### 6.1 Context-Aware Suggestions

```php
// app/Livewire/Drafts/AiDocumentEditor.php
public function getContextAwareSuggestions()
{
    if (!$this->activeSectionId) {
        return;
    }

    $activeSection = null;
    foreach ($this->sections as $section) {
        if ($section['id'] === $this->activeSectionId) {
            $activeSection = $section;
            break;
        }
    }

    if (!$activeSection) {
        return;
    }

    // Create a prompt for suggestions
    $prompt = "Based on the current content of the {$activeSection['name']} section, ".
              "please provide 3-5 specific suggestions to improve this section. ".
              "Focus on legal clarity, persuasiveness, and completeness.";

    // Add the current content to the context
    $context = $this->getDocumentContext();
    $context['current_section_content'] = $activeSection['content'];

    // Send to AI service
    $response = $this->documentAiService->generateContent($prompt, $context);

    // Parse the suggestions
    $suggestions = $this->parseSuggestions($response);
    $this->aiSuggestions = $suggestions;
}

protected function parseSuggestions($response)
{
    // Simple parsing - split by numbered items or bullet points
    $suggestions = [];
    $lines = explode("\n", $response);

    foreach ($lines as $line) {
        // Skip empty lines
        if (empty(trim($line))) continue;

        // Check for numbered items or bullet points
        if (preg_match('/^\d+\.\s+(.+)$/', $line, $matches) ||
            preg_match('/^[\*\-•]\s+(.+)$/', $line, $matches)) {
            $suggestions[] = [
                'text' => $matches[1],
                'type' => 'suggestion'
            ];
        } else if (!empty($suggestions)) {
            // Append to the last suggestion if it's a continuation
            $lastIndex = count($suggestions) - 1;
            $suggestions[$lastIndex]['text'] .= " " . $line;
        } else {
            // If it's not a numbered/bullet item and we don't have suggestions yet,
            // it might be an introduction text - skip it
        }
    }

    return $suggestions;
}
```

### 6.2 Legal Research Integration

```php
// app/Services/LegalResearchService.php
class LegalResearchService
{
    protected $documentAiService;
    protected $caseFile;

    public function __construct(DocumentAiService $documentAiService)
    {
        $this->documentAiService = $documentAiService;
    }

    public function initializeForCaseFile(CaseFile $caseFile)
    {
        $this->caseFile = $caseFile;
        return $this;
    }

    public function findRelevantCases($query, $jurisdiction = null, $limit = 5)
    {
        $prompt = "Find {$limit} relevant legal cases for the following legal issue: {$query}";

        if ($jurisdiction) {
            $prompt .= " in {$jurisdiction} jurisdiction.";
        }

        $prompt .= "\n\nFor each case, provide:\n";
        $prompt .= "1. Case name\n";
        $prompt .= "2. Citation\n";
        $prompt .= "3. Year\n";
        $prompt .= "4. Court\n";
        $prompt .= "5. Brief summary of holding\n";
        $prompt .= "6. Relevance to our issue\n";

        // Create a thread specifically for legal research
        $thread = AssistantThread::where('case_file_id', $this->caseFile->id)
            ->where('type', 'legal_research')
            ->first();

        if (!$thread) {
            $chatService = app(AssistantChatService::class);
            $thread = $chatService->createThreadWithSystemPrompt(
                $this->caseFile,
                auth()->user(),
                "Legal Research",
                "Thread for legal research related to this case",
                'legal_research',
                "You are a legal research assistant with expertise in finding relevant case law and legal authorities.",
                'legal_research'
            );
        }

        // Create a message with the prompt
        $message = AssistantMessage::create([
            'assistant_thread_id' => $thread->id,
            'role' => 'user',
            'content' => $prompt,
        ]);

        // Send the message and get the response
        $chatService = app(AssistantChatService::class);
        $response = $chatService->sendMessageToAssistant($message, $thread);

        return $this->parseCaseResults($response);
    }

    protected function parseCaseResults($response)
    {
        // Parse the response into structured case data
        // This is a simplified implementation
        $cases = [];
        $currentCase = null;
        $lines = explode("\n", $response);

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // Check for case name pattern (usually starts with a number or has v. in it)
            if (preg_match('/^\d+\.\s+(.+)$/', $line, $matches) ||
                strpos($line, ' v. ') !== false) {
                // Save previous case if exists
                if ($currentCase) {
                    $cases[] = $currentCase;
                }

                // Start new case
                $currentCase = [
                    'name' => preg_match('/^\d+\.\s+(.+)$/', $line, $matches) ? $matches[1] : $line,
                    'citation' => '',
                    'year' => '',
                    'court' => '',
                    'holding' => '',
                    'relevance' => ''
                ];
            } else if ($currentCase) {
                // Add details to current case
                if (preg_match('/Citation:\s*(.+)/i', $line, $matches)) {
                    $currentCase['citation'] = $matches[1];
                } else if (preg_match('/Year:\s*(\d{4})/i', $line, $matches)) {
                    $currentCase['year'] = $matches[1];
                } else if (preg_match('/Court:\s*(.+)/i', $line, $matches)) {
                    $currentCase['court'] = $matches[1];
                } else if (preg_match('/Holding:|Summary:/i', $line)) {
                    $currentCase['holding'] = '';
                } else if (preg_match('/Relevance:/i', $line)) {
                    $currentCase['relevance'] = '';
                } else if (!empty($currentCase['holding']) && empty($currentCase['relevance'])) {
                    $currentCase['holding'] .= " " . $line;
                } else if (!empty($currentCase['relevance'])) {
                    $currentCase['relevance'] .= " " . $line;
                }
            }
        }

        // Add the last case
        if ($currentCase) {
            $cases[] = $currentCase;
        }

        return $cases;
    }
}
```

### 6.3 Document Analysis

```php
// app/Services/DocumentAnalysisService.php
class DocumentAnalysisService
{
    protected $documentAiService;

    public function __construct(DocumentAiService $documentAiService)
    {
        $this->documentAiService = $documentAiService;
    }

    public function analyzeDocument(Draft $draft)
    {
        // Initialize the AI service for this draft
        $this->documentAiService->initializeForDraft($draft);

        // Prepare document content
        $content = '';
        foreach ($draft->sections_structure as $section) {
            $content .= "## {$section['name']}\n\n";
            $content .= $section['content'] . "\n\n";
        }

        // Create the analysis prompt
        $prompt = "Analyze this {$draft->draft_type} document and provide feedback on:\n";
        $prompt .= "1. Clarity and readability\n";
        $prompt .= "2. Legal argument strength\n";
        $prompt .= "3. Potential weaknesses or omissions\n";
        $prompt .= "4. Suggested improvements\n\n";

        // Get document context
        $context = [
            'document_type' => $draft->draft_type,
            'document_content' => $content,
            'case_file' => [
                'id' => $draft->caseFile->id,
                'title' => $draft->caseFile->title,
                'description' => $draft->caseFile->description,
                'jurisdiction' => $draft->caseFile->jurisdiction,
                'case_type' => $draft->caseFile->case_type,
            ],
        ];

        // Send to AI service
        return $this->documentAiService->generateContent($prompt, $context);
    }
}
```

## Implementation Timeline

1. **Phase 1 (Week 1-2)**: Integrate with existing AssistantChatService and create document-specific prompts
2. **Phase 2 (Week 3-4)**: Update Livewire components to use the AI integration
3. **Phase 3 (Week 5-6)**: Enhance document structure and add version control
4. **Phase 4 (Week 7-8)**: Implement document generation features
5. **Phase 5 (Week 9-10)**: Add UI enhancements
6. **Phase 6 (Week 11-12)**: Implement advanced AI features

## Technical Requirements

- PHP 8.1+
- Laravel 10+
- Existing AssistantChatService integration
- PhpWord library for document generation
- Livewire 3+
- Alpine.js for frontend interactivity
- MySQL/PostgreSQL for database storage

## Considerations and Risks

1. **Thread Management**: Need to properly manage AI assistant threads for different drafts
2. **Context Limitations**: OpenAI assistants have context limitations that may affect document analysis
3. **Content Security**: Legal documents contain sensitive information
4. **Performance**: Large document generation may be resource-intensive
5. **Reliability**: Dependency on external AI services requires fallback mechanisms

## Next Steps

1. Create the DocumentAiService that leverages the existing AssistantChatService
2. Develop and test document-specific prompts
3. Update the AiDocumentEditor component to use the real AI integration
4. Implement document section management
5. Develop document generation features

---

This plan provides a roadmap for implementing AI integration and document updates in the legal document editor. Each phase builds upon the previous one, allowing for incremental development and testing.
