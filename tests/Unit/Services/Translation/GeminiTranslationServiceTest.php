<?php

namespace Tests\Unit\Services\Translation;

use App\Services\Translation\GeminiTranslationService;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class GeminiTranslationServiceTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock the HTTP client
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'candidates' => [
                    [
                        'content' => [
                            'parts' => [
                                [
                                    'text' => 'Texto traducido en español'
                                ]
                            ]
                        ]
                    ]
                ]
            ], 200)
        ]);
    }
    
    public function test_translate_returns_original_content_when_target_language_is_english()
    {
        $service = new GeminiTranslationService();
        $content = '# Test Content\n\nThis is a test.';
        
        $result = $service->translate($content, 'en');
        
        $this->assertEquals($content, $result);
        Http::assertNothingSent();
    }
    
    public function test_translate_returns_original_content_when_content_is_empty()
    {
        $service = new GeminiTranslationService();
        $content = '';
        
        $result = $service->translate($content, 'es');
        
        $this->assertEquals($content, $result);
        Http::assertNothingSent();
    }
    
    public function test_translate_calls_gemini_api_and_returns_translated_content()
    {
        $service = new GeminiTranslationService();
        $content = '# Test Content\n\nThis is a test.';
        
        $result = $service->translate($content, 'es');
        
        $this->assertEquals('Texto traducido en español', $result);
        Http::assertSent(function ($request) {
            return $request->url() === 'https://generativelanguage.googleapis.com/v1/models/gemini-1.5-pro:generateContent?key='
                && $request->method() === 'POST'
                && isset($request['contents'][0]['parts'][0]['text'])
                && str_contains($request['contents'][0]['parts'][0]['text'], 'Translate the following markdown content to Spanish');
        });
    }
    
    public function test_translate_handles_api_error_gracefully()
    {
        // Override the fake for this test
        Http::fake([
            'generativelanguage.googleapis.com/*' => Http::response([
                'error' => [
                    'code' => 400,
                    'message' => 'Bad Request'
                ]
            ], 400)
        ]);
        
        $service = new GeminiTranslationService();
        $content = '# Test Content\n\nThis is a test.';
        
        $result = $service->translate($content, 'es');
        
        // Should return original content on error
        $this->assertEquals($content, $result);
    }
}
