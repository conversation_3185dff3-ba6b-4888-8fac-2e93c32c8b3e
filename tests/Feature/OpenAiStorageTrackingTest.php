<?php

namespace Tests\Feature;

use App\Models\CaseFile;
use App\Models\Document;
use App\Models\OpenAiProject;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class OpenAiStorageTrackingTest extends TestCase
{
    use RefreshDatabase;

    public function test_storage_is_incremented_when_document_is_created()
    {
        // Create test data
        $user = User::factory()->create();
        $project = OpenAiProject::factory()->create(['storage_used' => 0]);
        $caseFile = CaseFile::factory()->create([
            'user_id' => $user->id,
            'openai_project_id' => $project->id
        ]);

        // Create a document
        $document = Document::factory()->create([
            'case_file_id' => $caseFile->id,
            'file_size' => 1024 * 1024 // 1MB
        ]);

        // Refresh the project to get updated storage_used
        $project->refresh();

        // Assert storage was incremented
        $this->assertEquals(1024 * 1024, $project->storage_used);
    }

    public function test_storage_is_decremented_when_document_is_deleted()
    {
        // Create test data
        $user = User::factory()->create();
        $project = OpenAiProject::factory()->create(['storage_used' => 2 * 1024 * 1024]); // 2MB
        $caseFile = CaseFile::factory()->create([
            'user_id' => $user->id,
            'openai_project_id' => $project->id
        ]);

        $document = Document::factory()->create([
            'case_file_id' => $caseFile->id,
            'file_size' => 1024 * 1024 // 1MB
        ]);

        // Delete the document
        $document->delete();

        // Refresh the project
        $project->refresh();

        // Assert storage was decremented (2MB - 1MB = 1MB)
        $this->assertEquals(1024 * 1024, $project->storage_used);
    }

    public function test_storage_is_updated_when_document_file_size_changes()
    {
        // Create test data
        $user = User::factory()->create();
        $project = OpenAiProject::factory()->create(['storage_used' => 0]);
        $caseFile = CaseFile::factory()->create([
            'user_id' => $user->id,
            'openai_project_id' => $project->id
        ]);

        $document = Document::factory()->create([
            'case_file_id' => $caseFile->id,
            'file_size' => 1024 * 1024 // 1MB
        ]);

        // Update document file size
        $document->update(['file_size' => 2 * 1024 * 1024]); // 2MB

        // Refresh the project
        $project->refresh();

        // Assert storage reflects the new size (1MB + 1MB difference = 2MB)
        $this->assertEquals(2 * 1024 * 1024, $project->storage_used);
    }

    public function test_storage_percentage_calculation()
    {
        $project = OpenAiProject::factory()->create([
            'storage_used' => 50 * 1024 * 1024 * 1024 // 50GB
        ]);

        $this->assertEquals(50.0, $project->getStorageUsagePercentage());
        $this->assertFalse($project->isNearingCapacity()); // Default threshold is 90%
        $this->assertTrue($project->isNearingCapacity(40)); // Custom threshold of 40%
    }

    public function test_storage_info_method()
    {
        $project = OpenAiProject::factory()->create([
            'storage_used' => 10 * 1024 * 1024 * 1024 // 10GB
        ]);

        $info = $project->getStorageInfo();

        $this->assertEquals(10 * 1024 * 1024 * 1024, $info['used_bytes']);
        $this->assertEquals(10.0, $info['used_gb']);
        $this->assertEquals(100, $info['limit_gb']);
        $this->assertEquals(10.0, $info['percentage']);
        $this->assertEquals(90.0, $info['remaining_gb']);
        $this->assertFalse($info['is_nearing_capacity']);
    }

    public function test_no_storage_update_for_documents_without_openai_project()
    {
        // Create test data without OpenAI project
        $user = User::factory()->create();
        $caseFile = CaseFile::factory()->create([
            'user_id' => $user->id,
            'openai_project_id' => null
        ]);

        $document = Document::factory()->create([
            'case_file_id' => $caseFile->id,
            'file_size' => 1024 * 1024 // 1MB
        ]);

        // This should not cause any errors and no storage should be tracked
        $this->assertTrue(true); // Test passes if no exceptions are thrown
    }
}
