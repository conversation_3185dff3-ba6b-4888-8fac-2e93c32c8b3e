<?php

namespace Tests\Feature;

use App\Models\CaseFile;
use App\Models\Draft;
use App\Models\User;
use App\Services\DocumentGenerationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DraftDocumentNameTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $caseFile;

    public function setUp(): void
    {
        parent::setUp();

        // Create a user
        $this->user = User::factory()->create();

        // Create a case file
        $this->caseFile = CaseFile::factory()->create([
            'user_id' => $this->user->id,
            'title' => 'Test Case',
            'initial_summary' => 'This is a test case for document name functionality',
            'case_types' => ['Civil Litigation'],
            'status' => 'open',
        ]);
    }

    /** @test */
    public function it_can_create_draft_with_document_name()
    {
        // First test direct model creation to ensure the field works
        $draft = Draft::create([
            'case_file_id' => $this->caseFile->id,
            'draft_type' => 'motion',
            'document_name' => 'Motion to Compel Discovery',
            'description' => 'A motion to compel the defendant to provide discovery responses',
        ]);

        $this->assertNotNull($draft);
        $this->assertEquals('Motion to Compel Discovery', $draft->document_name);

        // Clean up for the HTTP test
        $draft->delete();

        // Now test via HTTP
        $this->actingAs($this->user);

        $response = $this->post(route('case-files.drafts.store', $this->caseFile), [
            'draft_type' => 'motion',
            'document_name' => 'Motion to Compel Discovery',
            'description' => 'A motion to compel the defendant to provide discovery responses',
        ]);

        // Debug the response
        if ($response->status() !== 302) {
            dump('Response status: ' . $response->status());
            dump('Response content: ' . $response->getContent());
            dump('Session errors: ' . json_encode(session('errors')));
        }

        $response->assertRedirect();

        // Debug database state
        dump('Drafts in database: ' . \App\Models\Draft::count());
        dump('All drafts: ', \App\Models\Draft::all()->toArray());

        $this->assertDatabaseHas('drafts', [
            'case_file_id' => $this->caseFile->id,
            'draft_type' => 'motion',
            'document_name' => 'Motion to Compel Discovery',
            'description' => 'A motion to compel the defendant to provide discovery responses',
        ]);
    }

    /** @test */
    public function it_requires_document_name_when_creating_draft()
    {
        $this->actingAs($this->user);

        $response = $this->post(route('case-files.drafts.store', $this->caseFile), [
            'draft_type' => 'motion',
            'description' => 'A motion to compel the defendant to provide discovery responses',
            // Missing document_name
        ]);

        $response->assertSessionHasErrors(['document_name']);
    }

    /** @test */
    public function it_can_update_draft_with_document_name()
    {
        $draft = Draft::create([
            'case_file_id' => $this->caseFile->id,
            'draft_type' => 'motion',
            'document_name' => 'Original Document Name',
            'description' => 'Original description',
        ]);

        $this->actingAs($this->user);

        $response = $this->put(route('case-files.drafts.update', [$this->caseFile, $draft]), [
            'draft_type' => 'motion',
            'document_name' => 'Updated Document Name',
            'description' => 'Updated description',
        ]);

        $response->assertRedirect();
        
        $draft->refresh();
        $this->assertEquals('Updated Document Name', $draft->document_name);
        $this->assertEquals('Updated description', $draft->description);
    }

    /** @test */
    public function document_generation_service_uses_document_name_for_filename()
    {
        $draft = Draft::create([
            'case_file_id' => $this->caseFile->id,
            'draft_type' => 'motion',
            'document_name' => 'Motion to Compel Discovery',
            'description' => 'A very long description that would make a bad filename if used instead of the document name field',
            'sections_structure' => [
                [
                    'id' => 'title',
                    'name' => 'Title',
                    'content' => 'Motion to Compel Discovery',
                ],
            ],
        ]);

        $service = new DocumentGenerationService();
        
        // Use reflection to test the protected generateFilename method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('generateFilename');
        $method->setAccessible(true);
        
        $filename = $method->invoke($service, $draft);
        
        // The filename should be based on the document_name, not the description
        $this->assertStringContainsString('motion-to-compel-discovery', $filename);
        $this->assertStringNotContainsString('very-long-description', $filename);
        $this->assertStringEndsWith('.docx', $filename);
    }

    /** @test */
    public function document_generation_service_falls_back_to_description_when_no_document_name()
    {
        $draft = Draft::create([
            'case_file_id' => $this->caseFile->id,
            'draft_type' => 'motion',
            'document_name' => null, // No document name
            'description' => 'Motion to Compel Discovery',
            'sections_structure' => [
                [
                    'id' => 'title',
                    'name' => 'Title',
                    'content' => 'Motion to Compel Discovery',
                ],
            ],
        ]);

        $service = new DocumentGenerationService();
        
        // Use reflection to test the protected generateFilename method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('generateFilename');
        $method->setAccessible(true);
        
        $filename = $method->invoke($service, $draft);
        
        // The filename should fall back to description
        $this->assertStringContainsString('motion-to-compel-discovery', $filename);
        $this->assertStringEndsWith('.docx', $filename);
    }

    /** @test */
    public function document_generation_service_falls_back_to_draft_type_when_no_document_name_or_description()
    {
        $draft = Draft::create([
            'case_file_id' => $this->caseFile->id,
            'draft_type' => 'motion',
            'document_name' => null, // No document name
            'description' => null, // No description
            'sections_structure' => [
                [
                    'id' => 'title',
                    'name' => 'Title',
                    'content' => 'Motion to Compel Discovery',
                ],
            ],
        ]);

        $service = new DocumentGenerationService();
        
        // Use reflection to test the protected generateFilename method
        $reflection = new \ReflectionClass($service);
        $method = $reflection->getMethod('generateFilename');
        $method->setAccessible(true);
        
        $filename = $method->invoke($service, $draft);
        
        // The filename should fall back to draft_type
        $this->assertStringContainsString('motion', $filename);
        $this->assertStringEndsWith('.docx', $filename);
    }
}
